using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.B2b;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;

namespace Motivai.Users.Repository.Repositories.B2b {
    public class UsersB2bPortalRepository : IUsersB2bPortalRepository {
        private IMongoCollection<UserB2bPortal> _users { get; set; }
        public UsersB2bPortalRepository() {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            this._users = dataBase.GetCollection<UserB2bPortal>("UsersB2bPortal");
        }

        public async Task<UserB2bPortal> Save(UserB2bPortal user) {
            var opts = new UpdateOptions { IsUpsert = true };
            await _users.ReplaceOneAsync(x => x.Id == user.Id, user, opts);
            return user;
        }

        public async Task<List<UserB2bPortal>> Find(List<Guid> bus, string document, int skip, int limit) {
            var query = _users.AsQueryable();
            if (bus != null)
                query = query.Where(x => bus.Contains(x.BuId));
            if (!string.IsNullOrEmpty(document))
                query = query.Where(x => x.Document == document);

            return await query.Skip(skip).Take(limit).ToListAsync();
        }

        public async Task<UserB2bPortal> FindById(Guid id) {
            return await _users.AsQueryable().FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<UserB2bPortal> FindByLogin(string login) {
            return await _users.AsQueryable().FirstOrDefaultAsync(x => x.Login == login);
        }

        public async Task<UserB2bPortal> FindByDocument(string document) {
            return await _users.AsQueryable().FirstOrDefaultAsync(x => x.Document == document);
        }
    }
}