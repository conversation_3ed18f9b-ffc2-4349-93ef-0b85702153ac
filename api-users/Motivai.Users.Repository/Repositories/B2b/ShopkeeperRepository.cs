using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.B2b;
using Motivai.Users.Domain.IRepository.B2b;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositories.B2b {
    public class ShopkeeperRepository : IShopkeeperRepository {
        private IMongoCollection<Shopkeeper> shopkeepers { get; set; }
        public ShopkeeperRepository() {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            this.shopkeepers = dataBase.GetCollection<Shopkeeper>("Shopkeepers");
        }

        public async Task<bool> Save(Shopkeeper shopkeeper) {
            var opts = new UpdateOptions { IsUpsert = true };
            var saved = await shopkeepers.ReplaceOneAsync(x => x.Id == shopkeeper.Id, shopkeeper, opts);
            return true;
        }

        public async Task<Shopkeeper> FindByDocument(string document, List<Guid> bus) {
            var query = shopkeepers.AsQueryable();
            if (bus != null) {
                query = query.Where(x => bus.Contains(x.BuId));
            }

            query = query.Where(x => x.Document == document);
            return await query.FirstOrDefaultAsync();
        }

        public async Task<Shopkeeper> FindById(Guid id, List<Guid> bus) {
            var query = shopkeepers.AsQueryable();
            if (bus != null) {
                query = query.Where(x => bus.Contains(x.BuId));
            }

            query = query.Where(x => x.Id == id);
            return await query.FirstOrDefaultAsync();
        }

        public async Task<Shopkeeper> FindByUserId(Guid userId, List<Guid> bus) {
            var query = shopkeepers.AsQueryable();
            if (bus != null) {
                query = query.Where(x => bus.Contains(x.BuId));
            }

            query = query.Where(x => x.UserId == userId);
            return await query.FirstOrDefaultAsync();
        }

        public async Task<List<Shopkeeper>> Find(string document, string name, int skip, int limit, List<Guid> bus) {
            var query = shopkeepers.AsQueryable();
            if (!string.IsNullOrEmpty(document)) {
                query = query.Where(x => x.Document == document);
            }
            if (!string.IsNullOrEmpty(name)) {
                query = query.Where(x => x.Name.ToLower().Contains(name.ToLower()));
            }
            if (bus != null) {
                query = query.Where(x => bus.Contains(x.BuId));
            }

            return await query.Skip(skip).Take(limit).ToListAsync();
        }
    }
}