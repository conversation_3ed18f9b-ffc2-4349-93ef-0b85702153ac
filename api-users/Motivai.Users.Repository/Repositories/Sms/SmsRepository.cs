using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.Sms;

namespace Motivai.Users.Repository.Repositories.Sms
{
    public class SmsRepository : ISmsRepository
    {
        public async Task<bool> send(string from, string number, string text) {
            var request = new {
                from = from,
                to = number,
                text = text
            };

            var response = await HttpClient
                .Create(MotivaiApi.SmsService)
                .Path("sms/send")
                .Entity(request)
                .AsyncPost()
                .GetApiReturn<dynamic>();

            var sent = response.GetReturnOrError();
            if (sent == null) return false;

            return sent;
        }
    }
}