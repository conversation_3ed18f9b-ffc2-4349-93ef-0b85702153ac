using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Auth;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IRepository.ClientIntegrations;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using PreRegisteredUser = Motivai.SharedKernel.Domain.Entities.References.Users.PreRegisteredUser;

namespace Motivai.Users.Repository.Repositories.ClientsIntegrations
{
	public class ClientIntegrations : IClientIntegrations
	{
		public async Task<ParticipantInfo> GetParticipantInfo(Guid campaignId, string identifier)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.ClientsIntegrations)
				.Path("clients/campaigns").Path(campaignId).Path("users").Path(identifier)
				.AsyncGet()
				.GetApiReturn<ParticipantInfo>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o cliente através da integração.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<UserParticipantModel> AuthenticateUsingIntegratedPassword(Guid campaignId, ParticipantLoginModel login)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.ClientsIntegrations)
				.Path("clients/campaigns").Path(campaignId).Path("users/authentication")
				.Entity(login)
				.AsyncPost()
				.GetApiReturn<AuthenticationResult>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar login, por favor, tente novamente.");
			var result = apiReturn.GetReturnOrError();
			return UserParticipantModel.FromIntegrationResult(result);
		}

		public async Task<bool> SendPreRegister(Guid campaignId, PreRegisteredUser preRegisteredUser)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.ClientsIntegrations)
				.Path("campaigns").Path(campaignId).Path("users/preregistration")
				.Entity(preRegisteredUser)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar o pre cadastro na integracao");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateRegistrationData(Guid campaignId, PreRegisteredUser user)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.ClientsIntegrations)
				.Path("campaigns").Path(campaignId).Path("users/registration/update")
				.Entity(user)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar atualização cadastral na integração");
			return apiReturn.GetReturnOrError();
		}
	}
}