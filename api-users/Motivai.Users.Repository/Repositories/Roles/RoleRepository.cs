using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Roles;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositorys.Roles
{
    public class RoleRepository : IRoleRepository
    {
        private readonly IMongoCollection<Role> _roles;

        public RoleRepository()
        {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            _roles = dataBase.GetCollection<Role>("Roles");
        }

        public async Task<Role> Get(List<Guid> bus, Guid roleId)
        {
            var query = _roles.AsQueryable().Where(r => r.Id == roleId);
            if (bus != null)
                query = query.Where(x => bus.Contains(x.BuId));

            return await query.FirstOrDefaultAsync();
        }

        public async Task<List<Role>> GetRoles(List<Guid> bus, string name, int? skip, int? limit)
        {
            var query = this._roles.AsQueryable().Where(r => r.Active);
            if (bus != null)
                query = query.Where(x => bus.Contains(x.BuId));
            if (!string.IsNullOrEmpty(name))
                query = query.Where(x => x.Name.ToLower().Contains(name.ToLower()));
            if (skip.HasValue)
                query = query.Skip(skip.Value);
            if (limit.HasValue)
                query = query.Take(limit.Value);

            return await query
                .OrderBy(r => r.Name)
                .Select(r => new Role() { Id = r.Id, Name = r.Name, Active = r.Active})
                .ToListAsync();
        }

        public async Task Save(Role role)
        {
            var opts = new UpdateOptions
            {
                IsUpsert = true
            };
            if (role.Id == Guid.Empty)
                role.Active = true;
            role.StartValidation();
            await this._roles.ReplaceOneAsync(r => r.Id == role.Id, role, opts);
        }

        public async Task<bool> Delete(Guid roleId)
        {
            var role = await _roles.AsQueryable()
                .Where(r => r.Id == roleId).FirstOrDefaultAsync();
            if (role == null) return false;
            role.Active = false;
            await _roles.ReplaceOneAsync(r => r.Id == roleId, role);
            return !role.Active;
        }

        public async Task<Role> GetRoleByToken(string token)
        {
            return await _roles.AsQueryable().Where(x => x.Token == token).FirstOrDefaultAsync();
        }
    }
}