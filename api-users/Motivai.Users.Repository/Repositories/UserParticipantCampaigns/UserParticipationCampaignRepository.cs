﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Entities.Participants;

namespace Motivai.Users.Repository.Repositorys.UserParticipantCampaigns
{
	public class UserParticipationCampaignRepository : IUserParticipationCampaignRepository
	{
		private readonly IMongoCollection<User> _users;
		private readonly IUserRepository _userRepository;

		public UserParticipationCampaignRepository(IUserRepository userRepository)
		{
			var dataBase = MongoDataBaseHelper.GetMongoDatabase();
			_users = dataBase.GetCollection<User>("Users");
			_userRepository = userRepository;
		}

		public async Task<bool> UpdateParticipant(UserParticipantCampaign participant)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == participant.UserId && u.UsersParticipantCampaign.Any(p => p.Id == participant.Id));
			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1], participant)
				.CurrentDate(a => a.UpdateDate);
			var updateResult = await this._users.UpdateOneAsync(filter, updateDefinition);
			return MongoDataBaseHelper.WasUpdated(updateResult);
		}

		public Task<bool> IsUserActive(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Active && u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Active && p.CampaignId == campaignId))
				.AnyAsync();
		}

		public Task<User> GetUserByCampaignAndLogin(Guid campaignId, string login)
		{
			return _users.AsQueryable()
				.Where(upc => upc.UsersParticipantCampaign.Any(u => u.Login == login && u.CampaignId == campaignId && u.Active))
				.FirstOrDefaultAsync();
		}

		public Task<UserParticipantCampaign> Get(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Active))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId && p.Active))
				.FirstOrDefaultAsync();
		}

		public Task<UserParticipantCampaign> Get(Guid participantId)
		{
			return _users.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.Id == participantId && p.Active))
				.Select(u => u.UsersParticipantCampaign.First(p => p.Id == participantId))
				.FirstOrDefaultAsync();
		}

		public Task<Guid> GetParticipantId(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => p.Id)
				.FirstOrDefaultAsync();
		}

		public async Task<bool> UpdateParticipantBalance(Guid userId, Guid participantId, decimal balance)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Id == participantId));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].Balance, balance)
				.CurrentDate(c => c.UpdateDate);
			return MongoDataBaseHelper.WasUpdated(await _users.UpdateOneAsync(filter, updateDefinition));
		}

		public Task<Guid?> GetParticipantRanking(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.Select(p => p.RankingId)
				.FirstOrDefaultAsync();
		}

		public Task<bool> ExistCpfWithCampaign(string cpf, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Cpf == cpf && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.AnyAsync();
		}

		public Task<bool> ExistCnpjWithCampaign(string cnpj, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Cnpj == cnpj && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.AnyAsync();
		}

		public Task<ParticipantContact> GetContactInfo(Guid userId, Guid campaignId)
		{
			var documentCondition = new BsonDocument {
				{
					"$cond",
					new BsonDocument {
						{
							"if",
							new BsonDocument { { "$eq", new BsonArray { "$Type", PersonType.Fisica.ToString() } },
							}
						}, { "then", "$Cpf" }, { "else", "$Cnpj" }
					}
				}
			};

			var emailCondition = new BsonDocument {
				{
					"$ifNull",
					new BsonArray {
						"$UsersParticipantCampaign.Contact.MainEmail",
						new BsonDocument {
							{
								"$ifNull",
								new BsonArray {
									"$UsersParticipantCampaign.Contact.PersonalEmail",
									"$UsersParticipantCampaign.Contact.CommercialEmail"
								}
							}
						}
					}
				}
			};

			return this._users.Aggregate()
				.Match(new BsonDocument { { "_id", userId } })
				.Unwind("UsersParticipantCampaign")
				.Match(new BsonDocument { { "UsersParticipantCampaign.CampaignId", campaignId } })
				.Project(new BsonDocument { { "Name", "$Name" }, { "Document", documentCondition }, { "ParticipantId", "$UsersParticipantCampaign._id" }, { "PersonType", "$Type" }, { "Cpf", "$Cpf" }, { "Cnpj", "$Cnpj" }, { "Login", "$UsersParticipantCampaign.Login" }, { "Email", emailCondition }, { "MobilePhone", "$UsersParticipantCampaign.Contact.MobilePhone" }, { "Telephone", "$UsersParticipantCampaign.Contact.MainPhone" }, { "AuthenticationMfaSettings", "$UsersParticipantCampaign.AuthenticationMfaSettings" }, { "_id", 0 }
				}).As<ParticipantContact>()
				.FirstOrDefaultAsync();
		}

		public Task<MainContactInfo> GetMainContactInfo(Guid userId, Guid campaignId)
		{
			var documentCondition = new BsonDocument {
				{
					"$cond",
					new BsonDocument {
						{
							"if",
							new BsonDocument { { "$eq", new BsonArray { "$Type", PersonType.Fisica.ToString() } },
							}
						}, { "then", "$Cpf" }, { "else", "$Cnpj" }
					}
				}
			};

			var emailCondition = new BsonDocument {
				{
					"$ifNull",
					new BsonArray {
						"$UsersParticipantCampaign.Contact.MainEmail",
						new BsonDocument {
							{
								"$ifNull",
								new BsonArray {
									"$UsersParticipantCampaign.Contact.PersonalEmail",
									"$UsersParticipantCampaign.Contact.CommercialEmail"
								}
							}
						}
					}
				}
			};

			return this._users.Aggregate()
				.Match(new BsonDocument { { "_id", userId } })
				.Unwind("UsersParticipantCampaign")
				.Match(new BsonDocument { { "UsersParticipantCampaign.CampaignId", campaignId } })
				.Project(new BsonDocument { { "Name", "$Name" }, { "Document", documentCondition }, { "Email", emailCondition }, { "MobilePhone", "$UsersParticipantCampaign.Contact.MobilePhone" }, { "_id", 0 }
				}).As<MainContactInfo>()
				.FirstOrDefaultAsync();
		}

		public Task<List<ResumedAddress>> GetResumedAddresses(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Active))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.SelectMany(p => p.Addresses)
				.Where(a => a.Active)
				.Select(a => new ResumedAddress()
				{
					Id = a.Id,
					MainAddress = a.MainAddress,
					AddressName = a.AddressName,
					Cep = a.Cep
				})
				.ToListAsync();
		}

		public async Task<List<Address>> GetAddresses(Guid userId, Guid campaignId, bool? main = false)
		{
			var addresses = await _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Active))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.SelectMany(p => p.Addresses)
				.Where(a => a.Active)
				.ToListAsync();
			if (addresses == null || addresses.Count == 0)
				return null;
			if (main != null && main.HasValue && main.Value)
				addresses.RemoveAll(a => !a.MainAddress);
			return addresses;
		}

		public async Task<Address> GetMainAddress(Guid userId, Guid campaignId)
		{
			var addresses = await _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Active && p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.Active && p.CampaignId == campaignId))
				.SelectMany(p => p.Addresses)
				.Where(a => a.Active)
				.ToListAsync();
			if (addresses == null || addresses == null || addresses.Count == 0) return null;
			var mainAddress = addresses.FirstOrDefault(a => a.Active && a.MainAddress);
			if (mainAddress == null)
				mainAddress = addresses.FirstOrDefault(a => a.Active);
			return mainAddress;
		}

		public async Task<List<Address>> GetAllAddresses(Guid userId, Guid campaignId, bool? main = false)
		{
			var addresses = await _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Active))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.SelectMany(p => p.Addresses)
				.ToListAsync();

			if (addresses == null || addresses.Count == 0)
				return null;

			if (main != null && main.HasValue && main.Value)
				addresses.RemoveAll(a => !a.MainAddress);

			return addresses;

		}

		public Task<Address> GetAddressById(Guid userId, Guid campaignId, Guid addressId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Active && p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.SelectMany(p => p.Addresses)
				.Where(a => a.Id == addressId)
				.FirstOrDefaultAsync();
		}

		public Task<string> GetAddressCep(Guid userId, Guid campaignId, Guid addressId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Active && p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.SelectMany(p => p.Addresses)
				.Where(a => a.Id == addressId)
				.Select(a => a.Cep)
				.FirstOrDefaultAsync();
		}

		public async Task<bool> DeleteAddress(Guid userId, Guid participantId, Address address)
		{
			return await this._userRepository.UpdateAddress(userId, participantId, address);
		}

		public async Task<bool> UpdateAddress(Address adress)
		{
			var user = await _users.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(upc => upc.Addresses.Any(a => a.Id == adress.Id)))
				.FirstOrDefaultAsync();
			if (user == null) return false;
			var userParticipantCampaign = user.UsersParticipantCampaign
				.Where(upc => upc.Addresses.Any(a => a.Id == adress.Id))
				.FirstOrDefault();
			if (userParticipantCampaign == null)
				return false;
			var _adress = userParticipantCampaign.Addresses.FirstOrDefault(a => a.Id == adress.Id);
			userParticipantCampaign.Addresses.Remove(_adress);
			userParticipantCampaign.Addresses.Add(adress);
			user.Validate();
			await _userRepository.Save(user);
			return true;
		}

		public async Task<List<string>> GetDistinctEmployee(Guid campaignId, string propertieEmployee)
		{
			var filter = Builders<User>.Filter.Where(u => u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var propertySelect = string.Format("UsersParticipantCampaign.Employee.{0}", propertieEmployee);
			var values = await _users.DistinctAsync(new StringFieldDefinition<User, string>(propertySelect), filter);
			return values.ToList();
		}

		public Task<UserParticipantCampaign> GetParticipantByCampaignEmailAndLogin(Guid campaignId, string email, string login)
		{
			return _users.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Contact.MainEmail == email && p.Login == login && p.Active))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId && p.Contact.MainEmail == email && p.Login == login && p.Active))
				.FirstOrDefaultAsync();
		}

		public Task<bool> ExistParticipantWithDocument(Guid campaignId, Guid userId, string userDocument)
		{
			return _users.AsQueryable()
				.Where(u => u.Id != userId && (u.Cpf == userDocument || u.Cnpj == userDocument) && u.UsersParticipantCampaign.Any(c => c.CampaignId == campaignId))
				.AnyAsync();
		}

		public Task ChangeParticipantsParent(Guid currentParentId, Guid newParentId, UserBasicInfo newParentDetails)
		{
			var filter = Builders<User>.Filter.Where(u => u.UsersParticipantCampaign.Any(p => p.ParentUserId == currentParentId));
			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1].ParentUserId, newParentId)
				.Set(a => a.UsersParticipantCampaign[-1].ParentUserDetails, newParentDetails)
				.CurrentDate(a => a.UpdateDate);
			return this._users.UpdateOneAsync(filter, updateDefinition);
		}

		#region Participante Pai

		public Task<List<UserParentDetails>> GetUsersParentsByIds(Guid campaignId, List<Guid> usersIds)
		{
			return _users.AsQueryable()
				.Where(u => usersIds.Contains(u.Id) && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => new UserParentDetails()
				{
					UserId = p.UserId,
					ParentUserId = p.ParentUserId,
					ParentDetails = p.ParentUserDetails
				})
				.ToListAsync();
		}

		public Task<UserParentDetails> GetUserParent(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => new UserParentDetails()
				{
					UserId = p.UserId,
					ParentUserId = p.ParentUserId,
					ParentDetails = p.ParentUserDetails
				})
				.FirstOrDefaultAsync();
		}

		public Task<List<UserBasicInfo>> FindUsersWithSameParent(Guid campaignId, Guid parentUserId)
		{
			return _users.AsQueryable()
				.Where(u => u.Active && u.UsersParticipantCampaign.Any(p => p.Active
					&& p.CampaignId == campaignId
					&& (p.UserId == parentUserId
						|| p.ParentUserId == parentUserId
						|| p.ParentUserDetails.UserId == parentUserId)
						)
				).Select(u => new UserBasicInfo()
				{
					UserId = u.Id,
					CampaignId = campaignId,
					Active = u.Active,
					Name = u.Name,
					Cpf = u.Cpf,
					Cnpj = u.Cnpj,
					ParticipantId = u.UsersParticipantCampaign
						.Where(a => a.CampaignId == campaignId)
						.Select(p => p.Id)
						.First()
				}).ToListAsync();
		}

		public Task<bool> UpdateParticipantParent(Guid campaignId, UserParentDetails userParent)
		{
			return this.UpdateParticipantParent(userParent.UserId, campaignId, userParent.ParentDetails);
		}

		public async Task<bool> UpdateParticipantParent(Guid userId, Guid campaignId, UserBasicInfo parent)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId
				&& u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1].ParentUserId, parent.UserId)
				.Set(a => a.UsersParticipantCampaign[-1].ParentUserDetails, parent)
				.CurrentDate(a => a.UpdateDate);
			return MongoDataBaseHelper.WasUpdated(await this._users.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateUserContactById(Guid campaignId, Guid userId, Contact contact)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId
				&& u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1].Contact.MainEmail, contact.MainEmail)
				.Set(a => a.UsersParticipantCampaign[-1].Contact.MobilePhone, contact.MobilePhone)
				.Set(a => a.UsersParticipantCampaign[-1].Contact.MainEmailLastUpdateDate, DateTime.UtcNow)
				.Set(a => a.UsersParticipantCampaign[-1].Contact.MobilePhoneLastUpdateDate, DateTime.UtcNow)
				.CurrentDate(a => a.UpdateDate);

			return MongoDataBaseHelper.WasUpdated(await this._users.UpdateOneAsync(filter, updateDefinition));
		}

		public Task<UserBasicInfo> GetParentUser(Guid userId, Guid campaignId)
		{
			return _users.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => p.ParentUserDetails)
				.FirstOrDefaultAsync();
		}

		#endregion
	}
}