using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.Terms;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositories.UserParticipantCampaigns
{
	public class CampaignTermsAcceptanceRepository : ICampaignTermsAcceptanceRepository
	{
		private readonly IMongoCollection<TermsAcceptance> termsAcceptance;

		public CampaignTermsAcceptanceRepository()
		{
			this.termsAcceptance = MongoDataBaseHelper.GetMongoDatabase()
				.GetCollection<TermsAcceptance>("UsersTermsAcceptance");
		}

		public Task RegisterAcceptance(TermsAcceptance acceptance)
		{
			return termsAcceptance.InsertOneAsync(acceptance);
		}

		public Task<bool> HasAcceptedAnyTerm(Guid userId, Guid campaignId)
		{
			return this.termsAcceptance.AsQueryable()
				.Where(t => t.CampaignId == campaignId && t.UserId == userId)
				.AnyAsync();
		}
	}
}