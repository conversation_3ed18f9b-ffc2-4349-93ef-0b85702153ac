﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.Models.Transactions;

namespace Motivai.Users.Repository.Repositorys.Transactions {
	public class TransactionApiRepository : ITransactionApiRepository {
		public async Task<decimal> GetBalance(Guid campaignId, Guid userId) {
			// LoggerHelper.GetLogger().Info("{date} - Users - Transaction Repository Start - {methodName}", DateTime.UtcNow, nameof(GetBalance));
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("balance")
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o saldo do participante.");
			if (apiReturn.HasNullReturn())
				return 0;
			// LoggerHelper.GetLogger().Info("{date} - Users - Transaction Repository End - {methodName}", DateTime.UtcNow, nameof(GetBalance));
			return apiReturn.GetReturnOrError();
		}

		public async Task<decimal> GetBalanceByRankings(Guid campaignId, Guid userId, List<Guid> rankings) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				// .Path("transactions/participants").Path(campaignId).Path(participantId).Path("balance/rankings")
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("balance/rankings")
				.Query("rankings", string.Join(",", rankings))
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o saldo do participante.");
			if (apiReturn.HasNullReturn())
				return 0;
			return apiReturn.GetReturnOrError();
		}

		public async Task<decimal> GetReservedBalanceFor(Guid userId, Guid campaignId, Product product) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("reservedbalance")
				.Query("partnerId", product.PartnerId)
				.Query("productId", product.ProductId)
				.Query("skuId", product.SkuId)
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o saldo do participante.");
			if (apiReturn.HasNullReturn())
				return 0;
			return apiReturn.GetReturnOrError();
		}

		public async Task<PointsSummaryModel> GetFirstPointsToExpire(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("expiringpoints/first")
				.AsyncGet()
				.GetApiReturn<Dictionary<decimal, DateTime>>();
			if (apiReturn == null) return null;

			var mechanic = apiReturn.GetReturnOrError();
			if (mechanic == null || mechanic.Keys.Count <= 0) return null;
			var firstKey = mechanic.Keys.ElementAtOrDefault(0);
			return new PointsSummaryModel() {
				Points = firstKey,
					Date = mechanic[firstKey]
			};
		}

		public async Task<ExpiringPointsSummary> GetExpiringPoints(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				// .Path("transactions/participants").Path(userParticipantId).Path("expiringpoints")
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("mechanics/summary")
				.AsyncGet()
				.GetApiReturn<ExpiringPointsSummary>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<PointsSummaryModel> GetFirstPointsToUnblock(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("blockedpoints/first")
				.AsyncGet()
				.GetApiReturn<Dictionary<decimal, DateTime>>();
			if (apiReturn == null) return null;

			var mechanic = apiReturn.GetReturnOrError();
			if (mechanic == null || mechanic.Keys.Count <= 0) return null;
			var firstKey = mechanic.Keys.ElementAtOrDefault(0);
			return new PointsSummaryModel() {
				Points = firstKey,
					Date = mechanic[firstKey]
			};
		}

		public async Task<BlockedPointsSummary> GetPointsBlocked(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("blockedpoints")
				.AsyncGet()
				.GetApiReturn<BlockedPointsSummary>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<TransactionDetailsModel>> GetLastAccumulations(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("accumulations")
				.AsyncGet()
				.GetApiReturn<List<TransactionDetailsModel>>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<TransactionDetailsModel>> GetLastRedemptions(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("redeems")
				.AsyncGet()
				.GetApiReturn<List<TransactionDetailsModel>>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<TransactionsExtractModel> GetExtract(Guid campaignId, Guid userId, Guid? participantId,
				TransactionType? transactionType, TransactionOrigin? transactionOrigin,
				DateTime? startDate, DateTime? endDate, int? skip, int? limit) {
			var httpClient = HttpClient
				.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("summary")
				.Query("participantId", participantId)
				.QueryDate("startDate", startDate)
				.QueryDate("endDate", endDate)
				.Query("skip", skip)
				.Query("limit", limit);

			if (transactionType.HasValue)
				httpClient.Query("transactionType", transactionType.Value.ToString());
			if (transactionOrigin.HasValue)
				httpClient.Query("transactionOrigin", transactionOrigin.Value.ToString());

			var apiReturn = await httpClient.AsyncGet()
				.GetApiReturn<TransactionsExtractModel>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<TransactionsExtractModel> GetExtractWithBlockedTransactions(Guid campaignId, Guid userId, Guid? participantId,
				TransactionType? transactionType, TransactionOrigin? transactionOrigin,
				DateTime? startDate, DateTime? endDate, int? skip, int? limit) {
			var httpClient = HttpClient
				.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("summary/with-blocked-transactions")
				.Query("participantId", participantId)
				.QueryDate("startDate", startDate)
				.QueryDate("endDate", endDate)
				.Query("skip", skip)
				.Query("limit", limit);

			if (transactionType.HasValue)
				httpClient.Query("transactionType", transactionType.Value.ToString());
			if (transactionOrigin.HasValue)
				httpClient.Query("transactionOrigin", transactionOrigin.Value.ToString());

			var apiReturn = await httpClient.AsyncGet()
				.GetApiReturn<TransactionsExtractModel>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<TransactionsExtractModel> GetBlockedTransactionsExtract(Guid campaignId, Guid userId, Guid? participantId,
				TransactionType? transactionType, TransactionOrigin? transactionOrigin,
				DateTime? startDate, DateTime? endDate, int? skip, int? limit) {
			var httpClient = HttpClient
				.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("summary/only-blocked-transactions")
				.Query("participantId", participantId)
				.QueryDate("startDate", startDate)
				.QueryDate("endDate", endDate)
				.Query("skip", skip)
				.Query("limit", limit);

			if (transactionType.HasValue)
				httpClient.Query("transactionType", transactionType.Value.ToString());
			if (transactionOrigin.HasValue)
				httpClient.Query("transactionOrigin", transactionOrigin.Value.ToString());

			var apiReturn = await httpClient.AsyncGet()
				.GetApiReturn<TransactionsExtractModel>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}
	}
}