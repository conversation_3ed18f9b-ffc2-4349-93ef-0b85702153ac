using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.ExtraServices;
using Motivai.Users.Domain.Models.MyAccount;

namespace Motivai.Users.Repository.Repositories.ExtraServices {
    public class CardRepository : ICardRepository {
        public async Task<List<ResumedCardOrder>> GetOrdersByParticipant(Guid campaignId, Guid userId, string status, DateTime? startDate, DateTime? endDate) {
            var httpClient = HttpClient.Create(MotivaiApi.PrepaidCards)
                .Path("cards/prepaid/orders/campaigns").Path(campaignId)
                .Path("users").Path(userId);

            if (startDate.HasValue)
                httpClient.QueryDate("startDate", startDate);
            if (endDate.HasValue)
                httpClient.QueryDate("endDate", endDate);
            if (!string.IsNullOrEmpty(status))
                httpClient.Query("status", status);

            var apiReturn = await httpClient.AsyncGet()
                .GetApiReturn<List<ResumedCardOrder>>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }
    }
}