using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums.Orders;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.ExtraServices;
using Motivai.Users.Domain.Models.MyAccount;

namespace Motivai.Users.Repository.Repositories.ExtraServices {
    public class CampaignExtraServicesRepository : ICampaignExtraServicesRepository {
		public async Task<List<ResumedExtraServiceOrder>> GetExtraServicesOrdersByParticipant(Guid campaignId, Guid userId, string status, DateTime? startDate, DateTime? endDate) {
            var httpClient = HttpClient
                .Create(MotivaiApi.CampaignsExtraServices)
                .Path("campaigns").Path(campaignId).Path("services")
                .Path("users").Path(userId)
                .Path("orders")
                .Query("status",status)
                .QueryDate("startDate", startDate)
                .QueryDate("endDate", endDate);
            var apiReturn = await httpClient.AsyncGet()
                .GetApiReturn<List<ResumedExtraServiceOrder>>();
            if (apiReturn == null) return null;
            return apiReturn.GetReturnOrError();
        }
    }
}