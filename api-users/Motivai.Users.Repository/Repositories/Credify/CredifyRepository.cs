using System;
using System.Globalization;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.IRepository.Credify;
using Motivai.Users.Domain.Models.Credify;

namespace Motivai.Users.Repository.Repositories.Credify
{
	public class CredifyRepository : ICredifyRepository
	{
		public async Task<Person> QueryDocument(PersonType personType, string document)
		{
			try
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Credify)
					.Path("credify/document").Path(document)
					.AsyncGet()
					.Timeout(10000)
					.GetApiReturn<dynamic>();
				if (apiReturn == null || apiReturn.HasNullReturn())
					return null;
				var personData = apiReturn.GetReturnOrError();

				DateTime? birthDate = default(DateTime?);
				try
				{
					birthDate = Convert.ToDateTime((string)personData.born, new CultureInfo("pt-br"));
				}
				catch
				{
					birthDate = null;
				}

				var size = personData.document?.ToString().Length > 11 ? 14 : 11;

				return new Person
				{
					Name = personData.name == null ? null : TextHelper.ToTitleCase(personData.name.ToString()),
					Type = personType,
					Document = personData.document?.ToString().PadLeft(size, '0'),
					BirthDate = birthDate == DateTime.MinValue ? (DateTime?)null : birthDate,
					Gender = personData.gender,
					MotherName = personData.motherName,
					Sign = personData.sign,
					Age = personData.age,
					MainAddress = personData.mainAddress,
					CompanyName = personData.companyName,
					NatureDescription = personData.natureDescription,
					NatureCode = personData.natureCode
				};
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Credify - Query", "Ocorreu um erro ao efetuar consulta na API Credify.");
				return null;
			}
		}
	}
}