using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Correios;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.Correios;

namespace Motivai.Users.Repository.Repositories.Correios {
    public class CorreiosRepository : ICorreiosRepository {
        public async Task<CorreiosAddress> QueryCep(string cep) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Correios)
                .Path("addresses").Path(cep)
                .AsyncGet()
                .GetApiReturn<CorreiosAddress>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }
    }
}