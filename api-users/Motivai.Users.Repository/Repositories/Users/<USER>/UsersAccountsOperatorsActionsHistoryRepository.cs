using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.AccountOperators.Actions;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using MongoDB.Driver;

namespace Motivai.Users.Repository.Repositories.Users.AccountOperators {

    public class UsersAccountsOperatorsActionsHistoryRepository : IUsersAccountsOperatorsActionsHistoryRepository {
        private readonly IMongoCollection<UsersAccountsOperatorsDataActionsHistory> accountsOperatorsActionsHistory;

        public UsersAccountsOperatorsActionsHistoryRepository() {
            this.accountsOperatorsActionsHistory = MongoDataBaseHelper.GetMongoDatabase()
                .GetCollection<UsersAccountsOperatorsDataActionsHistory>("UsersAccountsOperatorsDataActionsHistories");
        }

        public async Task Save(UsersAccountsOperatorsDataActionsHistory usersAccountsOperatorsDataActionsHistory) {
            await this.accountsOperatorsActionsHistory.InsertOneAsync(usersAccountsOperatorsDataActionsHistory);
        }
    }
}