using System;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Configurations.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.Users.Domain.Entities.Wallets.Devices;

namespace Motivai.Users.Repository.Repositories.Users
{
    public class UserConfigurationRepository : IUserConfigurationRepository {
        private readonly IMongoCollection<User> colletion;

        public UserConfigurationRepository() {
            this.colletion = MongoDataBaseHelper.GetMongoDatabase()
                .GetCollection<User>("Users");
        }

        public async Task SaveSecurityCode(Guid userId, GeneratedSecurityCode generatedCode) {
            var update = Builders<User>.Update
                .Set(u => u.Configurations.GeneratedSecurityCode, generatedCode);
            var result = await colletion.UpdateOneAsync(u => u.Id == userId, update);
            if (!MongoDataBaseHelper.WasUpdated(result)) {
                throw MotivaiException.ofValidation("Não foi possível salvar o código de segurança.");
            }
        }

        public async Task InvalidateSecurityCode(Guid userId) {
            var update = Builders<User>.Update
                .Unset(u => u.Configurations.GeneratedSecurityCode);
            await colletion.UpdateOneAsync(u => u.Id == userId, update);
        }

        public async Task<GeneratedSecurityCode> GetSecurityCodeForValidation(Guid userId) {
            return await this.colletion.AsQueryable()
                .Where(u => u.Active && u.Id == userId)
                .Select(u => u.Configurations.GeneratedSecurityCode)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> HasTransactionalPasswordConfigured(Guid userId) {
            return await this.colletion.AsQueryable()
                .Where(u => u.Active && u.Id == userId)
                .Select(u => u.Wallet.Transactional.Configured)
                .FirstOrDefaultAsync();
        }

        public async Task<string> GetTransactionalPassword(Guid userId) {
            return await this.colletion.AsQueryable()
                .Where(u => u.Active && u.Id == userId)
                .Select(u => u.Wallet.Transactional.Password)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> ConfigureTransactionalPassword(Guid userId, TransactionalPassword transactionalPassword) {
            var update = Builders<User>.Update
                .Set(u => u.Wallet.Transactional.Password, transactionalPassword.Password)
                .Set(u => u.Wallet.Transactional.ConfigurationDevice, transactionalPassword.DeviceRequestInfo)
                .Set(u => u.Wallet.Transactional.Configured, true)
                .CurrentDate(u => u.Wallet.Transactional.ConfigurationDate);
            var result = await colletion.UpdateOneAsync(u => u.Id == userId, update);
            return MongoDataBaseHelper.WasUpdated(result);
        }

        public async Task<bool> CreateRegisteredDevice(Guid userId, RegisteredDevice registeredDevice) {
            var update = Builders<User>.Update
                .Push(u => u.Wallet.AuthorizedDevices, registeredDevice);
            var result = await colletion.UpdateOneAsync(u => u.Id == userId, update);
            return MongoDataBaseHelper.WasUpdated(result);
        }

        public async Task<RegisteredDevice> GetDeviceById(Guid userId, string deviceId) {
            return await colletion.AsQueryable()
                .Where(a => a.Id == userId)
                .Select(b => b.Wallet.AuthorizedDevices
                    .First(c => c.Device.DeviceId == deviceId))
                .FirstOrDefaultAsync();
        }

        public async Task<RegisteredDevice> GetActiveDevice(Guid userId, string deviceId) {
            return await colletion.AsQueryable()
                .Where(a => a.Id == userId)
                .Select(b => b.Wallet.AuthorizedDevices
                    .First(c => c.Device.DeviceId == deviceId && c.Active))
                .FirstOrDefaultAsync();
        }

        public async Task<bool> VerifyAuthorization(Guid userId, DeviceRequestInfo deviceRequestInfo) {
            return await colletion.AsQueryable()
                .Where(u => u.Id == userId && u.Wallet.AuthorizedDevices.Any(d => d.Active && d.Status == DeviceStatus.Authorized
                    && d.Device.DeviceId == deviceRequestInfo.Device.DeviceId))
                .AnyAsync();
        }
    }
}