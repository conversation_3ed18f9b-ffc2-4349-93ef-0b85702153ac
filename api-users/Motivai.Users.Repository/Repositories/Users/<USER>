using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Users;
using MongoDB.Driver;

namespace Motivai.Users.Repository.Repositories.Users {
    public class UsersCallcenterActionRegisterRepository : IUsersCallcenterActionRegisterRepository {
        private readonly IMongoCollection<UsersCallcenterAction> colletion;

        public UsersCallcenterActionRegisterRepository() {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            colletion = dataBase.GetCollection<UsersCallcenterAction>("UsersCallcenterActionsLog");
        }
        public async Task Save(UsersCallcenterAction usersCallcenterActionRegister) {
            var opts = new UpdateOptions
            {
                IsUpsert = true
            };
            await colletion.InsertOneAsync(usersCallcenterActionRegister);
        }
    }
}