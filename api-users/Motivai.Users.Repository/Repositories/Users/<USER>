using System;
using System.Threading.Tasks;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.Repository.Repositories.Users {
	public class UserMetadataValueRepository : IUserMetadataValueRepository {
		private readonly IMongoCollection<UserMetadataValue> collection;

		public UserMetadataValueRepository() {
			var dataBase = MongoDataBaseHelper.GetMongoDatabase();
			collection = dataBase.GetCollection<UserMetadataValue>("UsersMetadataValue");
		}

		public Task<UserMetadataValue> GetUsersMetadataValue(Guid campaignId, Guid userId) {
			return collection.AsQueryable()
				.Where(u => u.CampaignId == campaignId && u.UserId == userId)
				.FirstOrDefaultAsync();
		}

		public Task<string> GetUserMetadataFieldValue(Guid userId, Guid campaignId, string fieldKey) {
			return collection.AsQueryable()
				.Where(u => u.UserId == userId && u.CampaignId == campaignId)
				.Select(u => u.Metadata[fieldKey])
				.FirstOrDefaultAsync();
		}

		public Task CreateUserMetadata(UserMetadataValue userMetadata) {
			return collection.InsertOneAsync(userMetadata);
		}

		public async Task<bool> SaveUserMetadata(Guid userId, Guid campaignId, UserMetadataValue userMetadata) {
			var opts = new UpdateOptions {
				IsUpsert = true
			};
			var result = await collection.ReplaceOneAsync(x => x.UserId == userId && x.CampaignId == campaignId, userMetadata, opts);
			return result.IsAcknowledged && result.ModifiedCount > 0;
		}
	}
}