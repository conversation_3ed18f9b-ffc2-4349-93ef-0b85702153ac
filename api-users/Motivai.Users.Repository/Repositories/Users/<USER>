﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.CreditCards;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Models.PrivacyPolicy;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositorys.Users
{
    public class UserRepository : IUserRepository
	{
		private readonly IMongoCollection<User> colletion;

		public UserRepository()
		{
			var dataBase = MongoDataBaseHelper.GetMongoDatabase();
			colletion = dataBase.GetCollection<User>("Users");
		}

		private FilterDefinition<User> BuildFilterByUserIdAndCampaignId(Guid userId, Guid campaignId)
		{
			return Builders<User>.Filter.Where(u =>
				u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId)
			);
		}

		public async Task<User> Get(Guid userId)
		{
			return await colletion.AsQueryable().FirstOrDefaultAsync(u => u.Id == userId);
		}

		public async Task<User> GetUserAndParticipant(Guid userId, Guid campaignId)
		{
			var filter = Builders<User>.Filter.Eq(u => u.Id, userId) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId)
				);

			return await colletion.Find(filter)
				.Project(ProjectUserAndParticipant(campaignId))
				.FirstOrDefaultAsync();
		}

		private Expression<Func<User, User>> ProjectUserAndParticipant(Guid campaignId)
		{
			return u => new User
			{
				Id = u.Id,
				Active = u.Active,
				Blocked = u.Blocked,
				Name = u.Name,
				CompanyName = u.CompanyName,
				Type = u.Type,
				Cpf = u.Cpf,
				Cnpj = u.Cnpj,
				Rg = u.Rg,
				Gender = u.Gender,
				MaritalStatus = u.MaritalStatus,
				BirthDate = u.BirthDate,
				GpInf = u.GpInf,
				GpPartnerInf = u.GpPartnerInf,
				StateInscriptionExempt = u.StateInscriptionExempt,
				StateInscription = u.StateInscription,
				StateInscriptionUf = u.StateInscriptionUf,
				PhotoUrl = u.PhotoUrl,
				UsersParticipantCampaign = u.UsersParticipantCampaign
					.Where(p => p.CampaignId == campaignId)
					// .Select(p => new UserParticipantCampaign()
					// {
					// 	Id = p.Id,
					// 	CampaignId = p.CampaignId,
					// 	Active = p.Active,
					// 	LastAccessDate = p.LastAccessDate,
					// 	Contact = p.Contact,
					// 	Blocked = p.Blocked,
					// 	AllowLoginSite = p.AllowLoginSite,
					// 	FirstAccess = p.FirstAccess,
					// 	LoginAttempts = p.LoginAttempts
					// })
					.ToList()
			};
		}

		public async Task<User> GetUserForSession(Guid userId, Guid campaignId)
		{
			var filter = Builders<User>.Filter.Eq(u => u.Active, true) &
				Builders<User>.Filter.Eq(u => u.Id, userId) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId)
				);

			return await colletion.Find(filter)
				.Project(ProjectUserAndParticipant(campaignId))
				.FirstOrDefaultAsync();
		}

		public async Task<bool> IsActive(Guid userId)
		{
			return await colletion.AsQueryable()
				.Where(u => u.Id == userId && u.Active)
				.AnyAsync();
		}

		public async Task<User> GetUserByCampaignAndLogin(Guid campaignId, string login)
		{
			var filter = Builders<User>.Filter.Eq(u => u.Active, true) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Login, login)
				);

			return await colletion.Find(filter)
				.Project(u => new User
				{
					Id = u.Id,
					Active = u.Active,
					Name = u.Name,
					CompanyName = u.CompanyName,
					Type = u.Type,
					Cpf = u.Cpf,
					Cnpj = u.Cnpj,
					Blocked = u.Blocked,
					UsersParticipantCampaign = u.UsersParticipantCampaign
					   .Select(p => new UserParticipantCampaign()
					   {
						   Id = p.Id,
						   CampaignId = p.CampaignId,
						   Active = p.Active,
						   LastAccessDate = p.LastAccessDate,
						   Password = p.Password,
						   Contact = p.Contact,
						   Blocked = p.Blocked,
						   AllowLoginSite = p.AllowLoginSite,
						   FirstAccess = p.FirstAccess,
						   LoginAttempts = p.LoginAttempts,
						   AuthenticationMfaSettings = p.AuthenticationMfaSettings,
						   PrivacySettings = p.PrivacySettings
					   }).ToList()
				})
				.FirstOrDefaultAsync();
		}

		public async Task<User> AuthenticateByCampaignLoginAndPassword(Guid campaignId, string login, string password)
		{
			var pwd = new Senha(password, password);

			var filter = Builders<User>.Filter.Eq(u => u.Active, true) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Login, login) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Password.Descricao, pwd.Descricao)
				);

			return await colletion.Find(filter)
				.Project(ProjectUserAndParticipant(campaignId))
				.FirstOrDefaultAsync();
		}

		public async Task<User> AuthenticateByDocumentAndCampaignPassword(string document, Guid campaignId, string password)
		{
			var personType = PersonTypeHelper.FromDocument(document);
			var pwd = new Senha(password, password);

			var filter = FilterDefinition<User>.Empty;
			if (personType == PersonType.Fisica)
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Cpf, document);
			}
			else
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Cnpj, document);
			}
			filter = filter & Builders<User>.Filter.Eq(u => u.Active, true) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Password.Descricao, pwd.Descricao)
				);

			return await colletion.Find(filter)
				.Project(ProjectUserAndParticipant(campaignId))
				.FirstOrDefaultAsync();
		}

		public async Task<UserCampaigns> GetUserCampaignsByDocument(PersonType type, string document, bool onlyActive = true)
		{
			var filter = FilterDefinition<User>.Empty;
			if (type == PersonType.Fisica)
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Cpf, document);
			}
			else
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Cnpj, document);
			}
			if (onlyActive)
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Active, true);
			}
			return await colletion.Find(filter)
				.Project(u => new UserCampaigns
				{
					userId = u.Id,
					Active = u.Active,
					Name = u.Name,
					Document = document,
					Campaigns = u.UsersParticipantCampaign
					   .Select(p => new CampaignData()
					   {
						   Id = p.CampaignId,
						   Active = p.Active,
						   Login = p.Login
					   }).ToList()
				})
				.FirstOrDefaultAsync();
		}

		///<summary>
		/// Depreceado.
		/// Será removido para utilizar os métodos de atualizações individuais.
		///</summary>
		public async Task Save(User user, bool skipValidation = false)
		{
			if (!skipValidation)
			{
				user.StartValidation();
			}

			var opts = new UpdateOptions
			{
				IsUpsert = true
			};
			await colletion.ReplaceOneAsync(x => x.Id == user.Id, user, opts);
		}

        public async Task CreateUser(User user, bool skipValidation = false) {
            if (!skipValidation) {
                user.StartValidation();
            }
			if (!string.IsNullOrEmpty(user.GetDocument()))
			{
				if (await ExistUserWithDocument(user.Id, user.Document))
					throw MotivaiException.ofValidation("Já existe CPF/CNPJ cadastrado");
			}
            await colletion.InsertOneAsync(user);
        }

        public async Task<bool> CreateParticipant(Guid userId, UserParticipantCampaign participant) {
            var filter = Builders<User>.Filter.Where(u => u.Id == userId);
            var update = Builders<User>.Update.Push("UsersParticipantCampaign", participant);
            return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, update));
        }

		public async Task<bool> SetUpdateDateToNow(Guid userId, Guid campaignId)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var update = Builders<User>.Update.Set("UpdateDate", DateTime.UtcNow);
			var updateDefinition = Builders<User>.Update
				.Set(u => u.UpdateDate, DateTime.UtcNow)
				.Set(u => u.UsersParticipantCampaign[-1].UpdateDate, DateTime.UtcNow);
			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, update));
		}

		public async Task<bool> UpdateRegistrationData(User user, UserParticipantCampaign participant, bool skipValidation = false,
			bool isFromAdmin = false) {
			if (!skipValidation) {
				user.StartValidation();
			}
			var filter = Builders<User>.Filter.Where(u => u.Id == user.Id && u.UsersParticipantCampaign.Any(p => p.Id == participant.Id));

			var updateDefinition = BuildUpdateDefinitionForRegistrationData(user, participant, isFromAdmin);

			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateContactInfo(Guid campaignId, Guid userId, UserParticipantDataModel participantData) {

			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			var updateDefinition = Builders<User>.Update
				.Set(c => c.Name, participantData.Name)
				.Set(c => c.UsersParticipantCampaign[-1].Contact.MainEmail, participantData.Contact.MainEmail)
				.Set(c => c.UsersParticipantCampaign[-1].Contact.MainPhone, participantData.Contact.MainPhone)
				.Set(c => c.UsersParticipantCampaign[-1].Contact.MobilePhone, participantData.Contact.MobilePhone);



			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateFirstAccessData(User user, UserParticipantCampaign participant, ParticipantData firstAccessSettings) {
			user.StartValidation();
			var filter = Builders<User>.Filter.Where(u => u.Id == user.Id && u.UsersParticipantCampaign.Any(p => p.Id == participant.Id));

			var updateDefinition = BuildUpdateDefinitionForRegistrationData(user, participant, false);

			if (firstAccessSettings.EnableHomeAddress || firstAccessSettings.EnableBusinessAddress)
			{
				updateDefinition = updateDefinition.Set(c => c.UsersParticipantCampaign[-1].Addresses, participant.Addresses);
			}

			if (firstAccessSettings.EnablePassword)
			{
				updateDefinition = updateDefinition.Set(c => c.UsersParticipantCampaign[-1].Password, participant.Password);
			}

			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

		private UpdateDefinition<User> BuildUpdateDefinitionForRegistrationData(User user, UserParticipantCampaign participant, bool isFromAdmin) {
			var update = Builders<User>.Update
				.Set(c => c.Name, user.Name)
				.Set(c => c.GpInf, user.GpInf)
				.Set(c => c.GpPartnerInf, user.GpPartnerInf)
				.Set(c => c.UsersParticipantCampaign[-1].Contact, participant.Contact)
				.Set(c => c.UsersParticipantCampaign[-1].UpdateDate, DateTime.UtcNow)
				.Set(c => c.UpdateDate, DateTime.UtcNow);

			if (participant.AuthenticationMfaSettings != null)
			{
				update = update.Set(c => c.UsersParticipantCampaign[-1].AuthenticationMfaSettings, participant.AuthenticationMfaSettings);
			}

			if (user.IsFisicPerson())
			{
				update = update.Set(c => c.Type, PersonType.Fisica)
					.Set(c => c.Cpf, user.Cpf)
					.Set(c => c.Rg, user.Rg)
					.Set(c => c.Gender, user.Gender)
					.Set(c => c.MaritalStatus, user.MaritalStatus)
					.Set(c => c.BirthDate, user.BirthDate);
			}
			else if (user.IsJuridicPerson())
			{
				update = update.Set(c => c.Type, PersonType.Juridica)
					.Set(c => c.Cnpj, user.Cnpj)
					.Set(c => c.CompanyName, user.CompanyName)
					.Set(c => c.StateInscriptionExempt, user.StateInscriptionExempt)
					.Set(c => c.StateInscription, user.StateInscription)
					.Set(c => c.StateInscriptionUf, user.StateInscriptionUf);

				if (!isFromAdmin)
				{
					update = update.Set(c => c.UsersParticipantCampaign[-1].AccountRepresentative, participant.AccountRepresentative);
				}
			}

			// Atualização pela Gestão de Participantes no Admin
			if (isFromAdmin)
			{
				update = update.Set(c => c.UsersParticipantCampaign[-1].Active, participant.Active)
					.Set(c => c.UsersParticipantCampaign[-1].Blocked, participant.Blocked)
					.Set(c => c.UsersParticipantCampaign[-1].AllowLoginSite, participant.AllowLoginSite)
					.Set(c => c.UsersParticipantCampaign[-1].FirstAccess, participant.FirstAccess)
					.Set(c => c.UsersParticipantCampaign[-1].RankingId, participant.RankingId);
			}
			return update;
		}

		public async Task<bool> UpdateFirstAccessFlag(User user, UserParticipantCampaign participant) {
			var filter = Builders<User>.Filter.Where(u => u.Id == user.Id && u.UsersParticipantCampaign.Any(p => p.Id == participant.Id));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].FirstAccess, participant.FirstAccess)
				.Set(c => c.UsersParticipantCampaign[-1].FirstAccessDate, DateTime.UtcNow)
				.Set(c => c.UsersParticipantCampaign[-1].LastRegistrationReviewDate, participant.LastRegistrationReviewDate)
				.Set(c => c.UpdateDate, DateTime.UtcNow);

			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> SetPrivacySetttings(Guid userId, Guid campaignId, PrivacySettings privacySettings) {
			var filter = BuildFilterByUserIdAndCampaignId(userId, campaignId);

			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings, privacySettings)
				.Set(c => c.UpdateDate, DateTime.UtcNow);

			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdatePrivacySettings(Guid userId, Guid campaignId, PrivacyPolicyResult privacyResult) {
			var filter = BuildFilterByUserIdAndCampaignId(userId, campaignId);

			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings.PrivacyPolicyAccepted, privacyResult.Accepted)
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings.ContentId, privacyResult.ContentId)
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings.Version, privacyResult.Version)
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings.EssentialsCookies, privacyResult.EssentialsCookies)
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings.AnalyticsCookies, privacyResult.AnalyticsCookies)
				.Set(c => c.UsersParticipantCampaign[-1].PrivacySettings.UpdateDate, DateTime.UtcNow)
				.Set(c => c.UpdateDate, DateTime.UtcNow);

			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdatePrivacySetttings(Guid userId, Guid campaignId, bool privacyPolicyAccepted,
				bool acceptedCampaignCommunications, bool acceptedPartnerCommunications) {
			var filter = BuildFilterByUserIdAndCampaignId(userId, campaignId);

			var updateDefinition = Builders<User>.Update
				.Set(c => c.UpdateDate, DateTime.UtcNow);

			return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
		}

        public async Task<bool> SaveAddress(Guid userId, Guid participantId, List<Address> address) {
            var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Id == participantId));

            var updateDefinition = Builders<User>.Update
                .Set(c => c.UsersParticipantCampaign[-1].Addresses, address)
                .Set(c => c.UsersParticipantCampaign[-1].UpdateDate, DateTime.UtcNow);

            return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition));
        }

        public async Task<bool> UpdateAddress(Guid userId, Guid participantId, Address address) {
            var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.Id == participantId));

            var updateDefinition = Builders<User>.Update
                .Set("UsersParticipantCampaign.$[p].Addresses.$[a]", address)
                .Set("UsersParticipantCampaign.$[p].UpdateDate", DateTime.UtcNow);

            UpdateOptions updateOptions = new UpdateOptions {
                ArrayFilters = new List<ArrayFilterDefinition> {
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("p._id", participantId)),
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("a._id", address.Id))
                }
            };

            return MongoDataBaseHelper.WasUpdated(await this.colletion.UpdateOneAsync(filter, updateDefinition, updateOptions));
        }

		public async Task<bool> UpdateCreditCardBilling(Guid userId, Guid creditCardId, UserCreditCardBilling userCreditCardBilling)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.Wallet.CreditCards.Any(p => p.Id == creditCardId));

			var updateDefinition = Builders<User>.Update
				.Set(c => c.Wallet.CreditCards[-1].Billing, userCreditCardBilling)
				.CurrentDate(c => c.UpdateDate)
				.CurrentDate(c => c.Wallet.CreditCards[-1].SystemUpdateddDate);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdatePassword(Guid userId, Guid campaignId, string password)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].Password, new Senha(password, password))
				.Set(c => c.UpdateDate, DateTime.UtcNow);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateAccess(UserParticipantCampaign participant)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == participant.UserId && u.UsersParticipantCampaign.Any(p => p.Id == participant.Id));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].Balance, participant.Balance)
				.Set(c => c.UsersParticipantCampaign[-1].LastAccessDate, participant.LastAccessDate)
				.CurrentDate(c => c.UpdateDate);
			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> ResetFirstAccess(Guid userId, Guid campaignId)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].FirstAccess, true)
				.CurrentDate(c => c.UpdateDate);
			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateLastAccess(Guid userId, Guid campaignId, string currentTimezone, decimal? currentBalance = default(decimal?))
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].LastAccessDate, DateTime.UtcNow)
				.Set(c => c.UsersParticipantCampaign[-1].LastAccessTimezone, currentTimezone);

			if (currentBalance != null && currentBalance.HasValue)
			{
				updateDefinition = updateDefinition.Set(c => c.UsersParticipantCampaign[-1].Balance, currentBalance);
			}

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateParticipantLoginAttempts(Guid userId, Guid campaignId, int loginAttempts, BlockingDetails blockDetails, bool blockParticipant = false)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].UpdateDate, DateTime.UtcNow);
			if (blockParticipant)
			{
				updateDefinition = updateDefinition
					.Set(c => c.UsersParticipantCampaign[-1].Blocked, true)
					.Set(c => c.UsersParticipantCampaign[-1].BlockingOrigin, BlockingOrigin.MaximumLoginAttemptsExceeded)
					.Set(c => c.UsersParticipantCampaign[-1].BlockingDetails, blockDetails)
					.Set(c => c.UsersParticipantCampaign[-1].LoginAttempts, 0);
			}
			else
			{
				updateDefinition = updateDefinition
					.Set(c => c.UsersParticipantCampaign[-1].LoginAttempts, loginAttempts);
			}
			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> ResetParticipantLoginAttempts(Guid userId, Guid campaignId)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(c => c.UsersParticipantCampaign[-1].LoginAttempts, 0)
				.Set(c => c.UsersParticipantCampaign[-1].UpdateDate, DateTime.UtcNow);
			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<User> GetDocumentByParticipantId(Guid participantId)
		{
			return await colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.Id == participantId))
				.Select(u => new User()
				{
					Id = u.Id,
					Type = u.Type,
					Cpf = u.Cpf,
					Cnpj = u.Cnpj
				})
				.FirstOrDefaultAsync();
		}

		public Task<Guid> GetUserIdByDocument(string document, Guid campaignId)
		{
			return colletion.AsQueryable()
				.Where(u => (u.Cnpj == document || u.Cpf == document) && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.Select(u => u.Id)
				.FirstOrDefaultAsync();
		}

		public async Task<User> GetByDocument(string document)
		{
			var personType = PersonTypeHelper.FromDocument(document);
			if (personType == PersonType.Fisica)
			{
				return await GetByCpf(document);
			}
			return await GetByCnpj(document);
		}

		public async Task<User> GetByCpf(string cpf)
		{
			return await colletion.AsQueryable()
				.FirstOrDefaultAsync(u => u.Cpf == cpf);
		}

		public async Task<User> GetByCnpj(string cnpj)
		{
			return await colletion.AsQueryable()
				.FirstOrDefaultAsync(u => u.Cnpj == cnpj);
		}

		public Task<User> GetUserByDocumentAndCampaign(PersonType personType, string document, Guid campaignId)
		{
			var filter = FilterDefinition<User>.Empty;
			if (personType == PersonType.Fisica)
			{
				filter &= Builders<User>.Filter.Eq(u => u.Cpf, document);
			}
			else
			{
				filter &= Builders<User>.Filter.Eq(u => u.Cnpj, document);
			}
			filter &= Builders<User>.Filter.Where(u => u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			return colletion.Find(filter)
				.FirstOrDefaultAsync();
		}

		public async Task<List<User>> GetByBusinessTypeAndName(UserBusinessType businessType, string name)
		{
			var filter = FilterDefinition<User>.Empty;
			filter = filter & Builders<User>.Filter.Eq(u => u.BusinessType, businessType) &
				(Builders<User>.Filter.Regex("Name", BsonRegularExpression.Create(new Regex(name, RegexOptions.IgnoreCase))) |
					Builders<User>.Filter.Regex("CompanyName", BsonRegularExpression.Create(new Regex(name, RegexOptions.IgnoreCase))));

			return await colletion.Find(filter)
				.Sort(Builders<User>.Sort.Ascending("Name"))
				.Project(c => new User()
				{
					Id = c.Id,
					Type = c.Type,
					Cpf = c.Cpf,
					Cnpj = c.Cnpj,
					Name = c.Name,
					CompanyName = c.CompanyName,
					BusinessType = c.BusinessType,
					Active = c.Active,
					UsersParticipantCampaign = c.UsersParticipantCampaign.Select(p => new UserParticipantCampaign()
					{
						Id = p.Id,
						CampaignId = p.CampaignId,
						Type = p.Type, // ! Verificar quem usa este campo
						Active = p.Active
					}).ToList()
				})
				.ToListAsync();
		}

		public async Task<List<User>> GetByBusinessTypeAndCnpj(UserBusinessType businessType, string cnpj)
		{
			var filter = FilterDefinition<User>.Empty &
				Builders<User>.Filter.Eq(u => u.BusinessType, businessType) &
				Builders<User>.Filter.Eq(u => u.Cnpj, cnpj);

			return await colletion.Find(filter)
				.Sort(Builders<User>.Sort.Ascending("Name"))
				.Project(c => new User()
				{
					Id = c.Id,
					Type = c.Type,
					Cnpj = c.Cnpj,
					Name = c.Name,
					CompanyName = c.CompanyName,
					BusinessType = c.BusinessType,
					Active = c.Active,
					UsersParticipantCampaign = c.UsersParticipantCampaign.Select(p => new UserParticipantCampaign()
					{
						Id = p.Id,
						CampaignId = p.CampaignId,
						Type = p.Type, // ! Verificar quem usa este campo
						Active = p.Active
					}).ToList()
				})
				.ToListAsync();
		}

		public async Task<List<User>> GetByBusinessTypeAndCpf(UserBusinessType businessType, string cpf)
		{
			var filter = FilterDefinition<User>.Empty &
				Builders<User>.Filter.Eq(u => u.BusinessType, businessType) &
				Builders<User>.Filter.Eq(u => u.Cpf, cpf);

			return await colletion.Find(filter)
				.Sort(Builders<User>.Sort.Ascending("Name"))
				.Project(c => new User()
				{
					Id = c.Id,
					Type = c.Type,
					Cpf = c.Cpf,
					Name = c.Name,
					CompanyName = c.CompanyName,
					BusinessType = c.BusinessType,
					Active = c.Active,
					UsersParticipantCampaign = c.UsersParticipantCampaign.Select(p => new UserParticipantCampaign()
					{
						Id = p.Id,
						CampaignId = p.CampaignId,
						Type = p.Type, // ! Verificar quem usa este campo
						Active = p.Active
					}).ToList()
				})
				.ToListAsync();
		}

		///<summary>
		/// Deprecear: não utiliza o ID da campanha.
		/// Verificar os processos que estão usando.
		///</summary>
		public async Task<User> GetByLogin(string login, bool onlyActive = true)
		{
			if (onlyActive)
			{
				return await colletion.AsQueryable()
					.Where(u => u.Active && u.UsersParticipantCampaign.Any(p => p.Active && p.Login == login))
					.FirstOrDefaultAsync();
			}
			return await colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.Login == login))
				.FirstOrDefaultAsync();
		}

		public async Task<User> GetByLogin(Guid campaignId, string login, bool onlyActive = true)
		{
			if (onlyActive)
			{
				return await colletion.AsQueryable()
					.Where(u => u.Active && u.UsersParticipantCampaign.Any(p => p.Active &&
					  p.CampaignId == campaignId && p.Login == login))
					.FirstOrDefaultAsync();
			}
			return await colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Login == login))
				.FirstOrDefaultAsync();
		}

		public Task<bool> ExistParticipantWithLogin(Guid campaignId, string login)
		{
			return colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.Login == login))
				.AnyAsync();
		}

		public async Task<User> GetUserByParticipantId(Guid participantId)
		{
			return await colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.Id == participantId))
				.FirstOrDefaultAsync();
		}

		public async Task<List<User>> GetByCampaignAndName(Guid campaignId, string name)
		{
			return await colletion.AsQueryable()
				.Where(u => u.Name.Contains(name) && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.ToListAsync();
		}

		public async Task<UserParticipantCampaign> GetParticipantByCampaign(Guid userId, Guid campaignId)
		{
			return await colletion.AsQueryable()
				.Where(u => u.Active && u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.FirstOrDefaultAsync();
		}

		public async Task<UserParticipantCampaign> GetParticipantByDocument(Guid campaignId, string document)
		{
			return await colletion.AsQueryable()
				.Where(u => (u.Cnpj == document || u.Cpf == document) && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.Select(u => u.UsersParticipantCampaign.First(p => p.CampaignId == campaignId))
				.FirstOrDefaultAsync();
		}

		public async Task<Guid> GetParticipantIdByUserAndCampaign(Guid userId, Guid campaignId)
		{
			return await colletion.AsQueryable()
				.Where(u => u.Active && u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => p.Id)
				.FirstOrDefaultAsync();
		}

		public async Task<List<User>> GetByCampaign(Guid campaignId)
		{
			return await colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.ToListAsync();
		}

		public async Task<ParticipantInfo> GetUserData(Guid userId, Guid campaignId)
		{
			// LoggerHelper.GetLogger().Info("{date} - Users - User Repository Start - {methodName}", DateTime.UtcNow, nameof(GetUserData));
			var activeCondition = new BsonDocument {
				{
				"$cond",
				new BsonArray {
				new BsonDocument {
				{
				"$and",
				new BsonArray { "$UsersParticipantCampaign.Active", "$Active" }
				}
				},
				true,
				false
				}
				}
			};

			var nameCondition = new BsonDocument {
				{
				"$cond",
				new BsonDocument {
				{
				"if",
				new BsonDocument {
				{
				"$eq",
				new BsonArray {
				"$Type",
				PersonType.Fisica.ToString ()
				}
				},
				}
				}, { "then", "$Name" }, {
				"else",
				new BsonDocument {
				{
				"$ifNull",
				new BsonArray { "$CompanyName", "$TradeName" }
				}
				}
				}
				}
				}
			};

			var emailCondition = new BsonDocument {
				{
				"$ifNull",
				new BsonArray {
				"$UsersParticipantCampaign.Contact.MainEmail",
				new BsonDocument {
				{
				"$ifNull",
				new BsonArray {
				"$UsersParticipantCampaign.Contact.PersonalEmail",
				"$UsersParticipantCampaign.Contact.CommercialEmail"
				}
				}
				}
				}
				}
			};

			var mobileCondition = new BsonDocument {
				{
				"$ifNull",
				new BsonArray {
				"$UsersParticipantCampaign.Contact.MobilePhone",
				new BsonDocument {
				{
				"$ifNull",
				new BsonArray {
				"$UsersParticipantCampaign.Contact.PersonalEmail",
				"$UsersParticipantCampaign.Contact.CommercialEmail"
				}
				}
				}
				}
				}
			};

			return await this.colletion.Aggregate()
				.Match(new BsonDocument { { "_id", userId } })
				.Unwind("UsersParticipantCampaign")
				.Match(new BsonDocument { { "UsersParticipantCampaign.CampaignId", campaignId } })
				.Project(new BsonDocument { { "UserId", "$_id" }, { "CampaignId", "$UsersParticipantCampaign.CampaignId" }, { "ClientUserId", "$UsersParticipantCampaign.ClientUserId" }, { "ParticipantId", "$UsersParticipantCampaign._id" }, { "Active", activeCondition }, { "Login", "$UsersParticipantCampaign.Login" }, { "Name", nameCondition }, { "CompanyName", "$UsersParticipantCampaign.CompanyName" }, { "Type", "$Type" }, { "Rg", "$Rg" }, { "Cpf", "$Cpf" }, { "Cnpj", "$Cnpj" }, { "AccountRepresentative", "$UsersParticipantCampaign.AccountRepresentative" }, { "StateInscriptionExempt", "$StateInscriptionExempt" }, { "StateInscription", "$StateInscription" }, { "StateInscriptionUf", "$StateInscriptionUf" }, { "CompanyIdentifier", "$UsersParticipantCampaign.Employee.CompanyIdentifier" }, { "MainEmail", emailCondition }, { "MobilePhone", "$UsersParticipantCampaign.Contact.MobilePhone" }, { "_id", 0 }
				}).As<ParticipantInfo>()
				.FirstOrDefaultAsync();
			// LoggerHelper.GetLogger().Info("{date} - Users - User Repository End - {methodName}", DateTime.UtcNow, nameof(GetUserData));
		}

		public async Task<dynamic> GetDocumentBy(Guid userId)
		{
			// LoggerHelper.GetLogger().Info("{date} - Users - User Repository Start - {methodName}", DateTime.UtcNow, nameof(GetDocumentBy));
			var document = await this.colletion.Aggregate()
				.Match(u => u.Id == userId)
				.Project(new BsonDocument {
					{
						"id",
						"$_id"
					}, {
						"document",
						new BsonDocument () {
							{
								"$ifNull",
								new BsonArray {
									"$Cpf",
									"$Cnpj"
								}
							}
						}
					}, {
						"_id",
						0
					}, {
						"personType",
						"$Type"
					}
				})
				.As<dynamic>()
				.FirstOrDefaultAsync();
			// LoggerHelper.GetLogger().Info("{date} - Users - User Repository End - {methodName}", DateTime.UtcNow, nameof(GetDocumentBy));
			return document;
		}

		public async Task<List<UserCampaignsInfo>> GetUsersByListOfIds(List<Guid> usersIds)
		{
			return await colletion.AsQueryable()
				.Where(x => usersIds.Contains(x.Id))
				.Select(x => new UserCampaignsInfo()
				{
					UserId = x.Id,
					Document = x.Cpf ?? x.Cnpj,
					Name = x.Name
				})
				.ToListAsync();
		}

		public async Task<List<UserCampaignsInfo>> GetUsersInfoByIds(Guid campaignId, List<Guid> usersIds)
		{
			return await this.colletion.Aggregate()
				.Match(u => usersIds.Contains(u.Id))
				.Unwind("UsersParticipantCampaign")
				.Match(new BsonDocument { { "UsersParticipantCampaign.CampaignId", campaignId } })
				.Project(new BsonDocument { { "UserId", "$_id" }, {
						"Document",
						new BsonDocument () {
							{
								"$ifNull",
								new BsonArray {
									"$Cpf",
									"$Cnpj"
								}
							}
						}
					}, { "Active", "$Active" },
					{ "Name", "$Name" },
					{ "CompanyName", "$CompanyName"},
					{
						"MainAddress",
						new BsonDocument () {
							{
								"$arrayElemAt",
								new BsonArray (new Object[] {
									"$UsersParticipantCampaign.Addresses",
									new BsonDocument () {
										{
											"$indexOfArray",
											new BsonArray (new Object[] {
												"$UsersParticipantCampaign.Addresses",
												new BsonDocument { { "MainAddress", true }
												}
											})
										}
									}
								})
							}
						}
					}, { "_id", 0 }
				})
				.As<UserCampaignsInfo>()
				.ToListAsync();
		}

		public async Task<bool> UpdateUserExternal(Guid id, Guid participantId, User user, UserParticipantCampaign userParticipantCampaign)
		{ // ! Que Deus tenha misericórdia dessa nação.
			var filter = Builders<User>.Filter.Where(u => u.Id == id && u.UsersParticipantCampaign.Any(p => p.Id == participantId));
			var updateDefinition = Builders<User>.Update
				.Set(a => a.Active, user.Active)
				.Set(a => a.Name, user.Name)
				.Set(a => a.Rg, user.Rg)
				.Set(a => a.BirthDate, user.BirthDate)
				.Set(a => a.CompanyName, user.CompanyName)
				.Set(a => a.StateInscription, user.StateInscription)
				.Set(a => a.StateInscriptionExempt, user.StateInscriptionExempt)
				.Set(a => a.StateInscriptionUf, user.StateInscriptionUf)
				.Set(a => a.UsersParticipantCampaign[-1].Addresses, userParticipantCampaign.Addresses)
				.Set(a => a.UsersParticipantCampaign[-1].Contact, userParticipantCampaign.Contact)
				.Set(a => a.UsersParticipantCampaign[-1].Employee, userParticipantCampaign.Employee)
				.Set(a => a.UsersParticipantCampaign[-1].IntegrationCompany, userParticipantCampaign.IntegrationCompany)
				.CurrentDate(a => a.UpdateDate);
			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public Task<UserBasicInfo> GetUserBasicInfoByDocument(Guid campaignId, string document)
		{
			Expression<Func<User, bool>> predicate = u => (u.Cnpj == document || u.Cpf == document)
				&& u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId);
			return GetUserBasicInfo(campaignId, predicate);
		}

		public Task<UserBasicInfo> GetUserBasicInfoByClientUserId(Guid campaignId, string clientUserId)
		{
			Expression<Func<User, bool>> predicate = u => u.Active == true &&
				u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId && p.ClientUserId == clientUserId && p.Active == true);
			return GetUserBasicInfo(campaignId, predicate);
		}

		public Task<UserBasicInfo> GetUserBasicInfo(Guid campaignId, Guid userId)
		{
			Expression<Func<User, bool>> predicate = u => u.Id == userId &&
				u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId);
			return GetUserBasicInfo(campaignId, predicate);
		}

		private Task<UserBasicInfo> GetUserBasicInfo(Guid campaignId, Expression<Func<User, bool>> predicate)
		{
			return colletion.AsQueryable()
				.Where(predicate)
				.Select(u => new UserBasicInfo()
				{
					UserId = u.Id,
					CampaignId = campaignId,
					Active = u.Active,
					Name = u.Name,
					Cpf = u.Cpf,
					Cnpj = u.Cnpj,
					ParticipantId = u.UsersParticipantCampaign
						.Where(a => a.CampaignId == campaignId)
						.Select(p => p.Id)
						.First()

				})
				.FirstOrDefaultAsync();
		}

		public async Task<bool> BlockParticipant(Guid userId, Guid campaignId, BlockingDetails blockingDetails, BlockingOrigin blockingOrigin)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId
					&& u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1].Active, false)
				.Set(a => a.UsersParticipantCampaign[-1].Blocked, true)
				.Set(a => a.UsersParticipantCampaign[-1].BlockingOrigin, blockingOrigin)
				.Set(a => a.UsersParticipantCampaign[-1].BlockingDetails, blockingDetails)
				.CurrentDate(a => a.UpdateDate);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UnblockingParticipant(Guid userId, Guid campaignId, UnblockingParticipantDetails unblockingDetails)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId
					&& u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1].Active, true)
				.Set(a => a.UsersParticipantCampaign[-1].Blocked, false)
				.Set(a => a.UsersParticipantCampaign[-1].AllowLoginSite, true)
				.CurrentDate(a => a.UpdateDate);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public Task<AccountRepresentative> GetAccountRepresentative(Guid userId, Guid campaignId)
		{
			return colletion.AsQueryable()
				.Where(u => u.Active && u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => p.AccountRepresentative)
				.FirstOrDefaultAsync();
		}

		public Task<bool> ExistUserWithDocument(Guid userId, string userDocument)
		{
			return colletion.AsQueryable()
				.Where(u => u.Id != userId && (u.Cpf == userDocument || u.Cnpj == userDocument))
				.AnyAsync();
		}

		public async Task<bool> AddUserAppCard(Guid userId, Guid campaignId, Card userCard)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			var updateDefinition = Builders<User>.Update
				.Set(u => u.UsersParticipantCampaign[-1].FirstAccess, false)
				.Set(c => c.UsersParticipantCampaign[-1].FirstAccessDate, DateTime.UtcNow)
				.Set(c => c.UsersParticipantCampaign[-1].LastRegistrationReviewDate, DateTime.UtcNow)
				.Push(u => u.Wallet.Cards, userCard)
				.CurrentDate(a => a.UpdateDate);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public async Task<bool> UpdateUserAppCards(Guid userId, Guid campaignId, List<Card> cards)
		{
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

			var updateDefinition = Builders<User>.Update
				.Set(u => u.UsersParticipantCampaign[-1].FirstAccess, false)
				.Set(c => c.UsersParticipantCampaign[-1].FirstAccessDate, DateTime.UtcNow)
				.Set(c => c.UsersParticipantCampaign[-1].LastRegistrationReviewDate, DateTime.UtcNow)
				.Set(u => u.Wallet.Cards, cards)
				.CurrentDate(a => a.UpdateDate);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
		}

		public Task<List<Card>> GetAppCards(Guid userId, Guid campaignId, bool onlyActive)
		{
			return colletion.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(x => x.Wallet.Cards)
				.Where(x => (x.Active || !onlyActive))
				.Select(MapToCard())
				.ToListAsync();
		}

		public Task<List<Card>> GetActiveAppCards(Guid userId, Guid campaignId, List<string> cardCodes)
		{
			return colletion.AsQueryable()
				.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(x => x.Wallet.Cards)
				.Where(x => x.Active && cardCodes.Contains(x.CardCode))
				.Select(MapToCard())
				.ToListAsync();
		}

		private Expression<Func<Card, Card>> MapToCard()
		{
			return source => new Card
			{
				Id = source.Id,
				Active = source.Active,
				Number = source.Number,
				LastDigits = source.LastDigits,
				CardType = source.CardType,
				CardCode = source.CardCode,
				CardId = source.CardId,
				CardEmitterDocument = source.CardEmitterDocument,
				AccountNumber = source.AccountNumber,
				HolderName = source.HolderName,
				CampaignId = source.CampaignId,
				CreateDate = source.CreateDate,
				ExpMonth = source.ExpMonth,
				ExpYear = source.ExpYear,
				PartnerCardIdentification = source.PartnerCardIdentification,
				ExternalCardToken = source.ExternalCardToken
			};
		}

        public async Task<bool> ResetAppCards(Guid campaignId, Guid userId, List<string> cardCodes)
        {
            var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));

            var updateDefinition = Builders<User>.Update
				.Set(u => u.UsersParticipantCampaign[-1].FirstAccess, true)
                .PullFilter("Wallet.Cards", Builders<Card>.Filter.In(card => card.CardCode, cardCodes))
                .CurrentDate(a => a.UpdateDate);

            return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
        }

		public Task<List<User>> GetUserChildrenParticipants(Guid userId, Guid campaignId)
		{
			return colletion.AsQueryable()
				.Where(u => u.UsersParticipantCampaign.Any(p => p.ParentUserId == userId && p.CampaignId == campaignId))
				.Select(u => new User
				{
					Id = u.Id,
					Active = u.Active,
					Name = u.Name,
					CompanyName = u.CompanyName,
					Type = u.Type,
					Cpf = u.Cpf,
					Cnpj = u.Cnpj,
					Blocked = u.Blocked,
					UsersParticipantCampaign = u.UsersParticipantCampaign
				})
				.ToListAsync();
		}

        public async Task<ParticipantAuthenticationMfaSettings> FindAuthenticationMfaSettings(Guid campaignId, Guid userId)
        {
			return await colletion.AsQueryable()
				.Where(u => u.Active && u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
				.SelectMany(u => u.UsersParticipantCampaign)
				.Where(p => p.CampaignId == campaignId)
				.Select(p => p.AuthenticationMfaSettings)
				.FirstOrDefaultAsync();
        }

        public async Task<bool> UpdateAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
			var filter = Builders<User>.Filter.Where(u => u.Id == userId && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId));
			var updateDefinition = Builders<User>.Update
				.Set(a => a.UsersParticipantCampaign[-1].AuthenticationMfaSettings, userAuthenticationMfa)
				.CurrentDate(a => a.UpdateDate);

			return MongoDataBaseHelper.WasUpdated(await colletion.UpdateOneAsync(filter, updateDefinition));
        }
    }
}
