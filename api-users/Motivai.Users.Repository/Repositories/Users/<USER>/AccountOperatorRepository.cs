using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.Models.AccountOperators;
using Motivai.Users.Domain.Entities.AccountOperators.Authentication;
using Newtonsoft.Json;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Repository.Repositories.Users.AccountOperators
{
    public class AccountOperatorRepository : IAccountOperatorRepository
    {
        private readonly IMongoCollection<AccountOperator> accountsOperators;

        public AccountOperatorRepository()
        {
            this.accountsOperators = MongoDataBaseHelper.GetMongoDatabase()
                .GetCollection<AccountOperator>("UsersAccountsOperators");
        }

        public async Task<AccountOperator> GetById(Guid accountOperatorId)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Id == accountOperatorId)
                .FirstOrDefaultAsync();
        }

        public async Task<AccountOperator> FindAccountOperatorByDocumentAndCampaign(string document, Guid campaignId)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Document == document && a.Logins.Any(l => l.CampaignId == campaignId))
                .FirstOrDefaultAsync();
        }

        public async Task<AccountOperator> FindAccountOperatorByEmailAndDocument(Guid campaignId, AccountOperatorSsoRequest operatorRequest)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Document == operatorRequest.Cpf && a.Active
                    && a.Logins.Any(e => e.Active && e.CampaignId == campaignId && e.Active && e.Email == operatorRequest.Email))
                .FirstOrDefaultAsync();
        }

        public async Task<List<AccountOperator>> GetAccountOperators(Guid userId, Guid campaignId)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Logins.Any(l => l.CampaignId == campaignId && l.AccessibleAccounts.Any(aa => aa.UserId == userId)))
                .ToListAsync();
        }

        public async Task<AccountOperator> GetAccountOperatorLoginByCampaignAndLogin(Guid campaignId, string login)
        {
            var loginFilter = Builders<AccountOperatorLogin>.Filter.Eq("CampaignId", campaignId) &
                Builders<AccountOperatorLogin>.Filter.Regex("Login", new BsonRegularExpression(login, "i"));

            var filter = Builders<AccountOperator>.Filter.ElemMatch("Logins", loginFilter);

            return await accountsOperators.Find(filter)
                .FirstOrDefaultAsync();
        }

        public async Task<AccountOperator> GetAccountOperatorLoginByCampaignAndLogin2(Guid campaignId, string login)
        {
            var loginUppercase = login.ToUpper();
            return await accountsOperators.AsQueryable()
                .Where(a => a.Logins.Any(l => l.CampaignId == campaignId && l.Login == login && l.AccessibleAccounts.Count > 0))
                .FirstOrDefaultAsync();
        }

        public async Task<AccountOperator> GetAccountOperatorByCampaignEmailAndLogin(Guid campaignId, string email, string login)
        {
            var loginFilter = FilterDefinition<AccountOperatorLogin>.Empty &
                Builders<AccountOperatorLogin>.Filter.Eq("CampaignId", campaignId) &
                Builders<AccountOperatorLogin>.Filter.Regex("Email", new BsonRegularExpression(email, "i")) &
                Builders<AccountOperatorLogin>.Filter.Regex("Login", new BsonRegularExpression(login, "i"));

            var filter = FilterDefinition<AccountOperator>.Empty &
                Builders<AccountOperator>.Filter.ElemMatch("Logins", loginFilter);
            return await accountsOperators.Find(filter)
                .FirstOrDefaultAsync();
        }

        public async Task<AccountOperator> FindAccountOperatorByDocument(string document)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Document == document)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> VerifyDifferentOperatorByEmail(Guid campaignId, string currentDocument, string email)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Document != currentDocument && a.Logins.Any(l => l.CampaignId == campaignId && l.Email == email))
                .AnyAsync();
        }

        public async Task Create(AccountOperator accountOperator)
        {
            await accountsOperators.InsertOneAsync(accountOperator);
        }

        public async Task<bool> Update(AccountOperator accountOperator)
        {
            var result = await accountsOperators.ReplaceOneAsync(a => a.Id == accountOperator.Id, accountOperator);
            return result.IsModifiedCountAvailable ? result.ModifiedCount > 0 : result.IsAcknowledged;
        }

        public async Task<bool> UpdateAccountOperatorLoginsByCampaigns(AccountOperator accountOperator, List<Guid> campaignIds)
        {
            var filter = Builders<AccountOperator>.Filter.Where(a => a.Id == accountOperator.Id && a.Logins.Any(l => campaignIds.Contains(l.CampaignId)));
            var updateDefinition = Builders<AccountOperator>.Update
                .Set(a => a.Name, accountOperator.Name)
                .Set(a => a.Logins, accountOperator.Logins)
                .CurrentDate(a => a.UpdateDate);

            return MongoDataBaseHelper.WasUpdated(await accountsOperators.UpdateOneAsync(filter, updateDefinition));
        }

        public async Task<bool> InsertAccountOperatorLogin(Guid accountOperatorId, AccountOperatorLogin accountOperatorLogin)
        {
            var filter = Builders<AccountOperator>.Filter
                .Where(u => u.Id == accountOperatorId && u.Logins.Any(l => l.CampaignId == accountOperatorLogin.CampaignId) == false);
            var updateDefinition = Builders<AccountOperator>.Update
                .Push(a => a.Logins, accountOperatorLogin)
                .CurrentDate(a => a.UpdateDate);

            return MongoDataBaseHelper.WasUpdated(await accountsOperators.UpdateOneAsync(filter, updateDefinition));
        }

        public async Task<bool> UpdateAccountOperatorLoginEmail(Guid accountOperatorId, Guid accountOperatorLoginId, UpdateDataAccountOperator updateDataAccountOperator)
        {
            var filter = Builders<AccountOperator>.Filter.Where(u => u.Id == accountOperatorId && u.Logins.Any(p => p.Id == accountOperatorLoginId));
            var updateDefinition = Builders<AccountOperator>.Update
                .Set(a => a.Logins[-1].Email, updateDataAccountOperator.FieldValue)
                .Set(a => a.Logins[-1].Login, updateDataAccountOperator.FieldValue)
                .Set(a => a.Logins[-1].UpdateOperationUser, updateDataAccountOperator.OperationUserDataAction)
                .Set(a => a.Logins[-1].UpdateDate, DateTime.UtcNow);

            return MongoDataBaseHelper.WasUpdated(await accountsOperators.UpdateOneAsync(filter, updateDefinition));
        }

        public async Task<bool> UpdateAccountOperatorLoginRole(Guid userId, Guid accountOperatorId, Guid accountOperatorLoginId, OperatorRole operatorRole)
        {
            var filter = Builders<AccountOperator>.Filter.Where(u => u.Id == accountOperatorId && u.Logins.Any(l => l.Id == accountOperatorLoginId));
            var updateDefinition = Builders<AccountOperator>.Update
                .Set("Logins.$[a].AccessibleAccounts.$[b].Role", operatorRole.ToString())
                .Set("Logins.$[a].UpdateDate", DateTime.UtcNow);

            UpdateOptions updateOptions = new UpdateOptions
            {
                ArrayFilters = new List<ArrayFilterDefinition> {
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("a._id", accountOperatorLoginId)),
                    new BsonDocumentArrayFilterDefinition<BsonDocument>(new BsonDocument("b.UserId", userId))
                }
            };

            return MongoDataBaseHelper.WasUpdated(await accountsOperators.UpdateOneAsync(filter, updateDefinition, updateOptions));
        }

        public async Task<AccountOperator> FindAccountOperatorBy(string document, Guid campaignId, Guid userId)
        {
            return await accountsOperators.AsQueryable()
                .Where(a => a.Document == document && a.Logins.Any(l => l.CampaignId == campaignId && l.AccessibleAccounts.Any(aa => aa.UserId == userId)))
                .FirstOrDefaultAsync();
        }

        public Task<List<OperatorAcessibleAccounts>> FindAcessibleAccountsBy(string document, Guid campaignId)
        {
            var filter = Builders<AccountOperator>.Filter.Where(a => a.Document == document);
            return accountsOperators.Aggregate()
                .Match(filter)
                .Unwind("Logins")
                .Match(new BsonDocument {{"Logins.CampaignId", campaignId}})
                .Project(new BsonDocument {
                    {"Document", "$Document"},
                    {"Name", "$Name"},
                    {"Email", "$Logins.Email"},
                    {"MobilePhone", "$Logins.MobilePhone"},
                    {"AccountDocument", "$Logins.AccessibleAccounts.UserDocument"},
                    {"_id", 0}
                })
                .As<OperatorAcessibleAccounts>()
                .ToListAsync();
        }
    }
}
