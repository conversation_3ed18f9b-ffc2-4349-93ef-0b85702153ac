using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.Models.Security;

namespace Motivai.Users.Repository.Repositories.Email
{
	public class EmailRepository : IEmailRepository
	{
		public async Task<bool> SendSecurityCodeByMethod(Guid userId, Guid campaignId, string name, string email, string mobilePhone,
				SecurityCodeSendMethod sendMethod, string securityCode)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/users/securitycode")
				.Entity(new
				{
					userId,
					campaignId,
					name,
					email,
					mobilePhone,
					sendMethod,
					securityCode
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi enviar o código de segurança.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendNewParticipantNotification(Guid campaignId, Guid userId, string name, string email,
			string mobilePhone, string login, string password)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("emails/participants/new")
				.Entity(new
				{
					campaignId,
					userId,
					name,
					email,
					mobilePhone,
					login,
					password,
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de novo participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendToken(Guid campaignId, Guid userId, string name, string email, string mobilePhone, SecurityTokenSendMethod sendMethod, string token)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/users/securitycode")
				.Entity(new
				{
					campaignId,
					userId,
					securityCode = token,
					name,
					email,
					mobilePhone,
					sendMethod
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail com o token de segurança.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendAdminToken(Guid userId, string name, string email, SecurityTokenSendMethod sendMethod, string token)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/users/admin/securitycode")
				.Entity(new
				{
					userId,
					name,
					email,
					sendMethod,
					securityCode = token
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail com o token de segurança.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendResetPasswordEmail(Guid campaignId, Guid userId, string name, string email,
			string mobilePhone, string login, string generatedPassword)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/users/account/password/reset")
				.Entity(new
				{
					campaignId,
					userId,
					name,
					email,
					mobilePhone,
					login,
					password = generatedPassword
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail com a senha gerada.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendResetPasswordEmail(Guid campaignId, Guid userId, Guid participantId, string generatedPassword)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("emails/participants/resetpassword")
				.Entity(new
				{
					campaignId,
					userId,
					participantId,
					password = generatedPassword
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail com a senha gerada.");
			return apiReturn.GetReturnOrError();
		}

		private string GetPathByEventType(CampaignEventType eventType)
		{
			switch (eventType)
			{
				case CampaignEventType.Participant:
					return "emails/participants/new";
				case CampaignEventType.PointsDistributedNewParticipant:
					return "notifications/participants/distributions/new";
				default:
					throw MotivaiException.ofValidation("Evento de novo participante inválido.");
			}
		}

		public async Task<bool> SendNewParticipantNotificationByEvent(Guid campaignId, CampaignEventType eventType,
			Guid userId, string name, string email, string mobilePhone, string login, string password, decimal points)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path(GetPathByEventType(eventType))
				.Entity(new
				{
					campaignId,
					userId,
					name,
					email,
					mobilePhone,
					login,
					password,
					points
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de novo participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendLoginNotification(AccountLoginNotification loginNotification)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/users/account/login")
				.Entity(loginNotification)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de notificação do login.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> NotifyPasswordReseted(Guid campaignId, Guid userId, string name, string login, string newPassword, string email, string mobilePhone)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/users/password/reseted")
				.Entity(new
				{
					campaignId,
					userId,
					name,
					email,
					mobilePhone,
					login,
					password = newPassword
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de novo participante.");
			return apiReturn.GetReturnOrError();
		}

        public async Task<bool> SendFirstAccessNotification(Guid campaignId, Guid userId, string name, string email, string mobilePhone)
        {
           var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/participants/firstaccess")
				.Entity(new
				{
					campaignId,
					userId,
					name,
					email,
					mobilePhone,
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de novo participante.");
			return apiReturn.GetReturnOrError();
        }

        public async Task<bool> SendPlataformActionNotification(dynamic notification)
        {
 			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("notifications/administrators/notify")
				.Entity(notification)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de notificação da plataforma.");
			return apiReturn.GetReturnOrError();

        }
    }
}