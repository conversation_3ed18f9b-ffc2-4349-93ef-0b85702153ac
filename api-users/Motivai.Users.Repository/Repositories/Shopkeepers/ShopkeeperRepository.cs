using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace RemoverEmBreve {
    public class ShopkeeperRepository : IShopkeeperRepository {
        public async Task<AddressShopkeeper> GetAddress(Guid userId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Shopkeepers)
                .Path("shopkeepers").Path(userId).Path("address")
                .AsyncGet()
                .GetApiReturn<AddressShopkeeper>();

            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o Endereço do Lojista através de sua API.");

            return apiReturn.GetReturnOrError();
        }
    }
}