using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.PreRegister;
using MongoDB.Driver;
using MongoDB.Bson;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositories.PreRegister
{
	public class PreRegisterRepository : IPreRegisterRepository
	{
		private IMongoCollection<PreRegisteredUser> _preRegisterUsers { get; set; }
		public PreRegisterRepository()
		{
			var dataBase = MongoDataBaseHelper.GetMongoDatabase();
			_preRegisterUsers = dataBase.GetCollection<PreRegisteredUser>("PreRegisteredUsers");
		}

		public Task<List<PreRegisteredUser>> Find(Guid campaignId, string document, bool? integrated, bool? integrationError, int? skip, int? limit)
		{
			var query = _preRegisterUsers.AsQueryable().Where(x => x.CampaignId == campaignId);
			if (!string.IsNullOrEmpty(document))
			{
				query = query.Where(x => x.Document == document);
			}

			if(integrated ?? false){
				query = query.Where(x => x.Integrated == true);
			}

			if(integrationError ?? false){
				query =  query.Where(x => x.IntegrationErrorMessage.Length > 0);
			}

			query = query.OrderByDescending(x => x.CreateDate);
			query = query.Skip(skip ?? 0).Take(limit ?? 50);

			return query.ToListAsync();
		}

		public Task<PreRegisteredUser> FindByDocumentAndCampaign(string document, Guid campaignId)
		{
			return _preRegisterUsers.AsQueryable()
				.FirstOrDefaultAsync(x => x.Document == document && x.CampaignId == campaignId);
		}

		public Task<PreRegisteredUser> FindById(Guid id)
		{
			return _preRegisterUsers.AsQueryable()
				.FirstOrDefaultAsync(x => x.Id == id);
		}

		public Task<bool> VerifyExistingDocument(Guid campaignId, string document)
		{
			return _preRegisterUsers.AsQueryable()
				.AnyAsync(x => x.CampaignId == campaignId && x.Document == document
					&& (x.Approved == true || (x.Approved == false && x.Refused == false)));
		}

		public async Task<bool> Save(PreRegisteredUser user)
		{
			var opts = new UpdateOptions { IsUpsert = true };
			var created = await _preRegisterUsers.ReplaceOneAsync(x => x.Id == user.Id, user, opts);
			if (created.IsModifiedCountAvailable)
			{
				if (created.IsAcknowledged)
					return true;
				else
					return false;
			}

			return true;
		}
	}
}