using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.Security;
using Motivai.Users.Domain.IRepository.Security;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositories.Security
{
    public class BlockListedIpRepository : IBlockListedIpRepository
    {
        private readonly IMongoCollection<BlockListedIp> blockListedIps;

        public BlockListedIpRepository()
        {
            var db = MongoDataBaseHelper.GetMongoDatabase();
            blockListedIps = db.GetCollection<BlockListedIp>("SecurityBlockListedIps");
        }

        public async Task<bool> ContainsIp(string ip)
        {
            if (string.IsNullOrEmpty(ip))
                return false;
            return await blockListedIps.AsQueryable()
                .Where(i => i.Ip == ip)
                .AnyAsync();
        }
    }
}