using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.ParticipantHistoryFiles;
using Motivai.Users.Domain.IRepository.ParticipantHistoryFiles;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Users.Repository.Repositories.ParticipantHistoryFiles {
    public class ParticipantHistoryFileRepository : IParticipantHistoryFileRepository {
        private IMongoCollection<User> colletion { get; set; }

        public ParticipantHistoryFileRepository() {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            colletion = dataBase.GetCollection<User>("Users");

        }
        public async Task<List<ParticipantHistoryFile>> GetByParticipant(Guid userId, Guid campaignId, bool active) {
            var query = colletion.AsQueryable()
                .Where(a => a.UsersParticipantCampaign.Any(b => b.UserId == userId && b.CampaignId == campaignId && b.Active))
                .SelectMany(c => c.UsersParticipantCampaign)
                .SelectMany(d => d.ParticipantHistoryFiles);
            if (active == true) {
                query = query.Where(f => f.Active == true);
            }

            return await query.ToListAsync();
        }

        public async Task<bool> Save(Guid userId, Guid campaignId, ParticipantHistoryFile participantHistoryFile) {

            var filter = FilterDefinition<User>.Empty;

            filter = filter & Builders<User>.Filter.Eq(u => u.Active, true) &
                Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
                    Builders<UserParticipantCampaign>.Filter.Eq(p => p.UserId, userId) &
                    Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
                    Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId));

            // update with positional operator
            var update = Builders<User>.Update.Push(u => u.UsersParticipantCampaign[-1].ParticipantHistoryFiles, participantHistoryFile);

            await colletion.UpdateOneAsync(filter, update);

            return true;
        }

        public async Task<bool> Unactive(Guid userId, Guid campaignId, List<ParticipantHistoryFile> participantHistoryFiles) {
            var filter = FilterDefinition<User>.Empty;

            filter = filter & Builders<User>.Filter.Eq(u => u.Active, true) &
                Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
                    Builders<UserParticipantCampaign>.Filter.Eq(p => p.UserId, userId) &
                    Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
                    Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId)
                );

            // update with positional operator
            var update = Builders<User>.Update.Set(u => u.UsersParticipantCampaign[-1].ParticipantHistoryFiles, participantHistoryFiles);

            await colletion.UpdateOneAsync(filter, update);

            return true;
        }
    }
}