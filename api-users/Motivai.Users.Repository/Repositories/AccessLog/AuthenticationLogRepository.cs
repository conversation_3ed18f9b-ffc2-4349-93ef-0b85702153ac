using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Repository.Repositories.AccessLog
{
    public class AuthenticationLogRepository : IAuthenticationLogRepository
    {
        private readonly MongoDB.Driver.IMongoCollection<CampaignAuthenticationLog> _logs;

        public AuthenticationLogRepository()
        {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            this._logs = dataBase.GetCollection<CampaignAuthenticationLog>("CampaignsAuthenticationsLogs");
        }

        public Task RegisterAuthenticationLog(CampaignAuthenticationLog authenticationLog)
        {
            return this._logs.InsertOneAsync(authenticationLog);
        }

        public Task RegisterLoginFromCallcenter(CallcenterLogin callcenterLogin, UserParticipantModel participantSession,
                LocationInfo locationInfo = null)
        {
            return this._logs.InsertOneAsync(CampaignAuthenticationLog.CallcenterLogged(callcenterLogin, participantSession, locationInfo));
        }

        public Task RegisterLogin(AuthenticationActionLog action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null)
        {
            return this._logs.InsertOneAsync(CampaignAuthenticationLog.JustLogged(action, userId, campaignId, locationInfo));
        }
    }
}