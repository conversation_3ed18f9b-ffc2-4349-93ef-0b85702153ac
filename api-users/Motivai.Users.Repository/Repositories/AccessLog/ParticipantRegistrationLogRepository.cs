using System;
using System.Threading.Tasks;
using MongoDB.Driver;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.IRepository.AccessLog;
using Newtonsoft.Json;

namespace Motivai.Users.Repository.Repositories.AccessLog
{
	public class ParticipantRegistrationLogRepository : IParticipantRegistrationLogRepository
	{
		private readonly IMongoCollection<ParticipantRegistrationLog> registrationsLogs;

		public ParticipantRegistrationLogRepository()
		{
			this.registrationsLogs = MongoDataBaseHelper.GetMongoDatabase()
				.GetCollection<ParticipantRegistrationLog>("UsersRegistrationsActionsLogs");
		}

		public Task RegisterLog(ParticipantRegistrationLog participantRegistrationLog)
		{
			return this.registrationsLogs.InsertOneAsync(participantRegistrationLog);
		}
	}
}