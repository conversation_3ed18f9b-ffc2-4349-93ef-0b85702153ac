using System;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.AccessLog;
using System.Threading.Tasks;
using Motivai.Users.Domain.Enums;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Entities.Integrations;

namespace Motivai.Users.Repository.Repositories.AccessLog
{
	public class AccessLogRepository : IAccessLogRepository
	{
		private readonly MongoDB.Driver.IMongoCollection<LogRegister> _logs;

		public AccessLogRepository()
		{
			var dataBase = MongoDataBaseHelper.GetMongoDatabase();
			this._logs = dataBase.GetCollection<LogRegister>("AccessLogs");
		}

		public Task RegisterLoginFromCallcenter(CallcenterLogin callcenterLogin, UserParticipantModel participantSession,
				LocationInfo locationInfo = null)
		{
			return this._logs.InsertOneAsync(LogRegister.CallcenterLogged(callcenterLogin, participantSession, locationInfo));
		}

		public Task RegisterLogin(LogRegisterAction action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null)
		{
			return this._logs.InsertOneAsync(LogRegister.JustLogged(action, userId, campaignId, document, locationInfo));
		}

		public Task RegisterAccountOperatorLogin(LogRegisterAction action, Guid userId, Guid campaignId, string userDocument,
			Guid accountOperatorId, string operatorDocument, Guid accountOperatorLoginId, LocationInfo locationInfo)
		{
			return this._logs.InsertOneAsync(LogRegister.AccountOperatorLogged(action, userId, campaignId, userDocument,
				accountOperatorId, operatorDocument, accountOperatorLoginId, locationInfo));
		}

		public Task RegisterLogin(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo)
		{
			return this._logs.InsertOneAsync(LogRegister.OfSuccessfulAuthentication(action, participantSession, connectionInfo));
		}
    }
}