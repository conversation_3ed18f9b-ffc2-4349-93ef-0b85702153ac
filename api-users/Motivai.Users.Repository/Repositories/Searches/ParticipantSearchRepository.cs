using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Users.Searches;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Motivai.Users.Repository.Repositories.Searches
{
	public class ParticipantSearchRepository : IParticipantSearchRepository
	{
		private readonly IMongoCollection<User> collection;

		public ParticipantSearchRepository()
		{
			collection = MongoDataBaseHelper.GetMongoDatabase().GetCollection<User>("Users");
		}


		public Task<List<ParticipantInfo>> FindParticipantsByDocument(Guid campaignId, string document)
		{
			var personType = PersonTypeHelper.FromDocument(document);

			var filter = FilterDefinition<User>.Empty;
			if (personType == PersonType.Fisica)
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Cpf, document);
			}
			else
			{
				filter = filter & Builders<User>.Filter.Eq(u => u.Cnpj, document);
			}

			return FindByFilter(filter, campaignId);
		}

		public Task<List<ParticipantInfo>> FindParticipantByLogin(Guid campaignId, string login)
		{
			var filter = Builders<User>.Filter.Eq(u => u.Active, true) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Login, login)
				);
			return FindByFilter(filter, campaignId);
		}

		public Task<List<ParticipantInfo>> FindParticipantByClientUserId(Guid campaignId, string clientUserId)
		{
			var filter = Builders<User>.Filter.Eq(u => u.Active, true) &
				Builders<User>.Filter.ElemMatch(u => u.UsersParticipantCampaign,
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.Active, true) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.CampaignId, campaignId) &
					Builders<UserParticipantCampaign>.Filter.Eq(p => p.ClientUserId, clientUserId)
				);
			return FindByFilter(filter, campaignId);
		}

		private Task<List<ParticipantInfo>> FindByFilter(FilterDefinition<User> filter, Guid campaignId)
		{
			return collection.Aggregate()
				.Match(filter)
				.Unwind("UsersParticipantCampaign")
				.Match(new BsonDocument { { "UsersParticipantCampaign.CampaignId", campaignId } })
				.Project(BuildProjection())
				.As<ParticipantInfo>()
				.ToListAsync();
		}

		private BsonDocument BuildProjection()
		{
			var documentCondition = new BsonDocument {
				{
					"$cond", new BsonDocument {
						{
							"if",
							new BsonDocument { { "$eq", new BsonArray { "$Type", PersonType.Fisica.ToString () } } }
						},
						{ "then", "$Cpf" }, { "else", "$Cnpj" }
					}
				}
			};

			return new BsonDocument {
					{ "UserId", "$_id" },
					{ "CampaignId", "$UsersParticipantCampaign.CampaignId" },
					{ "ParticipantId", "$UsersParticipantCampaign._id" },
					{ "Document", documentCondition },
					{ "MainEmail", "$UsersParticipantCampaign.Contact.MainEmail" },
					{ "Login", "$UsersParticipantCampaign.Login" },
					{ "ClientUserId", "$UsersParticipantCampaign.ClientUserId" },
					{ "Type", "$Type" },
					{ "Cpf", "$Cpf" },
					{ "Rg", "$Rg" },
					{ "Cnpj", "$Cnpj" },
					{ "CompanyName", "$CompanyName" },
					{ "Name", "$Name" },
					{ "MobilePhone", "$UsersParticipantCampaign.Contact.MobilePhone" },
					{ "MainPhone", "$UsersParticipantCampaign.Contact.MainPhone" },
					{ "StateInscriptionExempt", "$StateInscriptionExempt" },
					{ "StateInscriptionUf", "$StateInscriptionUf" },
					{ "StateInscription", "$StateInscription" },
					{ "_id", 0 }
				};
		}
	}
}