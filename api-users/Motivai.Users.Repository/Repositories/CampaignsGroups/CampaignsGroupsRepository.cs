using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IRepository.CampaignsGroups;

namespace Motivai.Users.Repository.Repositories.CampaignsGroups
{
	public class CampaignsGroupsRepository : ICampaignsGroupsRepository
	{

		public async Task<bool> VerifyIfCampaignIsInCoalition(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaignsgroups/campaigns/active/coalition")
				.Query("campaignId", campaignId)
				.AsyncGet()
				.GetApiReturn<bool>();
			if (apiReturn.HasNullReturn())
				return false;
			return apiReturn.GetReturnOrError();
		}

		//api de replicacao
		public async Task<bool> SendParticipantToCampaignsGroupSync(CampaignsGroupsUpdateRequest campaignsGroupsUpdateRequest)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CampaignsGroupsReplicationQueuer)
				.Path("campaigns/participants/replications")
				.AddXApiKeyFromProperties()
				.Entity(campaignsGroupsUpdateRequest)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn.HasNullReturn())
				return false;
			return apiReturn.GetReturnOrError();
		}
	}
}

