using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.AnonymousSession;
using Motivai.Users.Domain.IRepository.AnonymousSession;
using MongoDB.Driver;

namespace Motivai.Users.Repository.Repositories.AnonymousSession
{
	public class AnonymousSessionRepository : IAnonymousSessionRepository
	{
		private readonly IMongoCollection<AnonymousSessionCookieAcceptance> colletion;

		public AnonymousSessionRepository()
		{
			this.colletion = MongoDataBaseHelper.GetMongoDatabase()
				.GetCollection<AnonymousSessionCookieAcceptance>("AnonymousSessionsCookiesAcceptance");
		}

		public Task RegisterCookieAcceptance(AnonymousSessionCookieAcceptance sessionInfo)
		{
			return this.colletion.InsertOneAsync(sessionInfo);
		}
	}
}