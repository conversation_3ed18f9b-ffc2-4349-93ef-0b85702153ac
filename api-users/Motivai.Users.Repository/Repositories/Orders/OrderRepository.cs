using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.Orders;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.MyAccount;
using Newtonsoft.Json;

namespace Motivai.Users.Repository.Repositorys.Orders {
    public class OrderRepository : IOrderRepository {
        public async Task<List<ResumedOrder>> GetOrdersByParticipant(Guid participantId, string status, DateTime? startDate, DateTime? endDate) {
            var httpClient = HttpClient.Create(MotivaiApi.Orders)
                .Path("api/orders/participant").Path(participantId);

            if (startDate.HasValue)
                httpClient.QueryDate("startDate", startDate);
            if (endDate.HasValue)
                httpClient.QueryDate("endDate", endDate);
            if (!string.IsNullOrEmpty(status))
                httpClient.Query("status", status);

            var apiReturn = await httpClient.AsyncGet().GetApiReturn<List<ResumedOrder>>();
            if (apiReturn == null) return null;
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<ResumedOrder>> GetOrdersWithProductsByUserId(Guid userId, string status,
                DateTime? startDate, DateTime? endDate) {
            var httpClient = HttpClient.Create(MotivaiApi.Orders)
                .Path("api/orders/users").Path(userId);

            if (startDate.HasValue)
                httpClient.QueryDate("startDate", startDate);
            if (endDate.HasValue)
                httpClient.QueryDate("endDate", endDate);
            if (!string.IsNullOrEmpty(status))
                httpClient.Query("status", status);

            var apiReturn = await httpClient.AsyncGet()
                    .GetApiReturn<List<ResumedOrder>>();
            if (apiReturn == null) return null;
            return apiReturn.GetReturnOrError();
        }
    }
}