using System;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.IRepository.UsersManagement;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Repository.Repositories.UsersManagement {
    public class UserManagementRepository : IUserManagementRepository {

        private IMongoCollection<User> colletion { get; set; }

        public UserManagementRepository() {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            colletion = dataBase.GetCollection<User>("Users");
        }

        public async Task<User> GetByCpf(Guid campaignId, string document) {
            return await colletion.AsQueryable()
                .Where(u => u.Cpf == document && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
                .Select(u => new User() {
                    Id = u.Id,
                    Name = u.Name,
                    Cpf = u.Cpf,
                    Type = u.Type,
                    UsersParticipantCampaign = u.UsersParticipantCampaign
                })
                .FirstOrDefaultAsync();
        }

        public async Task<User> GetByCnpj(Guid campaignId, string document) {
            return await colletion.AsQueryable()
                .Where(u => u.Cnpj == document && u.UsersParticipantCampaign.Any(p => p.CampaignId == campaignId))
                .Select(u => new User() {
                    Id = u.Id,
                    // Name = u.Name,
                    CompanyName = u.CompanyName,
                    Cnpj = u.Cnpj,
                    Type = u.Type,
                    UsersParticipantCampaign = u.UsersParticipantCampaign
                })
                .FirstOrDefaultAsync();
        }
    }
}