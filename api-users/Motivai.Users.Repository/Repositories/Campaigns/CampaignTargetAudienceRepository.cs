using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IRepository.Camapaigns;

namespace Motivai.Users.Repository.Repositories.Campaigns {
	public class CampaignTargetAudienceRepository : ICampaignTargetAudienceRepository {
		public async Task ExecuteTargetAudienceFor(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.MotivaiAwsServicesApi)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId)
				.AsyncPut()
				.Timeout(30_000)
				.GetApiReturn<bool>();
			if (apiReturn == null) {
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar moeda da campanha.");
			}
			apiReturn.GetReturnOrError();
		}
	}
}