﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.Security;
using Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.Models.CampaignParticipantMigration;

namespace Motivai.Users.Repository.Repositorys.Campaigns
{
	public class CampaignRepository : ICampaignRepository
	{
		private readonly ICache _cache;

		public CampaignRepository(ICache cache)
		{
			this._cache = cache;
		}

		public async Task<CoinName> GetCoinName(Guid campaignId)
		{
			return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_COINNAME + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("coinname")
					.AsyncGet()
					.Timeout(30_000)
					.GetApiReturn<CoinName>();
				if (apiReturn == null)
				{
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar moeda da campanha.");
				}
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<bool> GetActive(Guid campaignId)
		{
			return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_STATUS + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("status")
					.AsyncGet()
					.GetApiReturn<bool>();
				if (apiReturn == null)
				{
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível verificar a situação da campanha.");
				}
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<Guid> GetClientId(Guid campaignId)
		{
			return await _cache.GetOrCreate("campaign-client-" + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("clientid")
					.AsyncGet()
					.GetApiReturn<Guid>();
				if (apiReturn == null)
				{
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o cliente da campanha.");
				}
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<CampaignSettingsModel> GetSettings(Guid campaignId)
		{
			return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_SETTINGS + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("settings/params")
					.AsyncGet()
					.GetApiReturn<CampaignSettingsModel>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<CampaignSecuritySettings> GetSecuritySettings(Guid campaignId)
		{
			return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_SECURITY_SETTINGS + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("settings/security")
					.AsyncGet()
					.GetApiReturn<CampaignSecuritySettings>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações de segurança da campanha.");
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<CampaignIntegrationSettings> GetIntegrationSettings(Guid campaignId)
		{
			return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_INTEGRATION_SETTINGS + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("settings/integration")
					.AsyncGet()
					.GetApiReturn<CampaignIntegrationSettings>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações de Integração da campanha.");
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<string> GetCampaignGroupTargetAccessUrl(Guid sourceCampaignId, Guid targetCampaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaignsgroups/campaigns/access/url")
				.Query("sourceCampaignId", sourceCampaignId)
				.Query("targetCampaignId", targetCampaignId)
				.AsyncGet()
				.GetApiReturn<string>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar a URL da campanha de destino.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> MigrateUserParticipantsGroups(Guid campaignId, CampaignParticipantMigration migration)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("participantsgroups/users/migration")
				.Entity(migration)
				.AsyncPut()
				.GetApiReturn<bool>();

			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar a URL da campanha de destino.");

			return apiReturn.GetReturnOrError();
		}

		public async Task<List<Guid>> GetRankingChildrenById(Guid campaignId, Guid rankingId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("participantsrankings").Path(rankingId).Path("children")
				.AsyncGet()
				.GetApiReturn<List<Guid>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os rankings da campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<Guid>> GetRankingsParents(Guid campaignId, List<Guid> lowestRankings)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId).Path("participantsrankings/parents")
				.Entity(new
				{
					rankings = lowestRankings
				})
				.AsyncPost()
				.GetApiReturn<List<Guid>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os rankings do participante para uso.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<PreRegisterSettings> GetPreRegistrationConfig(Guid campaignId)
		{
			return await _cache.GetOrCreate("campaign-preregister-config-" + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("settings/preregistration")
					.AsyncGet()
					.GetApiReturn<PreRegisterSettings>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações de pré-cadastro da campanha.");
				return apiReturn.GetReturnOrError();
			}, 60);
		}

		public async Task<ParticipantData> GetFirstAccessSettings(Guid campaignId)
		{
			return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_FIRST_ACCESS_SETTINGS + campaignId, async () =>
			{
				var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("settings/firstaccess")
					.AsyncGet()
					.GetApiReturn<ParticipantData>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações do primeiro acesso.");
				return apiReturn.GetReturnOrError();
			});
		}

		public async Task<List<CampaignData>> GetCampaignsByIds(List<Guid> campaignsIds)
		{
			var apiResponse = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns/search")
				.RootBu()
				.Entity(new { campaignsIds })
				.AsyncPost()
				.GetApiReturn<List<CampaignData>>();
			if (apiResponse == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as campanhas.");
			return apiResponse.GetReturnOrError();
		}

		public async Task<bool> AddParticipantToGroups(Guid campaignId, Guid userId, List<string> groupsCodes)
		{
			var apiResponse = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("participantsgroups/identifiers/participants").Path(userId)
				.Entity(new { groupsCodes })
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiResponse == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível adicionar o participante ao grupo.");
			return apiResponse.GetReturnOrError();
		}
		public async Task<ConsultPersonData> GetPersonDataConsultParametrizations(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("processes/parametrizations/consultpersondata")
				.AsyncGet()
				.GetApiReturn<ConsultPersonData>();
			if (apiReturn.HasNullReturn())
				return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task<PrivacySettings> GetCampaignActivePrivacyPolicyForUser(Guid campaignId, Guid userId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("institutional/privacypolicies/active")
				.Query("userId", userId)
				.AsyncGet()
				.GetApiReturn<PrivacySettings>();
			if (apiReturn.HasNullReturn())
				return null;

			return apiReturn.GetReturnOrError();
		}
	}
}
