using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IRepository.Camapaigns;

namespace Motivai.Users.Repository.Repositories.Campaigns {
	public class CampaignGoalsRepository : ICampaignGoalsRepository {
		public async Task<bool> ConfigureParticipantGoals(Guid campaignId, Guid userId, Guid participantId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.CampaignsParticipantsGoalsParametrizationsApi)
				.Path("campaigns").Path(campaignId).Path("users").Path(userId)
				.Path("participants").Path(participantId).Path("goals")
				.AsyncPut()
				.Timeout(30_000)
				.GetApiReturn<bool>();
			if (apiReturn == null) {
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar moeda da campanha.");
			}
			return apiReturn.GetReturnOrError();
		}
	}
}