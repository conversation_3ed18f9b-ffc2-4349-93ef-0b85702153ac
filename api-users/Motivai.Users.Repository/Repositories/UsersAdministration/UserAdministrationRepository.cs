﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.UsersAdministrations;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Motivai.SharedKernel.Helpers;
using System.Collections.Generic;

namespace Motivai.Users.Repository.Repositorys.UsersAdministration
{
	public class UserAdministrationRepository : IUserAdministrationRepository
	{
		private readonly IMongoCollection<UserAdministration> _administrators;

		public UserAdministrationRepository()
		{
			var dataBase = MongoDataBaseHelper.GetMongoDatabase();
			_administrators = dataBase.GetCollection<UserAdministration>("UsersAdministration");
		}

		public Task<UserAdministration> Get(List<Guid> bus, Guid userId)
		{
			return _administrators.AsQueryable()
				.Where(u => u.Id == userId && bus.Contains(u.BuId))
				.FirstOrDefaultAsync();
		}

		public Task<UserAdministration> Get(Guid userId)
		{
			return _administrators.AsQueryable()
				.Where(u => u.Id == userId)
				.FirstOrDefaultAsync();
		}

		public Task<string> GetUserName(Guid userId)
		{
			return _administrators.AsQueryable()
				.Where(u => u.Id == userId)
				.Select(u => u.Name)
				.FirstOrDefaultAsync();
		}

		public async Task<UserAdministration> Get(string login)
		{
			return await _administrators.AsQueryable()
				.Where(u => u.Login == login)
				.FirstOrDefaultAsync();
		}

		public async Task<bool> IsLoginIsUseByOtherThan(string login, Guid userAdminId)
		{
			return await _administrators.AsQueryable()
				.Where(u => u.Login == login && u.Id != userAdminId)
				.CountAsync() > 0;
		}

		public async Task<List<UserAdministration>> GetUsers(List<Guid> bus, string name, string email, string login, int? skip, int? limit)
		{
			var query = _administrators.AsQueryable()
				.Where(x => x.Active && bus.Contains(x.BuId));

			if (!string.IsNullOrEmpty(name))
				query = query.Where(x => x.Name.ToLower().Contains(name.ToLower()));
			if (!string.IsNullOrEmpty(email))
				query = query.Where(x => x.Email.ToLower() == email.ToLower());
			if (!string.IsNullOrEmpty(login))
				query = query.Where(x => x.Login.ToLower() == login.ToLower());
			if (skip.HasValue)
				query = query.Skip(skip.Value);
			if (limit.HasValue)
				query = query.Take(limit.Value);

			return await query
				.OrderBy(u => u.Name)
				.Select(u => new UserAdministration()
				{
					Id = u.Id,
					Name = u.Name,
					Email = u.Email,
					Login = u.Login,
					RoleId = u.RoleId,
					Active = u.Active,
					CreateDate = u.CreateDate
				})
				.ToListAsync();
		}

		public async Task<bool> Create(UserAdministration user)
		{
			await this._administrators.InsertOneAsync(user);
			return true;
		}

		public async Task<bool> Save(UserAdministration user)
		{
			await this._administrators.ReplaceOneAsync(r => r.Id == user.Id, user);
			return true;
		}

		public async Task<bool> Delete(Guid userId)
		{
			var user = await _administrators.AsQueryable()
				.Where(u => u.Id == userId).FirstOrDefaultAsync();
			if (user == null) return false;
			user.Active = false;
			await _administrators.ReplaceOneAsync(u => u.Id == userId, user);
			return !user.Active;
		}
	}
}
