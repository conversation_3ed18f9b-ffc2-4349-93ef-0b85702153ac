using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog;
using Motivai.Users.Domain.IRepository.UsersAdministrations.ActionLog;
using MongoDB.Driver;

namespace Motivai.Users.Repository.Repositories.UsersAdministration.Action
{
    public class UserAdministrationActionLogRepository : IUserAdministrationActionLogRepository
    {
        private readonly IMongoCollection<UserAdministrationActionLog> actionsLogs;

        public UserAdministrationActionLogRepository()
        {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            actionsLogs = dataBase.GetCollection<UserAdministrationActionLog>("UsersAdministrationActionsLogs");
        }

        public Task RegisterLog(UserAdministrationActionLog actionLog)
        {
            return actionsLogs.InsertOneAsync(actionLog);
        }
    }
}