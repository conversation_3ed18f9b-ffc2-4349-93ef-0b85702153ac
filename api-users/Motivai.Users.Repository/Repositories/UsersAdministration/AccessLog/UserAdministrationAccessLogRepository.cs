using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog;
using Motivai.Users.Domain.IRepository.UsersAdministrations.AccessLog;
using MongoDB.Driver;

namespace Motivai.Users.Repository.Repositories.UsersAdministration.AccessLog
{
    public class UserAdministrationAccessLogRepository : IUserAdministrationAccessLogRepository
    {
        private readonly IMongoCollection<UserAdministrationAccessLog> accessLogs;

        public UserAdministrationAccessLogRepository()
        {
            var dataBase = MongoDataBaseHelper.GetMongoDatabase();
            accessLogs = dataBase.GetCollection<UserAdministrationAccessLog>("UsersAdministrationAccessesLogs");
        }

        public Task RegisterLog(UserAdministrationAccessLog userAdministrationLog)
        {
            return accessLogs.InsertOneAsync(userAdministrationLog);
        }
    }
}