(
  {
    "UsersParticipantCampaign._id" : 1
  }
)

(
  {
    "_id" : 1,
    "UsersParticipantCampaign.CampaignId" : 1
  },
  {
    "name" : "Users_By_UserId_And_CampaignId"
  }
)

// Códigos dos Participantes
(
    {
        "UsersParticipantCampaign.CampaignId": 1,
        "UsersParticipantCampaign.ClientUserId": 1
    },
    {
        name: "UsersParticipantCampaign.CampaignId_1_ClientUserId_1",
        background: true,
        // sparse: true,
        // unique: true,
        partialFilterExpression: {
            'UsersParticipantCampaign.ClientUserId': {
                $exists: true,
                $type: "string"
            }
        }
    }
)

// Logs
(
  {
    "CampaignId" : 1
  },
  {
    "v" : 2,
    "name" : "CampaignId_1",
    "ns" : "motivaidb.AccessLogs",
    "background" : true
  }
)
(
  {
    "CampaignId" : 1,
    "UserId" : 1
  },
  {
    "v" : 2,
    "name" : "CampaignId_1_UserId_1",
    "ns" : "motivaidb.AccessLogs",
    "background" : true
  }
)
(
  {
    "CampaignId" : 1,
    "AccessDate" : 1
  },
  {
    "v" : 2,
    "name" : "CampaignId_1_AccessDate_1",
    "ns" : "motivaidb.AccessLogs"
  }
)