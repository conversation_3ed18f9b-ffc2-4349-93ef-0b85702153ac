FROM mcr.microsoft.com/dotnet/core/sdk:3.1 AS build
WORKDIR /src

ARG GH_USERNAME
ARG GH_TOKEN

ENV GH_USERNAME=${GH_USERNAME}
ENV GH_TOKEN=${GH_TOKEN}

COPY NuGet.Config ./
COPY Motivai.Users.sln ./
COPY ./Motivai.Users.Domain/ Motivai.Users.Domain/
COPY ./Motivai.Users.Repository/ Motivai.Users.Repository/
COPY ./Motivai.Users.App/ Motivai.Users.App/
COPY ./Motivai.Users.Api/ Motivai.Users.Api/

RUN dotnet restore ./Motivai.Users.Api/Motivai.Users.Api.csproj

WORKDIR /src/Motivai.Users.Api
RUN dotnet build Motivai.Users.Api.csproj -c Release -o /app/build
RUN dotnet publish Motivai.Users.Api.csproj -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/core/aspnet:3.1
WORKDIR /app
EXPOSE 80
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "Motivai.Users.Api.dll"]