using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Users.Domain.Enums;

namespace Motivai.Users.Domain.ex
{
    [Serializable]
    public class AuthenticantionException : Exception
    {
		public AuthenticationActionLog Action { get; set; }
		public AuthenticantionException(AuthenticationActionLog Action, string message) : base(message)
		{
			this.Action = Action;
		}
    }
}