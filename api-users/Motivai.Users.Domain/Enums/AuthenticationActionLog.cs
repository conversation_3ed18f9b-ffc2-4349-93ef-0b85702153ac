using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Users.Domain.Enums
{
    [JsonConverter(typeof(StringEnumConverter))]
    public enum AuthenticationActionLog
    {
        ACCOUNT_OPERATOR_LOGIN_WITH_USER,
        ACCOUNT_OPERATOR_LOGIN_WITH_PASSWORD,
        PLATFORM_SSO_LOGIN_STARTED,
        PLATFORM_SSO_LOGIN_ENDED,
        LOGIN_FROM_SITE,
        LOGIN_WITH_CALLCENTER,
        LOGIN_WITH_PASSWORD,
        LOGIN_WITH_SSO,
        BLOCKED_USER,
        PARTICIPANT_BLOCKED,
        PARTICIPANT_CANNOT_LOGIN,
        PARTICIPANT_INACTIVE,
        INACTIVE_CAMPAIGN,
        INVALID_CAMPAIGN,
        INVALID_ACESSIBLE_ACCOUNT,
        INVALID_ACCOUNT_OPERATOR,
        INVALID_ACCOUNT_OPERATOR_LOGIN,
        INACTIVE_ACCOUNT_OPERATOR,
        INACTIVE_ACCOUNT_OPERATOR_LOGIN,
        BLOCKED_ACCOUNT_OPERATOR_LOGIN,
        INVALID_SSO_TOKEN,
        INVALID_PASSWORD,
        INVALID_PARTICIPANT,
        INVALID_CAMPAIGN_TOKEN,
        INVALID_CAMPAIGN_SETTINGS,
        INVALID_USER,
        INACTIVE_USER,
        VR_ID_SSO,
        BLOCK_LISTED_IP
    }

    public static class AuthencationActionLogHelper
    {
        public static AuthenticationActionLog FromErrorCode(string errorCode)
        {
            switch (errorCode)
            {
                case "PARTICIPANT_INACTIVE":
                    return AuthenticationActionLog.PARTICIPANT_INACTIVE;
                case "PARTICIPANT_BLOCKED":
                    return AuthenticationActionLog.PARTICIPANT_BLOCKED;
                case "PARTICIPANT_CANNOT_LOGIN":
                    return AuthenticationActionLog.PARTICIPANT_CANNOT_LOGIN;
                default:
                    return AuthenticationActionLog.INVALID_PARTICIPANT;
            }
        }

        public static AuthenticationActionLog MapActionToAuthenticationActionLog(this LogRegisterAction action)
        {
            switch (action)
            {
                case LogRegisterAction.LoginWithPassword:
                    return AuthenticationActionLog.LOGIN_WITH_PASSWORD;
                case LogRegisterAction.LoginWithSSO:
                    return AuthenticationActionLog.LOGIN_WITH_SSO;
                case LogRegisterAction.PlatformSsoLoginStarted:
                    return AuthenticationActionLog.PLATFORM_SSO_LOGIN_STARTED;
                case LogRegisterAction.PlatformSsoLoginEnded:
                    return AuthenticationActionLog.PLATFORM_SSO_LOGIN_ENDED;
                case LogRegisterAction.LoginWithCallcenter:
                    return AuthenticationActionLog.LOGIN_WITH_CALLCENTER;
                case LogRegisterAction.LoginFromSite:
                    return AuthenticationActionLog.LOGIN_FROM_SITE;
                case LogRegisterAction.AccountOperatorLoginWithPassword:
                    return AuthenticationActionLog.ACCOUNT_OPERATOR_LOGIN_WITH_PASSWORD;
                case LogRegisterAction.AccountOperatorLoginWithUser:
                    return AuthenticationActionLog.ACCOUNT_OPERATOR_LOGIN_WITH_USER;
                case LogRegisterAction.VrIdSso:
                    return AuthenticationActionLog.VR_ID_SSO;
                default:
                    return AuthenticationActionLog.INVALID_USER;
            }
        }
    }
}
