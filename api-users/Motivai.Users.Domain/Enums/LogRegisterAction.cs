using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Users.Domain.Enums {
    [JsonConverter(typeof(StringEnumConverter))]
    public enum LogRegisterAction {
        LoginWithPassword,
        LoginWithSSO,
        ///<summary>
        /// Inicio do SSO da Motivai.
        ///</summary>
        PlatformSsoLoginStarted,
        ///<summary>
        /// Fim do SSO da Motivai.
        ///</summary>
        PlatformSsoLoginEnded,
        ///<summary>
        /// Login através do Call Center.
        ///</summary>
        LoginWithCallcenter,
        ///<summary>
        /// Login integrado no catálogo a partir do site campanha.
        ///</summary>
        LoginFromSite,
        ///<summary>
        /// Login integrado no catálogo a partir do parceiro.
        ///</summary>
        LoginWithIntegration,
        LoginFromMobile,
        ///<summary>
        /// Login do operador da conta utilizando login e senha.
        ///</summary>
        AccountOperatorLoginWithPassword,
        ///<summary>
        /// Operador selecionou o user que deseja acessar.
        ///</summary>
        AccountOperatorLoginWithUser,
         ///<summary>
        /// Login via integracao do Operador VR.
        ///</summary>
        VrIdSso
    }
}
