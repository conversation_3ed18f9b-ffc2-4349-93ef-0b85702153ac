

using System;
using Motivai.Users.Domain.Entities.AccountOperators.Actions;

namespace Motivai.Users.Domain.Models.AccountOperators  {
    public class UpdateDataAccountOperator: UsersAccountsOperatorsDataActionsHistory {
        public string FieldValue { get; set; }

        public UsersAccountsOperatorsDataActionsHistory ToEntity() {
            return new UsersAccountsOperatorsDataActionsHistory().CopyFrom(this);
        }
    }
}