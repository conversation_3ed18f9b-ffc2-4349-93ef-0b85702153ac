﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Motivai.Users.Domain.Models.Transactions {
    public class ExpiringPointsSummary {
        public List<MechanicsDetails> Mechanics { get; set; }
        public decimal TotalPoints { get; set; }
    }

    public class MechanicsDetails {
        public string Description { get; set; }
        public decimal TotalPoints { get; set; }
        public decimal AvailablePoints { get; set; }
        public decimal BlockedPoints { get; set; }
        public decimal UsedPoints { get; set; }
        public decimal ExpiringPoints { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }

    public class BlockedPointsDetails {
        public string Description { get; set; }
        public DateTime ProcessingDate { get; set; }
        public decimal Amount { get; set; }
    }
}