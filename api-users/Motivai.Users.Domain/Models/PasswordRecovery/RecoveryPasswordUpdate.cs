using System;
using System.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.PasswordRecovery {
    public class RecoveryPasswordUpdate {
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }

        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }

        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }

        public bool IsAccountOperator() {
            return AccountOperatorId != Guid.Empty && AccountOperatorLoginId != Guid.Empty;
        }

        public void Validate() {
            if (UserId == Guid.Empty && AccountOperatorId == Guid.Empty)
                throw MotivaiException.ofValidation("Participate inválido.");

            NewPassword.ForNullOrEmpty("Informe a nova senha");
            ConfirmPassword.ForNullOrEmpty("Confirme a nova senha");

            if (!NewPassword.Equals(ConfirmPassword))
            {
                throw MotivaiException.ofValidation("A confirmação de senha deve ser igual a nova senha");
            }

            if (!NewPassword.Any(char.IsDigit))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo um número");
            }

            if (!NewPassword.Any(char.IsLower))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma letra minúscula");
            }

            if (!NewPassword.Any(char.IsUpper))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma letra maiúscula");
            }
        }
    }
}