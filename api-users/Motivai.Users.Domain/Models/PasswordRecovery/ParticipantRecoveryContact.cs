using System;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccountOperators;

namespace Motivai.Users.Domain.Models.PasswordRecovery
{
    public class ParticipantRecoveryContact
    {
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }

        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }
        public AuthenticationAccess AuthenticationAccess { get; set; }

        public string Name { get; set; }
        public string Email { get; set; }
        public string MobilePhone { get; set; }

        public static ParticipantRecoveryContact OfParticipant(UserParticipantCampaign participant)
        {
            return new ParticipantRecoveryContact()
            {
                UserId = participant.UserId,
                ParticipantId = participant.Id,
                Email = participant.GetMainEmail(),
                MobilePhone = participant.GetMobilePhone()
            };
        }

        public static ParticipantRecoveryContact OfAccountOperator(AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin)
        {
            return new ParticipantRecoveryContact()
            {
                AccountOperatorId = accountOperator.Id,
                AccountOperatorLoginId = accountOperatorLogin.Id,
                Name = accountOperator.Name,
                Email = accountOperatorLogin.Email,
                MobilePhone = accountOperatorLogin.MobilePhone,
                AuthenticationAccess = accountOperatorLogin.AuthenticationAccess
            };
        }
    }
}
