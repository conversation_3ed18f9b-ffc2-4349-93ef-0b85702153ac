using System;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.PasswordRecovery {
    public class RecoveryTokenIssue {
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }

        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }

        public SecurityTokenSendMethod SendForm { get; set; }

        public bool IsAccountOperator() {
            return AccountOperatorId != Guid.Empty && AccountOperatorLoginId != Guid.Empty;
        }

        public SecurityTokenSendMethod GetSendMethod() {
            return IsBySms() ? SecurityTokenSendMethod.SMS : SecurityTokenSendMethod.EMAIL;
        }

        public bool IsBySms() {
            return SendForm == SecurityTokenSendMethod.SMS;
        }

        public void Validate() {
            if (UserId == Guid.Empty && AccountOperatorId == Guid.Empty)
                throw MotivaiException.ofValidation("Participate inválido.");

            if (SendForm != SecurityTokenSendMethod.EMAIL && SendForm != SecurityTokenSendMethod.SMS)
                throw MotivaiException.ofValidation("Forma de envio do token de segurança inválido.");
        }
    }
}