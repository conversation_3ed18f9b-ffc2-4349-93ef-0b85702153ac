﻿using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.PasswordRecovery {
    public class PasswordRecoveryRequest {
        public string Login { get; set; }
        public string Email { get; set; }

        public void Validate() {
            if (string.IsNullOrEmpty(Login))
                throw MotivaiException.ofValidation("Login é obrigatório.");
            if (string.IsNullOrEmpty(Email))
                throw MotivaiException.ofValidation("E-mail é obrigatório.");
        }
    }
}