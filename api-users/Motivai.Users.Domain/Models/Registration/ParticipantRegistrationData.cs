using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.Models.Registration {
    public class ParticipantRegistrationData {
        public string Document { get; set; }

        public string CompanyName { get; set; }
        public string TradeName { get; set; }
        public bool StateInscriptionExempt { get; set; }
        public string StateInscription { get; set; }
        public string StateInscriptionUf { get; set; }
        public AccountRepresentative AccountRepresentative { get; set; }
        public Address BusinessAddress { get; set; }
        public Contact Contact { get; set; }

        public bool GpInfo { get; set; }
        public bool GpPartnerInfo { get; set; }

        public dynamic Metadata { get; set; }

        public static ParticipantRegistrationData Of(User user, UserParticipantCampaign participant) {
            Address address = null;
            if (participant.Addresses != null) {
                address = participant.Addresses.FirstOrDefault(a => a.AddressName == "Endereço Comercial");
                if (address == null) {
                    address = participant.Addresses.FirstOrDefault(a => a.MainAddress);
                }
            }
            return new ParticipantRegistrationData() {
                Document = user.GetDocument(),
                    CompanyName = user.CompanyName,
                    TradeName = user.Name,
                    BusinessAddress = address,
                    Contact = participant.Contact,
                    GpInfo = user.GpInf,
                    GpPartnerInfo = user.GpPartnerInf
            };
        }

        public static ParticipantRegistrationData Of(User user, UserParticipantCampaign participant, UserMetadataValue usersMetadata) {
            Address address = null;
            if (participant.Addresses != null) {
                address = participant.Addresses.FirstOrDefault(a => a.AddressName == "Endereço Comercial");
                if (address == null) {
                    address = participant.Addresses.FirstOrDefault(a => a.MainAddress);
                }
            }

            dynamic metadata = new {};
             if (usersMetadata != null) {
                    metadata = usersMetadata.Metadata;
            }

            return new ParticipantRegistrationData() {
                Document = user.GetDocument(),
                    CompanyName = user.CompanyName,
                    TradeName = user.Name,
                    BusinessAddress = address,
                    Contact = participant.Contact,
                    GpInfo = user.GpInf,
                    GpPartnerInfo = user.GpPartnerInf,
                    Metadata = metadata
            };
        }

        public void Validate() {
            if (string.IsNullOrEmpty(Document)) {
                throw MotivaiException.ofValidation("CPF/CNPJ é obrigatório.");
            } else if (!Cpf.IsCpf(Document) && !Cnpj.IsCnpj(Document)) {
                throw MotivaiException.ofValidation("CPF/CNPJ inválido.");
            }
            if (string.IsNullOrEmpty(CompanyName))
                throw MotivaiException.ofValidation("Razão social é obrigatória.");
            if (string.IsNullOrEmpty(TradeName))
                throw MotivaiException.ofValidation("Nome fantasia é obrigatório.");

            if (BusinessAddress == null)
                throw MotivaiException.ofValidation("Preencha o endereço comercial.");
            BusinessAddress.Validate();

            if (Contact == null)
                throw MotivaiException.ofValidation("Preencha os dados de contato.");
            Contact.Validate();
        }
    }
}