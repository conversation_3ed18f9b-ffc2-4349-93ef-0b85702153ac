﻿using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Models.PrivacyPolicy;

namespace Motivai.Users.Domain.Models.FirstAccess
{
    public class FirstAccessAcceptancesModel
	{
		public bool KeepFirstAccess { get; set; }

		public bool AcceptedCampaignCommunications { get; set; }
		public bool AcceptedPartnerCommunications { get; set; }

		public CampaignAcceptancesResult CampaignAcceptancesResult { get; set; }

		public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }

		public LocationInfo LocationInfo { get; set; }
	}

	public class FirstAccessDataModel
	{
		public Guid Id { get; set; }

		public bool KeepFirstAccess { get; set; }

		#region Pessoa Física

		public bool IsPessoaFisica { get; set; }
		public string Name { get; set; }
		public string Cpf { get; set; }
		public string Rg { get; set; }
		public string BirthDate { get; set; }
		public string MaritalStatus { get; set; }
		public string Gender { get; set; }

		#endregion

		#region Pessoa Jurídica

		public bool IsPessoaJuridica { get; set; }
		public string Cnpj { get; set; }
		public bool StateInscriptionExempt { get; set; }
		public string StateInscription { get; set; }
		public string StateInscriptionUf { get; set; }
		public string CompanyName { get; set; }
		public AccountRepresentative AccountRepresentative { get; set; }

		#endregion

		public Address HomeAddress { get; set; }
		public Address CommercialAddress { get; set; }
		public Contact Contact { get; set; }

		public string Password { get; set; }
		public string PasswordConfirmation { get; set; }

		public bool AcceptedCampaignCommunications { get; set; }
		public bool AcceptedPartnerCommunications { get; set; }

		public CampaignAcceptancesResult CampaignAcceptancesResult { get; set; }

		public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }

		#region Compatibilidade para Remover o Futuro

		public string RegulationId { get; set; }
		public string Version { get; set; }
		public IList<AcceptanceTerms> AcceptanceTerms { get; set; }

		public PrivacyPolicyResult PrivacyPolicy { get; set; }

		#endregion

		public LocationInfo LocationInfo { get; set; }

		public string GetDocument()
		{
			return IsPessoaFisica ? Cpf : Cnpj;
		}

		public void Validate()
        {
            if (IsPessoaJuridica)
            {
                Cnpj.ForNullOrEmptyDefaultMessage("CNPJ");
            }
            else if (IsPessoaFisica)
            {
                Cpf.ForNullOrEmptyDefaultMessage("CPF");
            }

			if (CampaignAcceptancesResult == null)
			{
				CampaignAcceptancesResult = new CampaignAcceptancesResult
				{
					RegulationAcceptance = new RegulationAcceptanceResult
					{
						RegulationId = RegulationId,
						Version = Version,
						AcceptanceTerms = AcceptanceTerms
					},
					PrivacyPolicy = this.PrivacyPolicy
				};
			}
        }
	}

	public class CampaignAcceptancesResult
	{
		public RegulationAcceptanceResult RegulationAcceptance { get; set; }
		public PrivacyPolicyResult PrivacyPolicy { get; set; }
	}

	public class RegulationAcceptanceResult
	{
		public string RegulationId { get; set; }
		public string Version { get; set; }
		public IList<AcceptanceTerms> AcceptanceTerms { get; set; }
	}
}
