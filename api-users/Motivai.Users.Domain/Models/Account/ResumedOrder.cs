﻿using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Orders;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Users.Domain.Models.MyAccount
{
    public class ResumedOrder
    {
        public string Id { get; set; }
        public OrderType OrderType { get; set; }
        public string InternalOrderNumber { get; set; }
        public DateTime CreationDate { get; set; }
        public decimal TotalAmountPoints { get; set; }
        public OrderStatus Status { get; set; }
        public List<PaymentMethods> PaymentMethods{ get; set; }

        public List<OrderItemResumed> Items { get; set; }
    }

    public class OrderItemResumed
    {
        public string SkuCode { get; set; }
        public string ProductName { get; set; }
        public string ProductImageUrl { get; set; }
    }

    public class PaymentMethods {
        public string Code { get; set; }
        public string Type { get; set; }
    }

    public class ResumedCardOrder
    {
        public string Id { get; set; }
        public DateTime CreateDate { get; set; }
        public string Timezone { get; set; }
        public string OrderNumber { get; set; }
        public string Status { get; set; }
        public Amount OrderTotalCost { get; set; }

        public OrderStatus GetMappedStatus()
        {
            switch (Status)
            {
                case "CANCELED": return OrderStatus.Canceled;
                case "ERROR": return OrderStatus.Error;
                case "SHIPPING": return OrderStatus.Shipping;
                case "DELIVERED":
                case "INTEGRATED":
                    return OrderStatus.Delivered;
                default: return OrderStatus.Processed;
            }
        }

        public ResumedOrder ToResumedOrder()
        {
            return new ResumedOrder()
            {
                Id = this.Id,
                OrderType = OrderType.CARD_ORDER,
                InternalOrderNumber = this.OrderNumber,
                CreationDate = this.CreateDate,
                TotalAmountPoints = this.OrderTotalCost.GetPointsOrZero(),
                Status = this.GetMappedStatus()
            };
        }
    }

    public class ResumedCashbackOrder
    {
        public string Id { get; set; }
        public DateTime CreateDate { get; set; }
        public string Timezone { get; set; }
        public string OrderNumber { get; set; }
        public string Status { get; set; }
        public Amount TotalCost { get; set; }

        public OrderStatus GetMappedStatus()
        {
            switch (Status)
            {
                case "CANCELED": return OrderStatus.Canceled;
                case "ERROR": return OrderStatus.Error;
                case "TRANSFERRED":
                    return OrderStatus.Delivered;
                default: return OrderStatus.Processed;
            }
        }

        public ResumedOrder ToResumedOrder()
        {
            return new ResumedOrder()
            {
                Id = this.Id,
                OrderType = OrderType.CASHBACK_ORDER,
                InternalOrderNumber = this.OrderNumber,
                CreationDate = this.CreateDate,
                TotalAmountPoints = this.TotalCost.GetPointsOrZero(),
                Status = this.GetMappedStatus()
            };
        }
    }

    public class ResumedExtraServiceOrder
    {
        public string Id { get; set; }
        public DateTime SystemDate { get; set; }
        public bool Confirmed { get; set; }
        public string Protocol { get; set; }
        public string Type { get; set; }
        public dynamic CreditRecharge { get; set; }
        public dynamic BillPayment { get; set; }
        public DeteiledCost DeteiledCost { get; set; }

        public OrderStatus GetMappedStatus()
        {
            switch (Confirmed)
            {
                case true: return OrderStatus.Processed;
                case false: return OrderStatus.Processing;
                default: return OrderStatus.Processing;
            }
        }

        public ResumedOrder ToResumedOrder()
        {
            return new ResumedOrder()
            {
                Id = this.Id,
                OrderType = Type == "BILLPAYMENTS" ? OrderType.BILL_PAYMENT_ORDER : OrderType.RECHARGE_ORDER,
                InternalOrderNumber = this.Protocol,
                CreationDate = this.SystemDate,
                TotalAmountPoints = this.DeteiledCost != null ? this.DeteiledCost.ParticipantCost.GetPointsOrZero() : 0,
                Status = this.GetMappedStatus()
            };
        }
    }

    public class DeteiledCost
    {
        public Amount ParticipantCost { get; set; }
    }
}