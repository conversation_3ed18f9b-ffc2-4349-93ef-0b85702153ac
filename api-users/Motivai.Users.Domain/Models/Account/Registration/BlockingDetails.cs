using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.Enums;

namespace Motivai.Users.Domain.Models.Account.Registration
{
    public class UserBlockingRequest
    {
        public BlockingOrigin Origin { get; set; }

        public string Reason { get; set; }
        public OperationUser OperationUser { get; set; }

        public BlockingDetails ToBlockingDetails()
        {
            return new BlockingDetails()
            {
                Reason = Reason,
                OperationUser = OperationUser
            };
        }
    }
}