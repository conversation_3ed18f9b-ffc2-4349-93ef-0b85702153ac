using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;

namespace Motivai.Users.Domain.Models.MyAccount.Registration {
    public class AddressUpdate : Address {
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public Address ToEntity() {
            var address = new Address();
            address.CopyFrom(this);
            return address;
        }
    }
}