using System;
using System.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.MyAccount
{
    public class PasswordModel
    {
        public string OldPassword { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }

        public void Validate()
        {
            OldPassword.ForNullOrEmpty("Informe a senha antiga");
            NewPassword.ForNullOrEmpty("Informe a nova senha");
            ConfirmPassword.ForNullOrEmpty("Confirme a nova senha");

            if (!NewPassword.Equals(ConfirmPassword))
            {
                throw MotivaiException.ofValidation("A confirmação de senha deve ser igual a nova senha.");
            }

            if (!NewPassword.Any(char.IsDigit))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo um número");
            }

            if (!NewPassword.Any(char.<PERSON>))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma letra minúscula");
            }

            if (!NewPassword.Any(char.IsUpper))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma letra maiúscula");
            }
        }
    }
}