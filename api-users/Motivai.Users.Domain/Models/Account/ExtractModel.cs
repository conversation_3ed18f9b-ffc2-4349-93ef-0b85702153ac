﻿using System;
using System.Collections.Generic;

namespace Motivai.Users.Domain.Models.MyAccount {
	public class ExtractModel {
		public List<ExtractRegister> Transactions { get; set; }
		public decimal TotalAmount { get; set; }
	}

	public class ExtractRegister {
		public DateTime ProcessingDate { get; set; }
		public string Description { get; set; }
		public decimal Amount { get; set; }
		public bool? Blocked { get; set; }
		public dynamic ExtraData { get; set; }
	}
}