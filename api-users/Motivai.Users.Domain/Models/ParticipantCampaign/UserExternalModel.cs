using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.Models.ParticipantCampaign
{
	public class UserExternalModel
	{
		///<summary>
		/// Documento do participante (CPF ou CNPJ).
		///</summary>
		public string Document { get; set; }
		public PersonType PersonType { get; set; }
		///<summary>
		/// Recebe via payload apenas internamente quando utilizado em scripts.
		///</summary>
		public string Password { get; set; }

		public string Name { get; set; }
		public string Rg { get; set; }
		public DateTime? BirthDate { get; set; }

		public string CompanyName { get; set; }
		public bool? StateInscriptionExempt { get; set; }
		public string StateInscriptionUf { get; set; }
		public string StateInscription { get; set; }

		public string Telephone { get; set; }
		public string Cellphone { get; set; }
		public string Email { get; set; }

		public Address Address { get; set; }

		public string ClientUserId { get; set; }
		public ParentParticipant ParentParticipant { get; set; }
		public IntegrationCompany IntegrationCompany { get; set; }

		public List<string> GroupsCodes { get; set; }

		public Dictionary<string, string> Metadata { get; set; }

        public bool HasParent()
        {
            return !string.IsNullOrEmpty(ParentParticipant?.Document);
        }

        public bool HasMetadata()
        {
            return Metadata != null && Metadata.Count > 0;
        }

		public void Validate()
		{
			Document.ForNullOrEmpty("CPF/CNPJ é obrigatório.");

			RemoveMasks();

			PersonType = PersonTypeHelper.FromDocument(Document);

			if (Address != null)
			{
				Address.Validate();
			}
		}

		public bool HasCompanyIntegrationData()
		{
			return IntegrationCompany != null && (!string.IsNullOrEmpty(IntegrationCompany.CompanyName) || !string.IsNullOrEmpty(IntegrationCompany.CompanyIdentifier));
		}

		public void RemoveMasks()
		{
			Document = Extractor.RemoveMasks(Document);
			Rg = Extractor.RemoveMasks(Rg);
			if (Address != null)
			{
				Address.Cep = Extractor.RemoveMasks(Address.Cep);
			}
			StateInscription = Extractor.RemoveMasks(StateInscription);
			Telephone = Extractor.RemoveMasks(Telephone);
			Cellphone = Extractor.RemoveMasks(Cellphone);
			if (HasParent())
			{
				ParentParticipant.Document = Extractor.RemoveMasks(ParentParticipant.Document);
			}
		}
    }
}