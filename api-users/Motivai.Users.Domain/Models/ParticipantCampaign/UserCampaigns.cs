using System;
using System.Collections.Generic;

namespace Motivai.Users.Domain.Models.ParticipantCampaign {
    public class UserCampaigns {
        public Guid userId { get; set; }
        public string Name { get; set; }
        public string Document { get; set; }
        public bool Active { get; set; }

        public List<CampaignData> Campaigns { get; set; }
    }

    public class CampaignData {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Login { get; set; }
        public bool Active { get; set; }
    }
}