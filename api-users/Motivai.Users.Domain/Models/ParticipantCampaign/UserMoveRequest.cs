using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.ParticipantCampaign
{
	public class UserMoveRequest
	{
		public Guid UserId { get; set; }
		public string UserDocument { get; set; }

		public OperationUser OperationUser { get; set; }

		public UserMoveRequest() { }

		public UserMoveRequest(Guid targetUserId, string targetUserDocument)
		{
			this.UserId = targetUserId;
			this.UserDocument = targetUserDocument;
		}

		public void Validate()
		{
			if (UserId == Guid.Empty && string.IsNullOrEmpty(UserDocument))
			{
				throw MotivaiException.ofValidation("Preencha o CPF/CNPJ do participante de destino.");
			}
		}
	}
}