using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.ParticipantCampaign
{
    public class OperatorAuthenticationModel
    {
        public string Origin { get; set; }
        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }
        public Guid UserId { get; set; }
        public string Timezone { get; set; }
        public ConnectionInfo ConnectionInfo { get; set; }
        public bool SkipBalance { get; set; }

        public void Validate()
        {
            if (AccountOperatorId == Guid.Empty || AccountOperatorLoginId == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Operador de conta inválido.");
            }
            if (UserId == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Selecione a conta que deseja acessar.");
            }
        }
    }
}
