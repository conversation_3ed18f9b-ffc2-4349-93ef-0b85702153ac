using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;

namespace Motivai.Users.Domain.Models.ParticipantCampaign {
    public class UserCampaignsInfo {
        public Guid UserId { get; set; }
        public string Document { get; set; }
        public string Name { get; set; }
        public string CompanyName { get; set; }

        public Address MainAddress { get; set; }

        public bool Active { get; set; }
        public bool Blocked { get; set; }
        public List<Guid> Campaigns { get; set; }

        public void ValidateName() {
            if (!string.IsNullOrEmpty(CompanyName)) {
                Name = CompanyName;
            }
        }
    }
}