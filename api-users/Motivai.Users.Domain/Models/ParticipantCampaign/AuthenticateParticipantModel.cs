using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers;
using Motivai.Users.Domain.Entities.Wallets.Devices;

namespace Motivai.Users.Domain.Models.ParticipantCampaign
{
	public class ParticipantLoginModel
	{
		public string Origin { get; set; }
		public string Login { get; set; }
		public string Password { get; set; }
		public bool SkipBalance { get; set; }

		public string Timezone { get; set; }
		public ConnectionInfo ConnectionInfo { get; set; }

		public DeviceRequestInfo DeviceRequestInfo { get; set; }

		public LocationInfo GetLocationInfo()
		{
			return LocationInfo.Of(Timezone, ConnectionInfo);
		}

		public void Validate()
		{
			Login.ForNullOrEmpty("Deve ser informado o Login do Usuário.");
			Password.ForNullOrEmpty("Deve ser informado a Senha do Usuário.");
		}
    }
}