﻿using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Auth;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccountOperators;

namespace Motivai.Users.Domain.Models.ParticipantCampaign
{
	public class UserParticipantModel
	{
		public Guid? SessionId { get; set; }
        public string SessionOrigin { get; set; }

		public Guid UserId { get; set; }
		public Guid CampaignId { get; set; }
		public Guid ParticipantId { get; set; }

		public string Name { get; set; }
		public string Document { get; set; }
		public PersonType Type { get; set; }
		public string Email { get; set; }
		public string MobilePhone { get; set; }
		public string Timezone { get; set; }

		public string PictureUrl { get; set; }
		public decimal Balance { get; set; }

		public bool FirstAccess { get; set; }
		public bool NeedAuthenticationMfa { get; set; }
		public bool NeedSetupAuthenticationMfa { get; set; }
		///<summary>
		/// Define se o participante precisa passar pelo fluxo de aceite da política de privacidade.
		///</summary>
		public bool NeedAcceptPrivacyPolicy { get; set; }

		public Guid? RankingId { get; set; }

		public Guid? AccountOperatorId { get; set; }
		public Guid? AccountOperatorLoginId { get; set; }
		public string AccountOperatorDocument { get; set; }
		public string AccountOperatorEmail { get; set; }

		///<summary>
		/// Utilizado no login de operadores da conta do participante.
		/// Após a validação de senha será selecionado um participante que tenha acesso.
		///</summary>
		public List<AccessibleAccount> AccessibleAccounts { get; set; }

		public AuthenticationAccess AuthenticationAccess { get; set; }

        public UserParticipantModel()
		{
			SessionId = Guid.NewGuid();
		}

		public string GetEmail()
		{
			if (!string.IsNullOrEmpty(AccountOperatorEmail))
				return AccountOperatorEmail;
			return Email;
		}

		public static UserParticipantModel Of(Guid campaignId, User user, UserParticipantCampaign participant,
			string sessionOrigin, string timezone, CampaignSettingsModel campaignSettings)
		{
			return new UserParticipantModel
			{
				SessionOrigin = sessionOrigin,
				UserId = user.Id,
				CampaignId = campaignId,
				ParticipantId = participant.Id,
				Name = user.GetName(),
				Document = user.GetDocument(),
				Type = user.Type,
				Email = participant.GetMainEmail(),
				PictureUrl = user.PhotoUrl,
				Balance = participant.Balance,
				FirstAccess = campaignSettings.Parametrizations.EnableFirstAccess && participant.FirstAccess,
				NeedAuthenticationMfa = campaignSettings.Parametrizations.EnableAuthenticationMfa,
				NeedSetupAuthenticationMfa = campaignSettings.Parametrizations.EnableAuthenticationMfa && participant.NeedSetupAuthenticationMfa(),
				NeedAcceptPrivacyPolicy = participant.IsPrivacyPolicyPendingToAccept(),
				RankingId = participant.RankingId,
				Timezone = timezone
			};
		}

		public static UserParticipantModel FromIntegrationResult(AuthenticationResult authenticationResult)
		{
			return new UserParticipantModel()
			{
				UserId = authenticationResult.Participant.UserId,
				ParticipantId = authenticationResult.Participant.ParticipantId,
				CampaignId = authenticationResult.Participant.CampaignId,
				Name = authenticationResult.Participant.Name,
				Email = authenticationResult.Participant.MainEmail,
				Type = authenticationResult.Participant.Type,
				FirstAccess = authenticationResult.FirstAccess,
				Balance = 0
			};
		}

		public static UserParticipantModel OfExternalAccess(AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin)
		{
			return new UserParticipantModel()
			{
				CampaignId = accountOperatorLogin.CampaignId,
				AccountOperatorId = accountOperator.Id,
				Name = accountOperator.Name,
				AccountOperatorLoginId = accountOperatorLogin.Id,
				AccountOperatorDocument = accountOperator.Document,
				AccountOperatorEmail = accountOperatorLogin.Email,
				AccessibleAccounts = accountOperatorLogin.GetValidAccessibleAccounts(),
				AuthenticationAccess = accountOperatorLogin.AuthenticationAccess
			};
		}

		public bool IsAccountOperator()
		{
			return AccountOperatorId.HasValue && AccountOperatorLoginId.HasValue;
		}

		public void SetAccountOperator(AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin)
		{
			this.AccountOperatorId = accountOperator.Id;
			this.Name = accountOperator.Name;
			this.AccountOperatorLoginId = accountOperatorLogin.Id;
			this.AccountOperatorDocument = accountOperator.Document;
			this.AccountOperatorEmail = accountOperatorLogin.Email;
			this.AuthenticationAccess = accountOperatorLogin.AuthenticationAccess;
		}

		public void SetAccountOperatorForCampaignGroup(AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin)
		{
			this.AccountOperatorId = accountOperator.Id;
			this.Name = accountOperator.Name;
			this.AccountOperatorLoginId = accountOperatorLogin.Id;
			this.AccountOperatorDocument = accountOperator.Document;
			this.AccountOperatorEmail = accountOperatorLogin.Email;

			if (accountOperatorLogin.AuthenticationAccess != null)
				accountOperatorLogin.AuthenticationAccess.Migrated = false;

			this.AuthenticationAccess = accountOperatorLogin.AuthenticationAccess;
		}

		public void SetAccountOperator(ResumedAccountOperator accountOperator)
		{
			this.AccountOperatorId = accountOperator.AccountOperatorId;
			this.AccountOperatorLoginId = accountOperator.AccountOperatorLoginId;
			this.AccountOperatorDocument = accountOperator.Document;
			this.AccountOperatorEmail = accountOperator.Email;
		}
    }

}
