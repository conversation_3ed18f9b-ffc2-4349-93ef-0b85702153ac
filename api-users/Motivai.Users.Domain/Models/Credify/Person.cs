using System;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.Users.Domain.Models.Credify {
    public class Person {
        public string Name { get; set; }
        public PersonType Type { get; set; }
        public string Document { get; set; }
        public DateTime? BirthDate { get; set; }
        public string Gender { get; set; }
        public string MotherName { get; set; }
        public string Sign { get; set; }
        public string Age { get; set; }
        public dynamic MainAddress { get; set; }
        public string CompanyName { get; set; }
        public string NatureDescription { get; set; }
        public string NatureCode { get; set; }
    }
}