using System;

namespace Motivai.Users.Domain.Models.UserAdministration
{
    public class UserAdministrationAuthenticationResponse
    {
        public Guid Id { get; set; }
        public string Login { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public Guid RoleId { get; set; }
        public Guid BuId { get; set; }
        public Guid RegionId { get; set; }
        public bool ChangePassword { get; set; }
        public string StartComponent { get; set; }
        public string LayoutType { get; set; }

        public override string ToString()
        {
            return $"Id = {Id}, Login = {Login}, Name = {Name}, Email = {Email}, RoleId = {RoleId}, BuId = {BuId}, RegionId = {RegionId}, ChangePassword = {ChangePassword}, StartComponent = {StartComponent}, LayoutType = {LayoutType}";
        }
    }
}
