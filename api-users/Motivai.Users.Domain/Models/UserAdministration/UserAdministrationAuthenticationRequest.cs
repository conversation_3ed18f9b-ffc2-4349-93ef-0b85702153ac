using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.UserAdministration
{
    public class UserAdministrationAuthenticationRequest
    {
        public string Login { get; set; }
        public string Password { get; set; }

        public LocationInfo LocationInfo { get; set; }

        public void Validate()
        {
            Login.ForNullOrEmpty("Deve ser informado o Login.");
            Password.ForNullOrEmpty("Deve ser informado a Senha.");
            if (LocationInfo == null)
                throw MotivaiException.ofValidation("Requisição inválida.");
        }
    }
}
