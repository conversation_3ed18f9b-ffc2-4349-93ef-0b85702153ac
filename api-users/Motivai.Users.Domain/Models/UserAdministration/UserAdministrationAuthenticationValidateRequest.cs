using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Models.UserAdministration
{
    public class UserAdministrationAuthenticationValidateRequest
    {
        public Guid SessionId { get; set; }
        public string SecurityCode { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public void Validate()
        {
            if (SessionId == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Sessão inválida.");
            }
            SecurityCode.ForNullOrEmpty("Código de verificação inválido.");
            if (LocationInfo == null)
                throw MotivaiException.ofValidation("Requisição inválida.");
        }
    }
}
