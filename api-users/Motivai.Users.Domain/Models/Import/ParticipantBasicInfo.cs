using System;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Validators;
using Motivai.SharedKernel.Helpers.Values;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.Models.Import {
    public class ParticipantBasicInfo {
        public Guid? UserId { get; set; }
        public Guid? ParticipantId { get; set; }

        public string Document { get; set; }

        public string Name { get; set; }
        public string CompanyName { get; set; }

        public string Email { get; set; }
        public string Telephone { get; set; }

        public string MobilePhone { get; set; }

        public string Cellphone {
            get {
                return MobilePhone;
            }
            set {
                this.MobilePhone = value;
            }
        }

        public DateTime? BirthDate { get; set; }
        public string Gender { get; set; }

        public Address Address { get; set; }

        // Se foi importado proveniente de um evento específico
        public CampaignEventType? EventType { get; set; }
        // Se o evento gerou algum ponto
        public decimal? Points { get; set; }

        public static ParticipantBasicInfo Of(User user, Guid campaignId) {
            var basicInfo = new ParticipantBasicInfo() {
                UserId = user.Id,
                Document = user.GetDocument(),
                Name = user.Name,
                CompanyName = user.CompanyName
            };
            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant != null) {
                basicInfo.ParticipantId = participant.Id;
                basicInfo.Email = participant.GetMainEmail();
                basicInfo.MobilePhone = participant.GetMobilePhone();
            }
            return basicInfo;
        }

        public void Validate() {
            RemoveMasks();
            if (string.IsNullOrEmpty(Document))
                throw MotivaiException.ofValidation("Documento do participante é obrigatório.");
            if (!Cpf.IsCpf(Document) && !Cnpj.IsCnpj(Document))
                throw MotivaiException.ofValidation("Documento do participante é inválido.");
        }

        public void RemoveMasks() {
            if (Document != null)
                Document = Document.Replace(".", "").Replace("-", "").Replace("/", "").Replace(" ", "");
            if (string.IsNullOrEmpty(Email)) {
                Email = null;
            } else {
                Email = Email.Trim();
                EmailValidator.IsValidEmail(Email, "E-mail inválido.");
            }
            if (string.IsNullOrEmpty(Telephone)) {
                Telephone = null;
            } else {
                Telephone = Telephone.Replace("(", "").Replace(")", "").Replace("-", "").Replace(" ", "");
                TelephoneHelper.ValidMobilePhone(Telephone);
            }
        }

        public bool HasAnyContactInfo() {
            return !string.IsNullOrEmpty(Email) || !string.IsNullOrEmpty(Telephone) || !string.IsNullOrEmpty(Cellphone);
        }

        public bool HasEvent() {
            return EventType.HasValue;
        }
    }
}