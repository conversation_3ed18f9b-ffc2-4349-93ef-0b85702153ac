using System;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Integrations;

namespace Motivai.Users.Domain.Models.Import {
	public class CreatedParticipant {
		public Guid CampaignId { get; set; }
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }

		public string Name { get; set; }
		public string Email { get; set; }
		public string MobilePhone { get; set; }

		public string Login { get; set; }
		public string Password { get; set; }

		///<summary>
		/// Se foi importado proveniente de um evento específico.
		///</summary>
		public CampaignEventType? EventType { get; set; }
		///<summary>
		/// Se o evento gerou algum ponto.
		///</summary>
		public decimal? Points { get; set; }

		public static CreatedParticipant Of(User user, UserParticipantCampaign participant, string generatedPassword) {
			return new CreatedParticipant() {
				CampaignId = participant.CampaignId,
				UserId = user.Id,
				ParticipantId = participant.Id,
				Name = user.Name,
				Email = participant.GetMainEmail(),
				MobilePhone = participant.GetMobilePhone(),
				Login = participant.Login,
				Password = generatedPassword
			};
		}

		public static CreatedParticipant Of(User user, UserParticipantCampaign participant, ParticipantIntegrationData participantData) {
			return new CreatedParticipant() {
				CampaignId = participant.CampaignId,
				UserId = user.Id,
				ParticipantId = participant.Id,
				Name = user.Name,
				Email = participant.GetMainEmail(),
				MobilePhone = participant.GetMobilePhone(),
				Login = participant.Login,
				Password = participantData.Password,
				EventType = participantData.EventType,
				Points = participantData.Points
			};
		}

		public bool HasEvent() {
			return EventType.HasValue;
		}
	}
}