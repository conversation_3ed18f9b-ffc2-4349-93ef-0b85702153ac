using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.Models.UserParticipant
{
    public class UserDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Document { get; set; }
        /// Razao Social
        public string CompanyName { get; set; }
        // PF
        public DateTime BirthDate { get; set; }
        public List<UserParticipantCampaignDTO> UsersParticipantCampaign { get; set; }

        public static UserDTO Of(User user){
            UserDTO userDTO = new UserDTO();

            userDTO.Id = user.Id;
            userDTO.Name = user.Name;
            userDTO.Document = user.Document;
            userDTO.CompanyName = user.CompanyName;
            userDTO.BirthDate = user.BirthDate;
            userDTO.UsersParticipantCampaign = UserDTO.MapUserParticipantCampaign(user.UsersParticipantCampaign);

            return userDTO;
        }

        private static List<UserParticipantCampaignDTO> MapUserParticipantCampaign(List<UserParticipantCampaign> userParticipantCampaignList)
        {
            return userParticipantCampaignList
                .Select(p => UserParticipantCampaignDTO.Of(p))
                .ToList();
        }
    }
}
