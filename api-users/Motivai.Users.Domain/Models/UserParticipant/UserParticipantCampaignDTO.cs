using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.Models.UserParticipant
{
    public class UserParticipantCampaignDTO
    {
        public Guid CampaignId { get; set; }
        public Contact Contact { get; set; }
        public List<Address> Addresses { get; set; }

        public static UserParticipantCampaignDTO Of(UserParticipantCampaign participant){
            UserParticipantCampaignDTO participantDTO = new UserParticipantCampaignDTO();

            participantDTO.CampaignId = participant.CampaignId;
            participantDTO.Contact = participant.Contact;
            participantDTO.Addresses = participant.Addresses;

            return participantDTO;
        }
    }
}
