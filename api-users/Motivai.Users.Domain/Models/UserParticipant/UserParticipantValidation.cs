using System;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.Models.UserParticipant
{
	public class UserParticipantValidation
	{
		public static bool CheckForContactChanges(Contact currentContact, Contact newContact)
		{
			return CheckFieldUpdate(currentContact, newContact, c => c.MainEmail) ||
				CheckFieldUpdate(currentContact, newContact, c => c.PersonalEmail) ||
				CheckFieldUpdate(currentContact, newContact, c => c.CommercialEmail) ||
				CheckFieldUpdate(currentContact, newContact, c => c.MobilePhone) ||
				CheckFieldUpdate(currentContact, newContact, c => c.MainPhone) ||
				CheckFieldUpdate(currentContact, newContact, c => c.CommercialPhone) ||
				CheckFieldUpdate(currentContact, newContact, c => c.HomePhone);
		}

		public static void CopyContactFields(UserParticipantCampaign participant, Contact newContact)
		{
			if (participant.Contact == null)
				participant.Contact = new Contact();

			var currentContact = participant.Contact;

			if ((currentContact.UserId == null || currentContact.UserId == Guid.Empty) && (newContact.UserId != null && newContact.UserId != Guid.Empty))
				currentContact.UserId = newContact.UserId;

			if ((currentContact.ParticipantId == null || currentContact.ParticipantId == Guid.Empty) && (newContact.ParticipantId != null && newContact.ParticipantId != Guid.Empty))
				currentContact.ParticipantId = newContact.ParticipantId;

			if ((currentContact.CampaignId == null || currentContact.CampaignId == Guid.Empty) && (newContact.CampaignId != null && newContact.CampaignId != Guid.Empty))
				currentContact.CampaignId = newContact.CampaignId;

			if (CheckFieldUpdate(currentContact, newContact, c => c.MobileOperator))
			{
				currentContact.MobileOperator = newContact.MobileOperator;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.TalkTo))
			{
				currentContact.TalkTo = newContact.TalkTo;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.MainEmail))
			{
				currentContact.MainEmail = newContact.MainEmail;
				currentContact.MainEmailLastUpdateDate = DateTime.UtcNow;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.PersonalEmail))
			{
				currentContact.PersonalEmail = newContact.PersonalEmail;
				currentContact.PersonalEmailLastUpdateDate = DateTime.UtcNow;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.CommercialEmail))
			{
				currentContact.CommercialEmail = newContact.CommercialEmail;
				currentContact.CommercialEmailLastUpdateDate = DateTime.UtcNow;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.MobilePhone))
			{
				currentContact.MobilePhone = newContact.MobilePhone;
				currentContact.MobilePhoneLastUpdateDate = DateTime.UtcNow;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.MainPhone))
			{
				currentContact.MainPhone = newContact.MainPhone;
				currentContact.MainPhoneLastUpdateDate = DateTime.UtcNow;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.CommercialPhone))
			{
				currentContact.CommercialPhone = newContact.CommercialPhone;
				currentContact.CommercialPhoneLastUpdateDate = DateTime.UtcNow;
			}
			if (CheckFieldUpdate(currentContact, newContact, c => c.HomePhone))
			{
				currentContact.HomePhone = newContact.HomePhone;
				currentContact.HomePhoneLastUpdateDate = DateTime.UtcNow;
			}
		}

		private static bool CheckFieldUpdate<T>(Contact currentContact, Contact newContact, Func<Contact, T> getField)
		{
			if (currentContact == null && newContact != null)
				return true;
			if (newContact == null)
				return false;

			T currentFieldValue = getField(currentContact);
			T newFieldValue = getField(newContact);

			if (currentFieldValue == null && newFieldValue != null)
				return true;

			return (currentFieldValue != null && newFieldValue != null) && (currentFieldValue.ToString().Trim() != newFieldValue.ToString().Trim());
		}
	}
}