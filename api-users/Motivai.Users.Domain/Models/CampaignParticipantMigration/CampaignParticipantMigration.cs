using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Motivai.Users.Domain.Models.CampaignParticipantMigration
{
    public class CampaignParticipantMigration
    {
        public Guid SourceUserId { get; set; }
        public Guid TargetUserId { get; set; }

        public CampaignParticipantMigration(Guid sourceUserId, Guid targetUserId)
        {
            this.SourceUserId = sourceUserId;
            this.TargetUserId = targetUserId;
        }
    }
}
