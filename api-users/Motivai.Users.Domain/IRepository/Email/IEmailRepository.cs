
using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.Models.Security;

namespace Motivai.Users.Domain.IRepository.Email
{
	public interface IEmailRepository
	{
		Task<bool> SendSecurityCodeByMethod(Guid userId, Guid campaignId, string name, string email, string mobilePhone, SecurityCodeSendMethod sendMethod, string securityCode);
		Task<bool> SendNewParticipantNotification(Guid campaignId, Guid userId, string name, string email, string mobilePhone, string login, string password);
		Task<bool> SendToken(Guid campaignId, Guid userId, string name, string email, string mobilePhone, SecurityTokenSendMethod sendMethod, string token);
		Task<bool> SendResetPasswordEmail(Guid campaignId, Guid userId, Guid participantId, string generatedPassword);
		Task<bool> SendResetPasswordEmail(Guid campaignId, Guid userId, string name, string email, string mobilePhone, string login, string generatedPassword);
		Task<bool> SendNewParticipantNotificationByEvent(Guid campaignId, CampaignEventType eventType, Guid userId, string name,
			string email, string mobilePhone, string login, string tempPassword, decimal points);
		Task<bool> SendLoginNotification(AccountLoginNotification loginNotification);
		Task<bool> NotifyPasswordReseted(Guid campaignId, Guid userId, string name, string login, string newPassword, string email, string mobilePhone);
		Task<bool> SendFirstAccessNotification(Guid campaignId, Guid userId, string name, string email, string mobilePhone);
		Task<bool> SendAdminToken(Guid userId, string name, string email, SecurityTokenSendMethod sendMethod, string token);
		Task<bool> SendPlataformActionNotification(dynamic notification);
	}
}