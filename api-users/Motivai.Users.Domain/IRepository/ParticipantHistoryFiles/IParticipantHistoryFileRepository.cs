using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.ParticipantHistoryFiles;

namespace Motivai.Users.Domain.IRepository.ParticipantHistoryFiles {
    public interface IParticipantHistoryFileRepository {
        Task<bool> Save(Guid UserId, Guid CampaignId, ParticipantHistoryFile participantHistoryFile);
        Task<List<ParticipantHistoryFile>> GetByParticipant(Guid UserId, Guid CampaignId, bool active = false);
        Task<bool> Unactive(Guid userId, Guid CampaignId, List<ParticipantHistoryFile> participantHistoryFiles);
    }
}