﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.Users.Domain.Models.Transactions;

namespace Motivai.Users.Domain.IRepository.Transactions {
	public interface ITransactionApiRepository {
		Task<decimal> GetBalance(Guid campaignId, Guid userId);
		Task<decimal> GetBalanceByRankings(Guid campaignId, Guid id, List<Guid> rankings);
		Task<decimal> GetReservedBalanceFor(Guid userId, Guid campaignId, Product product);
		Task<PointsSummaryModel> GetFirstPointsToExpire(Guid campaignId, Guid userId);
		Task<PointsSummaryModel> GetFirstPointsToUnblock(Guid campaignId, Guid userId);
		Task<List<TransactionDetailsModel>> GetLastAccumulations(Guid campaignId, Guid userId);
		Task<List<TransactionDetailsModel>> GetLastRedemptions(Guid campaignId, Guid userId);
		Task<ExpiringPointsSummary> GetExpiringPoints(Guid campaignId, Guid userId);
		Task<BlockedPointsSummary> GetPointsBlocked(Guid campaignId, Guid userId);
		Task<TransactionsExtractModel> GetExtract(Guid campaignId, Guid userId, Guid? participantId,
			TransactionType? transactionType, TransactionOrigin? transactionOrigin,
			DateTime? startDate, DateTime? endDate, int? skip, int? limit);
		Task<TransactionsExtractModel> GetExtractWithBlockedTransactions(Guid campaignId, Guid userId, Guid? participantId,
			TransactionType? transactionType, TransactionOrigin? transactionOrigin,
			DateTime? startDate, DateTime? endDate, int? skip, int? limit);
		Task<TransactionsExtractModel> GetBlockedTransactionsExtract(Guid campaignId, Guid userId, Guid? participantId,
			TransactionType? transactionType, TransactionOrigin? transactionOrigin,
			DateTime? startDate, DateTime? endDate, int? skip, int? limit);
	}
}