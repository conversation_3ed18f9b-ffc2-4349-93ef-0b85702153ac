﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.Security;
using Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.Models.CampaignParticipantMigration;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IRepository.Camapaigns
{
	public interface ICampaignRepository
	{
		Task<CoinName> GetCoinName(Guid campaignId);
		Task<bool> GetActive(Guid campaignId);
		Task<Guid> GetClientId(Guid campaignId);
		Task<CampaignSettingsModel> GetSettings(Guid campaignId);
		Task<CampaignIntegrationSettings> GetIntegrationSettings(Guid campaignId);
		Task<string> GetCampaignGroupTargetAccessUrl(Guid sourceCampaignId, Guid targetCampaignId);
		Task<List<Guid>> GetRankingsParents(Guid campaignId, List<Guid> lowestRankings);
		Task<List<Guid>> GetRankingChildrenById(Guid campaignId, Guid rankingId);
		Task<PreRegisterSettings> GetPreRegistrationConfig(Guid campaignId);
		Task<ParticipantData> GetFirstAccessSettings(Guid campaignId);
		Task<List<CampaignData>> GetCampaignsByIds(List<Guid> campaignsIds);
		Task<CampaignSecuritySettings> GetSecuritySettings(Guid campaignId);
		Task<bool> MigrateUserParticipantsGroups(Guid campaignId, CampaignParticipantMigration userMigrationIds);
		Task<bool> AddParticipantToGroups(Guid campaignId, Guid userId, List<string> groupsCodes);
		Task<ConsultPersonData> GetPersonDataConsultParametrizations(Guid campaignId);

		Task<PrivacySettings> GetCampaignActivePrivacyPolicyForUser(Guid campaignId, Guid userId);
	}
}
