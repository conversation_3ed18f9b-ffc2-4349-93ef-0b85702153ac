using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums.Orders;
using Motivai.Users.Domain.Models.MyAccount;

namespace Motivai.Users.Domain.IRepository.ExtraServices {
    public interface ICampaignExtraServicesRepository {
        Task<List<ResumedExtraServiceOrder>> GetExtraServicesOrdersByParticipant(Guid campaignId, Guid userId, string status, DateTime? startDate, DateTime? endDate);
    }
}