using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;

namespace Motivai.Users.Domain.IRepository.Users.Searches
{
	public interface IParticipantSearchRepository
	{
		Task<List<ParticipantInfo>> FindParticipantsByDocument(Guid campaignId, string document);
		Task<List<ParticipantInfo>> FindParticipantByLogin(Guid campaignId, string login);
		Task<List<ParticipantInfo>> FindParticipantByClientUserId(Guid campaignId, string clientUserId);
	}
}