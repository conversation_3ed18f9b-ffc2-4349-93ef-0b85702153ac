using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.AccountOperators.Authentication;
using Motivai.Users.Domain.Models.AccountOperators;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IRepository.Users.AccountOperators
{
	public interface IAccountOperatorRepository
	{
		Task<AccountOperator> FindAccountOperatorByDocumentAndCampaign(string document, Guid campaignId);
		Task<AccountOperator> GetById(Guid accountOperatorId);
		Task<List<AccountOperator>> GetAccountOperators(Guid userId, Guid campaignId);
		Task<AccountOperator> FindAccountOperatorByDocument(string document);
		Task<AccountOperator> GetAccountOperatorLoginByCampaignAndLogin(Guid campaignId, string login);
		Task<AccountOperator> GetAccountOperatorByCampaignEmailAndLogin(Guid campaignId, string email, string login);
		Task<bool> VerifyDifferentOperatorByEmail(Guid campaignId, string currentDocument, string email);
		Task Create(AccountOperator accountOperator);
		Task<bool> Update(AccountOperator accountOperator);
		Task<AccountOperator> FindAccountOperatorBy(string document, Guid campaignId, Guid userId);
		Task<bool> InsertAccountOperatorLogin(Guid accountOperatorId, AccountOperatorLogin accountOperatorLogin);
		Task<bool> UpdateAccountOperatorLoginEmail(Guid accountOperatorId, Guid accountOperatorLoginId, UpdateDataAccountOperator updateDataAccountOperator);
		Task<bool> UpdateAccountOperatorLoginRole(Guid userId, Guid accountOperatorId, Guid accountOperatorLoginId, OperatorRole operatorRole);
		Task<bool> UpdateAccountOperatorLoginsByCampaigns(AccountOperator accountOperator, List<Guid> campaignIds);
		Task<AccountOperator> FindAccountOperatorByEmailAndDocument(Guid campaignId, AccountOperatorSsoRequest operatorRequest);
		Task<List<OperatorAcessibleAccounts>> FindAcessibleAccountsBy(string document, Guid campaignId);
	}
}
