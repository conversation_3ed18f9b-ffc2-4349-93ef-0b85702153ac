﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.CreditCards;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Models.PrivacyPolicy;

namespace Motivai.Users.Domain.IRepository.Users
{
	public interface IUserRepository
	{
		Task<User> Get(Guid userId);
		Task<User> GetUserForSession(Guid userId, Guid campaignId);
		Task<bool> IsActive(Guid userId);
		Task<UserCampaigns> GetUserCampaignsByDocument(PersonType type, string document, bool onlyActive = true);
        Task<ParticipantAuthenticationMfaSettings> FindAuthenticationMfaSettings(Guid campaignId, Guid userId);
        Task<User> GetDocumentByParticipantId(Guid participantId);
		Task Save(User user, bool skipValidation = false);
		Task CreateUser(User user, bool skipValidation = false);
		Task<bool> CreateParticipant(Guid userId, UserParticipantCampaign participant);
		Task<bool> SetUpdateDateToNow(Guid userId, Guid campaignId);
		Task<bool> UpdateRegistrationData(User user, UserParticipantCampaign participant, bool skipValidation = false, bool isFromAdmin = false);
		Task<bool> UpdateContactInfo(Guid campaignId, Guid userId, UserParticipantDataModel participantData);
		Task<bool> UpdateFirstAccessData(User user, UserParticipantCampaign participant, ParticipantData firstAccessSettings);
		Task<bool> UpdateFirstAccessFlag(User user, UserParticipantCampaign participant);
		Task<bool> ResetFirstAccess(Guid userId, Guid participantId);
		Task<bool> SaveAddress(Guid userId, Guid participantId, List<Address> address);
		Task<bool> UpdateAddress(Guid userId, Guid participantId, Address address);
		Task<bool> UpdatePassword(Guid userId, Guid campaignId, string password);
		Task<bool> UpdateAccess(UserParticipantCampaign user);
		Task<bool> UpdateLastAccess(Guid userId, Guid campaignId, string currentTimezone, decimal? currentBalance = default(decimal?));
		Task<bool> UpdateParticipantLoginAttempts(Guid userId, Guid campaignId, int loginAttempts, BlockingDetails blockDetails, bool blockParticipant = false);
		Task<bool> ResetParticipantLoginAttempts(Guid userId, Guid campaignId);
        Task<bool> UpdateAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa);
		Task<bool> SetPrivacySetttings(Guid userId, Guid campaignId, PrivacySettings privacySettings);
		Task<bool> UpdatePrivacySettings(Guid userid, Guid campaignId, PrivacyPolicyResult privacyPolicyResult);
		Task<bool> UpdatePrivacySetttings(Guid userId, Guid campaignId, bool privacyPolicyAccepted,
				bool acceptedCampaignCommunications, bool acceptedPartnerCommunications);
        Task<User> GetUserAndParticipant(Guid userId, Guid campaignId);
		Task<User> GetUserByCampaignAndLogin(Guid campaignId, string login);
		Task<User> AuthenticateByCampaignLoginAndPassword(Guid campaignId, string login, string password);
		Task<User> AuthenticateByDocumentAndCampaignPassword(string login, Guid campaignId, string password);

		Task<Guid> GetUserIdByDocument(string document, Guid campaignId);
		Task<User> GetUserByDocumentAndCampaign(PersonType personType, string document, Guid campaignId);
		Task<User> GetByDocument(string document);
		Task<User> GetByCpf(string cpf);
		Task<User> GetByCnpj(string cnpj);

		Task<User> GetUserByParticipantId(Guid participantId);
		Task<UserParticipantCampaign> GetParticipantByCampaign(Guid userId, Guid campaignId);
		Task<UserParticipantCampaign> GetParticipantByDocument(Guid campaignId, string document);
		Task<Guid> GetParticipantIdByUserAndCampaign(Guid userId, Guid campaignId);
		Task<List<User>> GetByCampaignAndName(Guid campaignId, string name);
		Task<User> GetByLogin(Guid campaignId, string login, bool onlyActive = true);
		///<summary>
		/// Deprecear: não utiliza o ID da campanha
		///</summary>
		Task<User> GetByLogin(string login, bool onlyActive = true);
		Task<List<User>> GetByCampaign(Guid campaignId);
		Task<ParticipantInfo> GetUserData(Guid userId, Guid campaignId);
		Task<dynamic> GetDocumentBy(Guid userId);
		Task<List<User>> GetByBusinessTypeAndName(UserBusinessType businessType, string name);
		Task<List<UserCampaignsInfo>> GetUsersByListOfIds(List<Guid> usersIds);
		Task<List<UserCampaignsInfo>> GetUsersInfoByIds(Guid campaignId, List<Guid> usersIds);
		Task<bool> UpdateUserExternal(Guid id, Guid participantId, User user, UserParticipantCampaign userParticipantCampaign);
		Task<UserBasicInfo> GetUserBasicInfoByDocument(Guid campaignId, string document);
		Task<UserBasicInfo> GetUserBasicInfoByClientUserId(Guid campaignId, string clientUserId);
		Task<UserBasicInfo> GetUserBasicInfo(Guid campaignId, Guid userId);
		Task<bool> UnblockingParticipant(Guid userId, Guid campaignId, UnblockingParticipantDetails unblockingRequest);
		Task<bool> BlockParticipant(Guid userId, Guid campaignId, BlockingDetails blockingDetails, BlockingOrigin blockingOrigin);
		Task<bool> UpdateCreditCardBilling(Guid userId, Guid creditCardId, UserCreditCardBilling userCreditCardBilling);
		Task<AccountRepresentative> GetAccountRepresentative(Guid userId, Guid campaignId);
		Task<bool> ExistUserWithDocument(Guid userId, string document);
		Task<bool> ExistParticipantWithLogin(Guid campaignId, string login);
		Task<bool> AddUserAppCard(Guid userId, Guid campaignId, Card userCard);
		Task<List<Card>> GetAppCards(Guid userId, Guid campaignId, bool onlyActive);
		Task<List<Card>> GetActiveAppCards(Guid userId, Guid campaignId, List<string> cardCodes);
		Task<bool> ResetAppCards(Guid campaignId, Guid userId, List<string> cardCodes);
		Task<List<User>> GetUserChildrenParticipants(Guid userId, Guid campaignId);
		Task<bool> UpdateUserAppCards(Guid userId, Guid campaignId, List<Card> cards);
	}
}
