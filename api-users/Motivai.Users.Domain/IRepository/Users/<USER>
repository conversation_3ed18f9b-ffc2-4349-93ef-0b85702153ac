using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IRepository.Users
{
	public interface IUserMetadataValueRepository
	{
		Task<UserMetadataValue> GetUsersMetadataValue(Guid campaignId, Guid userId);
		Task<string> GetUserMetadataFieldValue(Guid userId, Guid campaignId, string fieldKey);
		Task CreateUserMetadata(UserMetadataValue userMetadata);
		Task<bool> SaveUserMetadata(Guid userId, Guid campaignId, UserMetadataValue userMetadata);
	}
}