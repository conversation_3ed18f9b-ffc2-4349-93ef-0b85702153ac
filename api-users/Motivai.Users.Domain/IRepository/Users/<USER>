using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.Configurations.Security;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.Users.Domain.Entities.Wallets.Devices;

namespace Motivai.Users.Domain.IRepository.Users {
    public interface IUserConfigurationRepository {
        Task SaveSecurityCode(Guid userId, GeneratedSecurityCode generatedCode);
        Task<GeneratedSecurityCode> GetSecurityCodeForValidation(Guid userId);
        Task InvalidateSecurityCode(Guid userId);

        Task<bool> HasTransactionalPasswordConfigured(Guid userId);
        Task<string> GetTransactionalPassword(Guid userId);
        Task<bool> ConfigureTransactionalPassword(Guid userId, TransactionalPassword transactionalPassword);

        Task<bool> CreateRegisteredDevice(Guid userId, RegisteredDevice registeredDevice);
        Task<RegisteredDevice> GetDeviceById(Guid userId, string deviceId);
        Task<RegisteredDevice> GetActiveDevice(Guid userId, string deviceId);
        Task<bool> VerifyAuthorization(Guid userId, DeviceRequestInfo deviceRequestInfo);
    }
}