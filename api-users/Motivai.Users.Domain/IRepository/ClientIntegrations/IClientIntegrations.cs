using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using PreRegisteredUser = Motivai.SharedKernel.Domain.Entities.References.Users.PreRegisteredUser;

namespace Motivai.Users.Domain.IRepository.ClientIntegrations
{
	public interface IClientIntegrations
	{
		Task<ParticipantInfo> GetParticipantInfo(Guid clientId, string identifier);
		Task<UserParticipantModel> AuthenticateUsingIntegratedPassword(Guid campaignId, ParticipantLoginModel user);
		Task<bool> SendPreRegister(Guid campaignId, PreRegisteredUser preRegisteredUser);
		Task<bool> UpdateRegistrationData(Guid campaignId, PreRegisteredUser user);
	}
}