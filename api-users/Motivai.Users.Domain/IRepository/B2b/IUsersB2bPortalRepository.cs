using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IRepository.B2b {
    public interface IUsersB2bPortalRepository {
        Task<UserB2bPortal> Save(UserB2bPortal user);
        Task<List<UserB2bPortal>> Find(List<Guid> bus, string document, int skip, int limit);
        Task<UserB2bPortal> FindByLogin(string login);
        Task<UserB2bPortal> FindByDocument(string document);
        Task<UserB2bPortal> FindById(Guid id);
    }
}