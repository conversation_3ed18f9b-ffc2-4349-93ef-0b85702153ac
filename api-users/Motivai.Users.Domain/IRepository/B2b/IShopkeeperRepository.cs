using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.Users.Domain.Entities.B2b;

namespace Motivai.Users.Domain.IRepository.B2b {
    public interface IShopkeeperRepository {
        Task<Shopkeeper> FindById(Guid id, List<Guid> bus);
        Task<Shopkeeper> FindByDocument(string document, List<Guid> bus);
        Task<bool> Save(Shopkeeper shopkeeper);
        Task<Shopkeeper> FindByUserId(Guid userId, List<Guid> bus);
        Task<List<Shopkeeper>> Find(string document, string name, int skip, int limit, List<Guid> bus);
    }
}