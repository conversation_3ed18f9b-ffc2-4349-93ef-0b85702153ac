using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.MyAccount;

namespace Motivai.Users.Domain.IRepository.Orders
{
    public interface IOrderRepository
    {
        Task<List<ResumedOrder>> GetOrdersByParticipant(Guid participantId, string status, DateTime? initialDate, DateTime? finalDate);
        Task<List<ResumedOrder>> GetOrdersWithProductsByUserId(Guid userId, string status, DateTime? startDate, DateTime? endDate);
    }
}