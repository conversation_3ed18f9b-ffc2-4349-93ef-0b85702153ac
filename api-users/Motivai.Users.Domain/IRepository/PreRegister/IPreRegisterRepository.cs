using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IRepository.PreRegister {
    public interface IPreRegisterRepository {
        Task<bool> Save(PreRegisteredUser user);
        Task<PreRegisteredUser> FindById(Guid id);
        Task<List<PreRegisteredUser>> Find(Guid campaignId, string document, bool? integrated, bool? integrationError, int? skip, int? limit);
        Task<PreRegisteredUser> FindByDocumentAndCampaign(string document, Guid campaignId);
        Task<bool> VerifyExistingDocument(Guid campaignId, string document);
    }
}