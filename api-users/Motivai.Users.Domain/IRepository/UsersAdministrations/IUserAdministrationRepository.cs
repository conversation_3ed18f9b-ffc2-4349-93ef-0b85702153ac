﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IRepository.UsersAdministrations
{
	public interface IUserAdministrationRepository
	{
		Task<UserAdministration> Get(List<Guid> bus, Guid userId);
		Task<string> GetUserName(Guid userId);
		Task<UserAdministration> Get(string login);
		Task<UserAdministration> Get(Guid userId);
		Task<List<UserAdministration>> GetUsers(List<Guid> bus, string name, string email, string login, int? skip, int? limit);
		Task<bool> IsLoginIsUseByOtherThan(string login, Guid userAdminId);
		Task<bool> Create(UserAdministration user);
		Task<bool> Save(UserAdministration user);
		Task<bool> Delete(Guid userId);
	}
}
