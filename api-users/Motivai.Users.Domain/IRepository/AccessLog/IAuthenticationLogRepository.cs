using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IRepository.AccessLog
{
    public interface IAuthenticationLogRepository
    {
        Task RegisterAuthenticationLog(CampaignAuthenticationLog authenticationLog);
        Task RegisterLoginFromCallcenter(CallcenterLogin callcenterLogin, UserParticipantModel participantSession, LocationInfo locationInfo = null);
        Task RegisterLogin(AuthenticationActionLog action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null);
    }
}