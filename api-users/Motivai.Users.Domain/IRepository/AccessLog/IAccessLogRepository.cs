using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IRepository.AccessLog
{
	public interface IAccessLogRepository
	{
		Task RegisterLoginFromCallcenter(CallcenterLogin callcenterLogin, UserParticipantModel participantSession, LocationInfo locationInfo = null);
		Task RegisterLogin(LogRegisterAction action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null);
		Task RegisterAccountOperatorLogin(LogRegisterAction action, Guid userId, Guid campaignId, string userDocument,
			Guid accountOperatorId, string operatorDocument, Guid accountOperatorLoginId, LocationInfo locationInfo);
		Task RegisterLogin(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo);
    }
}