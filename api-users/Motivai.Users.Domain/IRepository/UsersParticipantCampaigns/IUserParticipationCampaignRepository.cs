﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.Models.MyAccount;

namespace Motivai.Users.Domain.IRepository.UsersParticipantCampaigns {
	public interface IUserParticipationCampaignRepository {
		Task<bool> UpdateParticipant(UserParticipantCampaign participant);
		Task<bool> IsUserActive(Guid userId, Guid campaignId);
		Task<User> GetUserByCampaignAndLogin(Guid campaignId, string login);
		Task<UserParticipantCampaign> Get(Guid participantId);
		Task<UserParticipantCampaign> Get(Guid userId, Guid campaignId);
		Task<Guid> GetParticipantId(Guid userId, Guid campaignId);
		Task<Guid?> GetParticipantRanking(Guid userId, Guid campaignId);
		Task<bool> UpdateParticipantBalance(Guid userId, Guid participantId, decimal balance);
		Task<ParticipantContact> GetContactInfo(Guid userId, Guid campaignId);
		Task<bool> ExistCpfWithCampaign(string cpf, Guid campaignId);
		Task<bool> ExistCnpjWithCampaign(string cnpj, Guid campaignId);
		Task<MainContactInfo> GetMainContactInfo(Guid userId, Guid campaignId);
		Task<List<Address>> GetAddresses(Guid userId, Guid campaignId, bool? main = false);
		Task<List<ResumedAddress>> GetResumedAddresses(Guid userId, Guid campaignId);
		Task<Address> GetMainAddress(Guid userId, Guid campaignId);
		Task<List<Address>> GetAllAddresses(Guid userId, Guid campaignId, bool? main = false);
		Task<Address> GetAddressById(Guid userId, Guid campaignId, Guid addressId);
		Task<string> GetAddressCep(Guid userId, Guid campaignId, Guid addressId);
		Task<bool> DeleteAddress(Guid userId, Guid participantId, Address address);
		Task<bool> UpdateAddress(SharedKernel.Domain.Entities.References.Contacts.Address address);
		Task<List<string>> GetDistinctEmployee(Guid campaignId, string propertieEmployee);
		Task<UserParticipantCampaign> GetParticipantByCampaignEmailAndLogin(Guid campaignId, string email, string login);
		Task<bool> ExistParticipantWithDocument(Guid campaignId, Guid userId, string userDocument);
		Task ChangeParticipantsParent(Guid currentParentId, Guid newParentId, UserBasicInfo newParentDetails);

		#region Participante Pai

		Task<List<UserParentDetails>> GetUsersParentsByIds(Guid campaignId, List<Guid> usersIds);
		Task<UserParentDetails> GetUserParent(Guid userId, Guid campaignId);
		Task<UserBasicInfo> GetParentUser(Guid userId, Guid campaignId);
		Task<List<UserBasicInfo>> FindUsersWithSameParent(Guid campaignId, Guid parentUserId);
		Task<bool> UpdateParticipantParent(Guid campaignId, UserParentDetails userParent);
		Task<bool> UpdateParticipantParent(Guid userId, Guid campaignId, UserBasicInfo parent);

		#endregion

		Task<bool> UpdateUserContactById(Guid campaignId, Guid userId, Contact contact);
	}
}