using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IRepository.Roles
{
    public interface IRoleRepository
    {
        Task<List<Role>> GetRoles(List<Guid> bus, string name, int? skip, int? limit);
        Task<Role> Get(List<Guid> bus, Guid roleId);
        Task Save(Role role);
        Task<bool> Delete(Guid roleId);
        Task<Role> GetRoleByToken(string token);
    }
}