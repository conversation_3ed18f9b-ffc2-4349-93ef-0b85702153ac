namespace Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson
{
    public class ConsultPersonData
    {
        public bool Enabled { get; set; }

        ///<summary>
        /// Páginas onde a consulta sera realizada
        ///</summary>
        public ProcessesEnabled ProcessesEnabled { get; set; }

        ///<summary>
        /// Campos que serão exibidos na tela
        ///</summary>
        public FieldsEnabled FieldsEnabled { get; set; }

        public bool IsEnabled()
        {
            return (this.Enabled && this.ProcessesEnabled != null) && this.FieldsEnabled != null;
        }
        public bool IsPreRegisterEnabled()
        {
            return (this.Enabled && this.ProcessesEnabled != null) && this.ProcessesEnabled.PreRegister;
        }

        public bool IsParticipantInviteEnabled()
        {
            return (this.Enabled && this.ProcessesEnabled != null) && this.ProcessesEnabled.ParticipantInvite;
        }

        public bool IsPointsDistributeEnabled()
        {
            return (this.Enabled && this.ProcessesEnabled != null) && (this.FieldsEnabled != null && this.ProcessesEnabled.PointsDistribute);
        }
    }
}
