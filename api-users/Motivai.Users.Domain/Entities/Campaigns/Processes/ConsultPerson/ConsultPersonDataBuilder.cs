using System;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Models.Credify;
using Motivai.Users.Domain.Models.Import;

namespace Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson
{
	public class ConsultPersonDataBuilder
	{
		public static dynamic BuildWithEnabledFieldsOrDefault(Person person, ConsultPersonData consultPersonDataParams)
		{
			if (IsConsultPersonDataValidAndEnabled(consultPersonDataParams))
			{
				return ConsultPersonDataBuilder.BuildWithEnabledFields(person, consultPersonDataParams.FieldsEnabled);
			}
			return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(person.Document, person.Type);
		}

		public static dynamic BuildWithEnabledFieldsOrDefault(ParticipantInfo participant, ConsultPersonData consultPersonDataParams)
		{
			if (IsConsultPersonDataValidAndEnabled(consultPersonDataParams))
			{
				return ConsultPersonDataBuilder.BuildWithEnabledFields(participant, consultPersonDataParams.FieldsEnabled);
			}
			return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(participant.Document, participant.Type);
		}

		public static dynamic BuildWithEnabledFieldsOrDefault(User user, Guid campaignId, ConsultPersonData consultPersonDataParams)
		{
			if (IsPointsDistributionEnabled(consultPersonDataParams))
			{
				return ConsultPersonDataBuilder.BuildWithEnabledFields(user, campaignId, consultPersonDataParams.FieldsEnabled);
			}
			return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(user, campaignId);
		}

		public static dynamic BuildByPointsDistributionPageProcess(Person person, ConsultPersonData consultPersonDataParams)
		{
			if (IsPointsDistributionEnabled(consultPersonDataParams))
			{
				return ConsultPersonDataBuilder.BuildWithEnabledFields(person, consultPersonDataParams.FieldsEnabled);
			}
			return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(person.Document, person.Type);
		}

		private static Person BuildWithEnabledFields(Person person, FieldsEnabled fieldsEnabled)
		{
			var personData = new Person
			{
				Document = person.Document,
				Type = person.Type
			};

			if (fieldsEnabled.Name)
				personData.Name = person.Name;
			if (fieldsEnabled.BirthDate)
				personData.BirthDate = person.BirthDate;
			if (fieldsEnabled.Gender)
				personData.Gender = person.Gender;
			if (fieldsEnabled.Address)
				personData.MainAddress = person.MainAddress;

			return personData;
		}

		private static dynamic BuildWithEnabledFields(User user, Guid campaignId, FieldsEnabled fieldsEnabled)
		{
			var basicInfo = new ParticipantBasicInfo
			{
				UserId = user.Id,
				Document = user.GetDocument(),
				CompanyName = user.CompanyName
			};

			if (fieldsEnabled.Name)
				basicInfo.Name = user.Name;

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant != null)
			{
				basicInfo.ParticipantId = participant.Id;
				basicInfo.Email = participant.GetMainEmail();
				basicInfo.MobilePhone = participant.GetMobilePhone();
			}
			return basicInfo;
		}

		private static dynamic BuildWithEnabledFields(ParticipantInfo participant, FieldsEnabled fieldsEnabled)
		{
			var personData = new Person
			{
				Document = participant.Document,
				Type = participant.Type
			};

			if (fieldsEnabled.Name)
				personData.Name = participant.Name;
			if (fieldsEnabled.Address)
				personData.MainAddress = participant.MainAddress;

			return personData;
		}

		public static dynamic BuildWithDefaultEnabledFields(string document, PersonType personType)
		{
			return new Person
			{
				Document = document,
				Type = personType
			};
		}

		private static dynamic BuildWithDefaultEnabledFields(User user, Guid campaignId)
		{
			var basicInfo = new ParticipantBasicInfo
			{
				UserId = user.Id,
				Document = user.GetDocument(),
				CompanyName = user.CompanyName
			};

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant != null)
			{
				basicInfo.ParticipantId = participant.Id;
				basicInfo.Email = participant.GetMainEmail();
				basicInfo.MobilePhone = participant.GetMobilePhone();
			}
			return basicInfo;
		}

		private static bool IsConsultPersonDataValidAndEnabled(ConsultPersonData consultPersonDataParams)
		{
			return consultPersonDataParams != null && consultPersonDataParams.IsEnabled();
		}

		private static bool IsPointsDistributionEnabled(ConsultPersonData consultPersonDataParams)
		{
			return consultPersonDataParams != null && consultPersonDataParams.IsPointsDistributeEnabled();
		}
	}
}
