using System;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.UsersAdministrators
{
    public class UserAdministrationPasswordModel : UserAdministrationAction
    {
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
        public string Confirmation { get; set; }

        public void Validate()
        {
            if (string.IsNullOrWhiteSpace(NewPassword))
            {
                throw MotivaiException.ofValidation("Deve ser informado a nova senha.");
            }

            if (NewPassword != Confirmation)
            {
                throw MotivaiException.ofValidation("A nova senha e a confirmação não conferem.");
            }

            if (NewPassword.Length < 6)
            {
                throw MotivaiException.ofValidation("A nova senha deve ter no mínimo 6 caracteres.");
            }

            if (!NewPassword.Any(char.IsDigit))
            {
                throw MotivaiException.ofValidation("A nova senha deve ter no mínimo 1 caracter numérico.");
            }

            if (!NewPassword.Any(char.<PERSON>))
            {
                throw MotivaiException.ofValidation("A nova senha deve ter no mínimo 1 caracter maiúsculo.");
            }

            if (!NewPassword.Any(char.IsLower))
            {
                throw MotivaiException.ofValidation("A nova senha deve ter no mínimo 1 caracter minúsculo.");
            }
        }
    }
}