﻿using System;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.UsersAdministrators;

namespace Motivai.Users.Domain.Entities
{
    public class UserAdministration : BaseEntity
    {
        public string Login { get; set; }
        public Senha Password { get; set; }

        public string Document { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string MobilePhone { get; set; }

        ///<summary>
        /// E-mail do responsável pelo contato.
        ///</summary>
        public string ContactEmail { get; set; }

        public Guid RoleId { get; set; }
        public Guid BuId { get; set; }
        public Guid RegionId { get; set; }

        public string Description { get; set; }

        ///<summary>
        /// ID da BU que criou o usuário.
        ///</summary>
        public Guid? CreationBuId { get; set; }
        public OperationUser CreationOperationUser { get; set; }
        public LocationInfo CreationLocationInfo { get; set; }
        public OperationUser UpdateOperationUser { get; set; }
        public LocationInfo UpdateLocationInfo { get; set; }

        public static UserAdministration From(UserAdministrationModel userModel)
        {
            var user = new UserAdministration()
            {
                Login = userModel.Login,
                Active = userModel.Active,
                Document = userModel.Document,
                Name = userModel.Name,
                Email = userModel.Email,
                MobilePhone = userModel.MobilePhone,
                ContactEmail = userModel.ContactEmail,
                RoleId = userModel.RoleId,
                BuId = userModel.BuId,
                RegionId = userModel.RegionId,
                Description = userModel.Description,
                CreationOperationUser = userModel.OperationUser,
                CreationLocationInfo = userModel.LocationInfo
            };
            user.StartValidation();
            return user;
        }

        public OperationUser GetLastOperationUser()
        {
            if (UpdateOperationUser == null)
                return CreationOperationUser;
            return UpdateOperationUser;
        }

        public LocationInfo GetLastLocationInfo()
        {
            if (UpdateLocationInfo == null)
                return CreationLocationInfo;
            return UpdateLocationInfo;
        }

        ///<summary>
        /// Documento, E-mail, Celular e E-mail do responsável serão somentes alterados se não existirem.
        ///</summary>
        public void Update(UserAdministrationModel userModel)
        {
            UpdateDate = DateTime.UtcNow;
            UpdateOperationUser = userModel.OperationUser;
            UpdateLocationInfo = userModel.LocationInfo;
            Name = userModel.Name;
            Active = userModel.Active;
            RoleId = userModel.RoleId;
            BuId = userModel.BuId;
            RegionId = userModel.RegionId;

            if (string.IsNullOrEmpty(Document)) {
                Document = userModel.Document;
            }
            if (string.IsNullOrEmpty(Email))
            {
                Email = userModel.Email;
            }
            if (string.IsNullOrEmpty(MobilePhone))
            {
                MobilePhone = userModel.MobilePhone;
            }
            if (string.IsNullOrEmpty(ContactEmail)) {
                ContactEmail = userModel.ContactEmail;
            }

            Validate();
        }

        public void UpdatePassword(string password)
        {
            Password = new Senha(password, password);
            Password.GerarNovoTokenAlterarSenha();
        }

        public override void Validate()
        {
            Login.ForNullOrEmpty("Deve ser informado o login do usuário.");
            Name.ForNullOrEmpty("Deve ser informado o nome do usuário.");
            Email.ForNullOrEmpty("Deve ser informado o e-mail do usuário.");
            ContactEmail.ForNullOrEmpty("Deve ser informado o e-mail do responsável para contato");
            Document.ForNullOrEmpty("Deve ser informado o CPF do usuário.");
            if (!Cpf.IsCpf(Document))
            {
                throw MotivaiException.ofValidation("CPF inválido.");
            }

            if (RoleId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o perfil do usuário.");
            if (BuId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informada a unidade de negócio do usuário.");
            if (CreationOperationUser == null && UpdateOperationUser == null)
                throw MotivaiException.ofValidation("Usuário da operação inválido.");

            Email = Email.Trim();
            Login = Login.Trim();
            ContactEmail = ContactEmail.Trim();
        }
    }
}
