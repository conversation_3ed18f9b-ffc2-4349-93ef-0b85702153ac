using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.UsersAdministrators
{
    public class UserAdministrationModel
    {
        public bool Active { get; set; }
        public string Login { get; set; }

        public string Document { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string MobilePhone { get; set; }
        public string ContactEmail { get; set; }

        public Guid RoleId { get; set; }
        public Guid BuId { get; set; }
        public Guid RegionId { get; set; }

        public bool UseLoginAsPassword { get; set; }

        public string Description { get; set; }

        public OperationUser OperationUser { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public void Validate()
        {
            Login.ForNullOrEmpty("Deve ser informado o login do usuário.");
            Name.ForNullOrEmpty("Deve ser informado o nome do usuário.");
            Email.ForNullOrEmpty("Deve ser informado o e-mail do usuário.");
            ContactEmail.ForNullOrEmpty("Deve ser informado o e-mail do responsável para contato");
            Document.ForNullOrEmpty("Deve ser informado o CPF do usuário.");
            if (!Cpf.IsCpf(Document))
            {
                throw MotivaiException.ofValidation("CPF inválido.");
            }

            if (RoleId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o perfil do usuário.");
            if (BuId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informada a unidade de negócio do usuário.");
            if (OperationUser == null)
                throw MotivaiException.ofValidation("Usuário da operação inválido.");

            Email = Email.Trim();
            Login = Login.Trim();
            ContactEmail = ContactEmail.Trim();
        }
    }
}