using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using MongoDB.Bson;

namespace Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog
{
    public class UserAdministrationActionLog
    {
        public ObjectId Id { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime AccessDate { get; set; }
        public OperationUser OperationUser { get; set; }
        public Guid UserId { get; set; }
        public string Module { get; set; }
        public string Feature { get; set; }
        public string Action { get; set; }
        public Guid? CampaignId { get; set; }
        public Dictionary<string, string> Metadata { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public static UserAdministrationActionLog OfOperationLog(UserActionOperationLog operationLog)
        {
            return new UserAdministrationActionLog()
            {
                Id = ObjectId.GenerateNewId(),
                CreateDate = DateTime.UtcNow,
                AccessDate = operationLog.AccessDate,
                OperationUser = operationLog.OperationUser,
                UserId = operationLog.OperationUser.UserId,
                Module = operationLog.Module,
                Feature = operationLog.Feature,
                Action = operationLog.Action,
                CampaignId = operationLog.CampaignId,
                Metadata = operationLog.Metadata,
                LocationInfo = operationLog.LocationInfo
            };
        }
    }
}
