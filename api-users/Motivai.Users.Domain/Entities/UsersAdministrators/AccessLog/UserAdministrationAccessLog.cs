using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Models.UserAdministration;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog
{
    public class UserAdministrationAccessLog
    {
        public ObjectId Id { get; set; }
        public Guid? SessionId { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime AccessDate { get; set; }
        [BsonRepresentation(BsonType.String)]
        public UserAdministrationAccessLogAction Action { get; set; }

        public Guid UserId { get; set; }
        public string Login { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public Guid RoleId { get; set; }
        public Guid BuId { get; set; }

        public LocationInfo LocationInfo { get; set; }

        public dynamic OperationUserAdministration { get; set; }

        public static UserAdministrationAccessLog OfAction(Guid sessionId, UserAdministrationAccessLogAction action,
            LocationInfo locationInfo)
        {
            return new UserAdministrationAccessLog()
            {
                Id = ObjectId.GenerateNewId(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                SessionId = sessionId,
                Action = action,
                LocationInfo = locationInfo
            };
        }

        public static UserAdministrationAccessLog OfLoggedWithPassword(Guid sessionId, UserAdministration user,
            LocationInfo locationInfo)
        {
            return new UserAdministrationAccessLog()
            {
                Id = ObjectId.GenerateNewId(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                SessionId = sessionId,
                Action = UserAdministrationAccessLogAction.LOGIN_WITH_PASSWORD,
                UserId = user.Id,
                Login = user.Login,
                Email = user.Email,
                RoleId = user.RoleId,
                BuId = user.BuId,
                LocationInfo = locationInfo
            };
        }

        public static UserAdministrationAccessLog OfSecurityCodeValidated(Guid sessionId,
            UserAdministrationAuthenticationResponse user, LocationInfo locationInfo)
        {
            return new UserAdministrationAccessLog()
            {
                Id = ObjectId.GenerateNewId(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                SessionId = sessionId,
                Action = UserAdministrationAccessLogAction.SECURITY_CODE_VALIDATED,
                UserId = user.Id,
                Login = user.Login,
                Email = user.Email,
                RoleId = user.RoleId,
                BuId = user.BuId,
                LocationInfo = locationInfo
            };
        }

        public static UserAdministrationAccessLog OfAuthenticationRequest(UserAdministrationAccessLogAction action,
            UserAdministrationAuthenticationRequest authenticationRequest, UserAdministration userAdmin = null)
        {
            var log = new UserAdministrationAccessLog()
            {
                Id = ObjectId.GenerateNewId(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                Action = action,
                Login = authenticationRequest.Login,
                LocationInfo = authenticationRequest.LocationInfo
            };

            if (userAdmin != null)
            {
                log.UserId = userAdmin.Id;
                log.Email = userAdmin.Email;
                log.Name = userAdmin.Name;
                log.RoleId = userAdmin.RoleId;
                log.BuId = userAdmin.BuId;
            }

            return log;
        }
    }

    public enum UserAdministrationAccessLogAction
    {
        INACTIVE_BU,
        LOGIN_WITH_PASSWORD,
        SECURITY_CODE_VALIDATED,
        INVALID_USER,
        INVALID_PASSWORD,
        INACTIVE_USER,
        INACTIVE_ROLE,
        INVALID_SECURITY_CODE,
        SUCCESSFUL_LOGIN,
        PASSWORD_RESETED
    }
}
