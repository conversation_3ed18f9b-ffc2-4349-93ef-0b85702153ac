using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Enums.B2bPortal;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities {
    public class UserB2bPortal : BaseEntity {
        public string Document { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string MobilePhone { get; set; }
        [BsonRepresentation(BsonType.String)]
        public B2bUserType? Type { get; set; }
        public string Login { get; set; }
        public Senha Password { get; set; }
        public List<B2bUserCampaign> Campaigns { get; set; }
        public List<B2bFactories> Factories { get; set; }
        public Guid BuId { get; set; }
        public string GeneratedPassword { get; set; }

        public override void Validate() {
            if (Id == Guid.Empty)
                Id = Guid.NewGuid();

            Document.ForNullOrEmpty("Documento é obrigatório");
            Name.ForNullOrEmpty("Nome é obrigatório");
            Email.ForNullOrEmpty("Email é obrigatório");
            Login.ForNullOrEmpty("Login é obrigatório");
            if (!Type.HasValue) {
                throw MotivaiException.ofValidation("Tipo do usuário é obrigatório");
            }

            GeneratedPassword = null;
        }

        public void UpdateFieldsFrom(UserB2bPortal user) {
            this.Document = user.Document;
            this.Name = user.Name;
            this.Email = user.Email;
            this.MobilePhone = user.MobilePhone;
            this.Type = user.Type;
            this.Login = user.Login;
            this.Campaigns = user.Campaigns;
            this.Factories = user.Factories;
            this.Active = user.Active;
        }
    }

    public class B2bFactories {
        public Guid FactoryId { get; set; }
        public string CepFrom { get; set; }
        public string CepTo { get; set; }
    }

    public class B2bUserCampaign {
        public Guid CampaignId { get; set; }
    }
}