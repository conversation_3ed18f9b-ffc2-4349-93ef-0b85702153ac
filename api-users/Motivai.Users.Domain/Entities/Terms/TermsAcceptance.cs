using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Users.Domain.Entities.Terms {
    public class TermsAcceptance {
        public Guid Id { get; set; }
        [BsonRepresentation(BsonType.String)]
        public TermsType Type { get; set; }
        public DateTime AcceptanceDate { get; set; }

        public Guid UserId { get; set; }
        public Guid CampaignId { get; set; }

        ///<summary>
        /// ID do Regulamento, Política de Entrega ou Privacidade.
        ///</summary>
        public string TermId { get; set; }
        public string Version { get; set; }

        public static TermsAcceptance ofRegulation(Guid userId, Guid campaignId, string regulationId, string version) {
            return new TermsAcceptance() {
                Id = Guid.NewGuid(),
                Type = TermsType.Regulation,
                AcceptanceDate = DateTime.UtcNow,
                UserId = userId,
                CampaignId = campaignId,
                TermId = regulationId,
                Version = version
            };
        }
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum TermsType {
        Regulation,
        ShippingPolicy,
        PrivacyPolicy
    }
}