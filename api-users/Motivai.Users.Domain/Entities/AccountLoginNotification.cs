using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Entities
{
	public class AccountLoginNotification
	{
		public Guid CampaignId { get; set; }
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }

		public string Name { get; set; }
		public string Email { get; set; }
		public string Document { get; set; }

		///<summary>
		/// E-mail do operador que está logando.
		///</summary>
		public string AccountOperatorEmail { get; set; }

		public string Timezone { get; set; }

		public ConnectionInfo ConnectionInfo { get; set; }

		public static AccountLoginNotification FromParticipant(Guid campaignId, User user, UserParticipantCampaign participant, LocationInfo locationInfo)
		{
			return new AccountLoginNotification()
			{
				CampaignId = campaignId,
				UserId = user.Id,
				ParticipantId = participant.Id,
				Name = user.GetName(),
				Document = user.GetDocument(),
				Email = participant.GetMainEmail(),
				Timezone = locationInfo?.Timezone,
				ConnectionInfo = locationInfo?.ConnectionInfo
			};
		}

		public static AccountLoginNotification FromParticipantSession(UserParticipantModel participantSession, ConnectionInfo connectionInfo)
		{
			return new AccountLoginNotification()
			{
				CampaignId = participantSession.CampaignId,
				UserId = participantSession.UserId,
				ParticipantId = participantSession.ParticipantId,
				Name = participantSession.Name,
				Document = participantSession.IsAccountOperator() ? participantSession.AccountOperatorDocument : participantSession.Document,
				Email = participantSession.Email,
				AccountOperatorEmail = participantSession.AccountOperatorEmail,
				Timezone = participantSession.Timezone,
				ConnectionInfo = connectionInfo
			};
		}
	}

}