using System;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace Motivai.Users.Domain.Entities {
	public class UserBasicInfo {
		public Guid? UserId { get; set; }
		public Guid? ParticipantId { get; set; }
		public Guid? CampaignId { get; set; }

		public string Name { get; set; }
		public string Document { get; set; }

		public bool? Active { get; set; }

		[JsonIgnore]
		[BsonIgnore]
		public string Cpf {
			get {
				return Document;
			}
			set {
				if (string.IsNullOrEmpty(value)) {
					return;
				}
				Document = value;
			}
		}

		[JsonIgnore]
		[BsonIgnore]
		public string Cnpj {
			get {
				return Document;
			}
			set {
				if (string.IsNullOrEmpty(value)) {
					return;
				}
				Document = value;
			}
		}

		public override string ToString(){
			return $"Name: {Name}" +
					$"Document: {Document}";
		}
	}
}