using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities
{
	public class PreRegisteredUser : Motivai.SharedKernel.Domain.Entities.References.Users.PreRegisteredUser
	{
		private const string HOME_ADDRESS_NAME = "Endereço Residencial";
		private const string COMMERCIAL_ADDRESS_NAME = "Endereço Comercial";

		//Parent Participant
		public string ParentParticipantIdentifier { get; set; }
		public UserBasicInfo ParentParticipant { get; set; }
		public Guid? ParentUserId { get; set; }

		public bool EnableSkipApproval { get; set; }

		public Dictionary<string, string> UserMetadata { get; set; }

		///<summary>
		/// Flag para informar se o cadastro do participante foi integrado.
		///</summary>
		public bool? Integrated { get; set; }
		public DateTime? IntegratedDate { get; set; }
		public string IntegrationErrorMessage { get; set; }

		public void SetIntegrated(bool integrated)
		{
			Integrated = integrated;
			IntegratedDate = DateTime.UtcNow;
		}

		public void SetIntegrationError(string error)
		{
			SetIntegrated(false);
			IntegrationErrorMessage = error;
		}

		private void VerifyDocument()
		{
			if (string.IsNullOrEmpty(Document))
				throw MotivaiException.ofValidation("CPF/CNPJ é obrigatório.");
			if (Cpf.IsCpf(Document))
			{
				Type = PersonType.Fisica;
			}
			else if (Cnpj.IsCnpj(Document))
			{
				Type = PersonType.Juridica;
			}
			else
			{
				throw MotivaiException.ofValidation("CPF/CNPJ inválido.");
			}
		}

		public override void Validate()
		{
			VerifyDocument();
			if (!this.IsFisica() && !this.IsJuridica())
				throw MotivaiException.ofValidation("Documento inválido. Informe 11 digitos para CPF ou 14 digitos para CNPJ");
		}

		public void ValidateUsingConfig(PreRegisterSettings settings)
		{
			Validate();

			if (settings.EnablePersonalData)
			{
				if (IsFisica())
				{
					settings.PersonalData.ValidateTypeFisica(Name, Rg, BirthDate, MaritalStatus, Gender);
				}
				else
				{
					settings.PersonalData.ValidateTypeJuridica(Name, CompanyName, StateInscriptionExempt, StateInscription, StateInscriptionUf);
				}
			}
			if (settings.EnableEmails)
			{
				settings.Emails.ValidateEmails(PersonalEmail, BusinessEmail);
			}
			if (settings.EnablePhones)
			{
				settings.Phones.ValidatePhones(HomePhone, BusinessPhone, MobilePhone, MobileOperator);
			}
			if (settings.EnablePassword)
			{
				settings.Password.ValidatePassword(Password);
			}
			if (settings.EnableHomeAddress)
			{
				if (HomeAddress == null)
					throw MotivaiException.ofValidation($"{settings.HomeAddressTitle} é obrigatório.");
				settings.Address.ValidateAddress(HomeAddress);
			}
			if (settings.EnableBusinessAddress)
			{
				if (BusinessAddress == null)
					throw MotivaiException.ofValidation($"{settings.BusinessAddressTitle} é obrigatório.");
				settings.Address.ValidateAddress(BusinessAddress);
			}
		}

		public void RegisterSkipApproval()
		{
			EnableSkipApproval = true;
			Approved = true;
			ApprovedOn = DateTime.UtcNow;
		}

		public void ValidateApproval()
		{
			if (!ApprovedBy.HasValue || ApprovedBy.Value == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário de aprovação é obrigatório");
		}

		public void ValidateRefuse()
		{
			if (!RefusedBy.HasValue || RefusedBy.Value == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário da reprovação é obrigatório");
		}

		public bool IsFisica()
		{
			if (!Type.HasValue)
				VerifyDocument();
			return Type == PersonType.Fisica;
		}

		public bool IsJuridica()
		{
			if (!Type.HasValue)
				VerifyDocument();
			return Type == PersonType.Juridica;
		}

		public User ToUser(CampaignSettingsModel campaignSettings)
		{
			var user = User.CreateUser(campaignSettings.Type, Document);

			if (user.IsFisicPerson())
			{
				user.Type = PersonType.Fisica;
				user.Cpf = Document;
				user.Name = this.Name;
				user.Rg = this.Rg;
				user.MaritalStatus = this.MaritalStatus;
				user.Gender = this.Gender;
				if (this.BirthDate.HasValue)
					user.BirthDate = this.BirthDate.Value;
				// PJ
			}
			else if (user.IsJuridicPerson())
			{
				user.Type = PersonType.Juridica;
				user.Cnpj = Document;
				user.Name = this.Name;
				user.CompanyName = this.CompanyName;
				user.StateInscriptionExempt = this.StateInscriptionExempt;
				user.StateInscription = this.StateInscription;
				user.StateInscriptionUf = this.StateInscriptionUf;
			}

			return user;
		}

		public UserParticipantCampaign ToParticipant(User user, CampaignSettingsModel campaignSettings)
		{

			var participant = UserParticipantCampaign.CreateForCampaign(CampaignId, campaignSettings, user);
			participant.Origin = "PreRegistration";

			if (this.ParentParticipant != null) {
				participant.ParentUserId = this.ParentParticipant.UserId;
				participant.ParentUserDetails = this.ParentParticipant;
			}

			if (this.HomeAddress != null)
			{
				participant.Addresses.Add(new Address
				{
					Id = Guid.NewGuid(),
					AddressName = HOME_ADDRESS_NAME,
					Cep = this.HomeAddress.Zipcode,
					Street = this.HomeAddress.Street,
					Number = this.HomeAddress.Number,
					Complement = this.HomeAddress.Complement,
					Neighborhood = this.HomeAddress.Neighborhood,
					City = this.HomeAddress.City,
					State = this.HomeAddress.State,
					Reference = this.HomeAddress.Reference,
					CreateDate = DateTime.UtcNow,
					Active = true
				});
			}

			if (this.BusinessAddress != null)
			{
				participant.Addresses.Add(new Address
				{
					Id = Guid.NewGuid(),
					AddressName = COMMERCIAL_ADDRESS_NAME,
					Cep = this.BusinessAddress.Zipcode,
					Street = this.BusinessAddress.Street,
					Number = this.BusinessAddress.Number,
					Complement = this.BusinessAddress.Complement,
					Neighborhood = this.BusinessAddress.Neighborhood,
					City = this.BusinessAddress.City,
					State = this.BusinessAddress.State,
					Reference = this.BusinessAddress.Reference,
					CreateDate = DateTime.UtcNow,
					Active = true
				});
			}

			participant.Contact = new Contact
			{
				Id = Guid.NewGuid(),
				MainEmail = this.PersonalEmail.Trim() ?? this.BusinessEmail.Trim(),
				PersonalEmail = this.PersonalEmail,
				CommercialEmail = this.BusinessEmail,
				CommercialPhone = this.BusinessPhone,
				HomePhone = this.HomePhone,
				MobilePhone = this.MobilePhone,
				MobileOperator = this.MobileOperator,
				CreateDate = DateTime.UtcNow,
				Active = true
			};

			return participant;
		}

		public static PreRegisteredUser OfUser(User user, Guid campaignId)
		{
			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não encontrado na campanha informada.");

            return new PreRegisteredUser
            {
				CampaignId = participant.CampaignId,
                Document = user.GetDocument(),
                Name = user.Name,
                PersonalEmail = participant.GetMainEmail(),
                BirthDate = user.BirthDate,
                CompanyName = user.CompanyName,
                HomeAddress =  participant.Addresses?.Count > 0 ? participant.Addresses.FirstOrDefault().ToShortAddress() : null,
                HomePhone = participant.GetHomePhone(),
                MobilePhone = participant.GetMobilePhone()
            };
		}
	}
}