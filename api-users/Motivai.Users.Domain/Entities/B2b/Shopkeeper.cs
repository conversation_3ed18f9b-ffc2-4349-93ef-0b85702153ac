using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using MongoDB.Bson;

namespace Motivai.Users.Domain.Entities.B2b {
    public class Shopkeeper : BaseEntity {
        public Guid UserId { get; set; }
        public Guid BuId { get; set; }
        public string Document { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string MainPhone { get; set; }
        public string MobilePhone { get; set; }
        public string BusinessPhone { get; set; }
        public Address Address { get; set; }
        public List<ShopkeeperCampaign> Campaigns { get; set; }
        public bool CreatedByIntegration { get; set; }
        public IntegrationCompany IntegrationCompany { get; set; }

        private void UpdateAddressFields(Address address) {
            address.Cep = this.Address.Cep;
            address.Street = this.Address.Street;
            address.Number = this.Address.Number;
            address.Complement = this.Address.Complement;
            address.Reference = this.Address.Reference;
            address.Neighborhood = this.Address.Neighborhood;
            address.City = this.Address.City;
            address.State = this.Address.State;
            address.UpdateDate = DateTime.UtcNow;
        }

        private void UpdateContactFields(Contact contact) {
            contact.MainEmail = Email;
            contact.MainPhone = MainPhone;
            contact.MobilePhone = MobilePhone;
            contact.CommercialEmail = Email;
            contact.UpdateDate = DateTime.UtcNow;
        }

        private bool IsPessoaFisica() {
            if (Document.Length == 11) {
                return Cpf.IsCpf(Document);
            }

            return false;
        }

        private bool IsPessoaJuridica() {
            if (Document.Length == 14) {
                return Cnpj.IsCnpj(Document);
            }

            return false;
        }

        public void AddCampaign(Guid campaignId, bool active) {
            if (Campaigns == null)
                Campaigns = new List<ShopkeeperCampaign>();

            var isInCampaign = Campaigns.FirstOrDefault(x => x.CampaignId == campaignId);
            if (isInCampaign != null) {
                isInCampaign.Active = active;
            } else {
                Campaigns.Add(new ShopkeeperCampaign() {
                    CampaignId = campaignId,
                        Active = active
                });
            }
        }

        public void UpdateFields(Shopkeeper shopkeeper) {
            Document = shopkeeper.Document;
            Name = shopkeeper.Name;
            Email = shopkeeper.Email;
            MainPhone = shopkeeper.MainPhone;
            MobilePhone = shopkeeper.MobilePhone;
            BusinessPhone = shopkeeper.BusinessPhone;
            if (shopkeeper.Address != null) {
                if (Address == null)
                    Address = new Address();

                Address.Street = shopkeeper.Address.Street;
                Address.Number = shopkeeper.Address.Number;
                Address.Complement = shopkeeper.Address.Complement;
                Address.Reference = shopkeeper.Address.Reference;
                Address.Neighborhood = shopkeeper.Address.Neighborhood;
                Address.City = shopkeeper.Address.City;
                Address.State = shopkeeper.Address.State;
                Address.UpdateDate = DateTime.UtcNow;
            }

            if (Campaigns == null) {
                Campaigns = shopkeeper.Campaigns;
            } else {
                // Atualiza campanhas que já estavam na lista
                Campaigns.ForEach(campaign => {
                    var shopkeeperCampaign = shopkeeper.Campaigns.FirstOrDefault(c => c.CampaignId == campaign.CampaignId);
                    if (shopkeeperCampaign != null) {
                        campaign.Active = shopkeeperCampaign.Active;
                        campaign.UpdateDate = DateTime.UtcNow;
                    }
                });

                // Adiciona campanhas que não estavam na lista
                var except = shopkeeper.Campaigns.Select(x => x.CampaignId).ToList()
                    .Except(Campaigns.Select(x => x.CampaignId)).ToList();

                if (except.Count() > 0) {
                    var campaignsNotAdded = shopkeeper.Campaigns.Where(x => except.Contains(x.CampaignId));
                    if (campaignsNotAdded != null && campaignsNotAdded.Count() > 0) {
                        campaignsNotAdded.ToList().ForEach(c => Campaigns.Add(c));
                    }
                }
            }
        }

        public void UpdateUserFields(User user) {
            user.Name = Name;
            user.UpdateDate = DateTime.UtcNow;
            if (IsPessoaFisica()) {
                user.Cpf = Document;
                user.Type = PersonType.Fisica;
            } else if (IsPessoaJuridica()) {
                user.Cnpj = Document;
                user.Type = PersonType.Juridica;
                user.CompanyName = Name;
            }

            if (Campaigns != null) {
                if (user.UsersParticipantCampaign != null) {
                    // Atualiza campanhas existentes
                    user.UsersParticipantCampaign.ForEach(participant => {
                        var shopkeeperCampaign = Campaigns.FirstOrDefault(c => c.CampaignId == participant.CampaignId);
                        if (shopkeeperCampaign != null) {
                            participant.Active = shopkeeperCampaign.Active;
                            participant.UpdateDate = DateTime.UtcNow;
                            if (participant.Addresses != null) {
                                var address = participant.Addresses.FirstOrDefault(a => a.MainAddress);
                                if (address != null) {
                                    this.UpdateAddressFields(address);
                                }
                            }
                            if (participant.Contact != null) {
                                this.UpdateContactFields(participant.Contact);
                            }
                        }
                    });

                    // Adiciona campanhas que não existiam
                    var except = Campaigns.Select(x => x.CampaignId).ToList()
                        .Except(user.UsersParticipantCampaign.Select(x => x.CampaignId)).ToList();

                    if (except.Count() > 0) {
                        var campaignsNotAdded = Campaigns.Where(x => except.Contains(x.CampaignId));
                        if (campaignsNotAdded != null && campaignsNotAdded.Count() > 0) {
                            campaignsNotAdded.ToList().ForEach(campaign => {
                                user.UsersParticipantCampaign.Add(this.CreateParticipant(user, campaign));
                            });
                        }
                    }
                } else { // Cria participantes
                    user.UsersParticipantCampaign = new List<UserParticipantCampaign>();
                    Campaigns.ForEach(campaign => {
                        user.UsersParticipantCampaign.Add(this.CreateParticipant(user, campaign));
                    });
                }
            }
        }

        private UserParticipantCampaign CreateParticipant(User user, ShopkeeperCampaign campaign) {
            var participant = new UserParticipantCampaign();
            participant.Id = Guid.NewGuid();
            participant.UserId = user.Id;
            participant.CampaignId = campaign.CampaignId;
            participant.Active = campaign.Active;
            participant.CreateDate = DateTime.UtcNow;
            participant.Type = ParticipantType.B2B;
            participant.Login = Document;
            participant.Password = new Senha($"!MA-{Document}", $"!MA-{Document}");
            participant.AllowLoginSite = true;
            participant.FirstAccess = false;
            participant.Contact = new Contact {
                Id = Guid.NewGuid(),
                UserId = user.Id,
                CampaignId = campaign.CampaignId,
                ParticipantId = participant.Id,
                Active = true,
                CreateDate = DateTime.UtcNow,
                CommercialPhone = BusinessPhone,
                MainEmail = Email,
                MainPhone = MainPhone,
                MobilePhone = MobilePhone,
                CommercialEmail = Email,
            };
            if (Address != null) {
                participant.Addresses = new List<Address>();
                participant.Addresses.Add(new Address {
                    Id = Guid.NewGuid(),
                        Active = true,
                        UserId = user.Id,
                        UserParticipantCampaignId = participant.Id,
                        CampaignId = campaign.CampaignId,
                        CreateDate = DateTime.UtcNow,
                        MainAddress = true,
                        AddressName = "Endereço Principal",
                        Cep = Address.Cep,
                        Street = Address.Street,
                        Number = Address.Number,
                        Complement = Address.Complement,
                        Reference = Address.Reference,
                        Neighborhood = Address.Neighborhood,
                        City = Address.City,
                        State = Address.State
                });
            }

            return participant;
        }

        public User CreateUser() {
            var user = new User();
            user.Id = Id;
            // user.Identifier = ObjectId.GenerateNewId();
            user.Name = Name;
            user.BusinessType = UserBusinessType.B2B;
            user.CreateDate = DateTime.UtcNow;
            user.Active = true;

            if (IsPessoaFisica()) {
                user.Cpf = Document;
                user.Type = PersonType.Fisica;
            } else if (IsPessoaJuridica()) {
                user.Cnpj = Document;
                user.Type = PersonType.Juridica;
                user.CompanyName = Name;
            }

            if (Campaigns != null) {
                user.UsersParticipantCampaign = new List<UserParticipantCampaign>();
                Campaigns.ForEach(campaign => {
                    user.UsersParticipantCampaign.Add(this.CreateParticipant(user, campaign));
                });
            }

            return user;
        }

        public override void Validate() {
            if (Id == Guid.Empty) {
                Id = Guid.NewGuid();
                CreateDate = DateTime.UtcNow;
                Active = true;

                if (Address != null) {
                    Address.Id = Guid.NewGuid();
                    Address.Active = true;
                    Address.CreateDate = DateTime.UtcNow;
                }
            }

            if (!IsPessoaFisica() && !IsPessoaJuridica()) {
                throw MotivaiException.ofValidation("Preencha um CPF ou CNPJ válido");
            }

            Document.ForNullOrEmpty("Documento é obrigatório");
            Name.ForNullOrEmpty("Nome é obrigatório");

            if (Campaigns == null || !Campaigns.Any()) {
                throw MotivaiException.ofValidation("Informe ao menos uma campanha para o lojista");
            }
        }
    }
}