using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Strings;

namespace Motivai.Users.Domain.Entities.OperatorMigration
{
    public class OperatorMigrationModel
    {
        public string Name { get; set; }
        public string Document { get; set; }
        public string Email { get; set; }
        public List<Guid> CampaignIds { get; set; }
        public string AccessType { get; set; }
        public string CommunicationOptIn { get; set; }

        public void Validate()
        {
            if (String.IsNullOrEmpty(Name))
                throw MotivaiException.ofValidation("Nome informado inválido.");

            if (String.IsNullOrEmpty(Document))
                throw MotivaiException.ofValidation("Documento do operador inválido.");

            if (String.IsNullOrEmpty(Email))
                throw MotivaiException.ofValidation("Email informado inválido.");

            if (CampaignIds == null || CampaignIds.Count == 0)
                throw MotivaiException.ofValidation("Para migrar operador deve ser informado pelo menos uma campanha.");

            Document = Document.Trim();
            Document = Extractor.RemoveMasks(Document);

            if (!Cpf.IsCpf(Document))
                throw MotivaiException.ofValidation("Número de documento do operador inválido.");

            Email = Email.Trim();
        }
    }
}