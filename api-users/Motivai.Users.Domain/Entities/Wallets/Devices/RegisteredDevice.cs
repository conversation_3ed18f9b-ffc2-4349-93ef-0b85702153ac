using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Users.Domain.Entities.Wallets.Devices {
    public class RegisteredDevice : BaseEntity {
        public DateTime AuthorizationDate { get; set; }
        public Guid CampaignId { get; set; }

        public DateTime TokenRequestDate { get; set; }
        public DateTime TokenConfirmationDate { get; set; }

        public DeviceDetails Device { get; set; }
        public DeviceRequestInfo DeviceRequestInfo { get; set; }

        [BsonRepresentation(BsonType.String)]
        public DeviceStatus Status { get; set; }

        public RegisteredDevice() { }

        public RegisteredDevice(DeviceAuthorizationRequest deviceAuthorizationRequest) {
            this.Id = Guid.NewGuid();
            this.CreateDate = DateTime.UtcNow;
            this.TokenRequestDate = deviceAuthorizationRequest.TokenRequestDate;
            this.TokenConfirmationDate = deviceAuthorizationRequest.TokenConfirmationDate;
            this.SetRequestInfo(deviceAuthorizationRequest.DeviceRequestInfo);
        }

        public static RegisteredDevice FromAuthorizationRequest(DeviceAuthorizationRequest deviceAuthorizationRequest) {
            var device = new RegisteredDevice(deviceAuthorizationRequest);
            device.Validate();
            return device;
        }

        public void SetRequestInfo(DeviceRequestInfo deviceRequestInfo) {
            this.Device = deviceRequestInfo.Device;
            this.CampaignId = deviceRequestInfo.CampaignId;
            this.DeviceRequestInfo = deviceRequestInfo;
            this.DeviceRequestInfo.Device = null;
        }

        public void Authorizate() {
            this.Active = true;
            this.Status = DeviceStatus.Authorized;
            this.AuthorizationDate = DateTime.UtcNow;
        }

        public override void Validate() {
            if (CreateDate == null)
                CreateDate = DateTime.UtcNow;

            if (AuthorizationDate == null)
                AuthorizationDate = DateTime.UtcNow;

            this.Device.Validate();
        }
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum DeviceStatus {
        Created,
        Authorized,
        Blocked
    }
}