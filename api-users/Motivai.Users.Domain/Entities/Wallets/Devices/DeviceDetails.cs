using System;
namespace Motivai.Users.Domain.Entities.Wallets.Devices {
    public class DeviceDetails {
        public string DeviceId { get; set; }
        public string DeviceName { get; set; }
        public string DeviceType { get; set; }

        public string DeviceBrand { get; set; }
        public string DeviceOS { get; set; }
        public string DeviceOSId { get; set; }
        public string DeviceOSVersion { get; set; }

        public string DeviceLocale { get; set; }
        public string DeviceTimezone { get; set; }
        public string DeviceMacAddress { get; set; }

        public string DeviceAppVersion { get; set; }
        public string DeviceBuildNumber { get; set; }

        public string DeviceIpAddress { get; set; }

        public void Validate() {
            if (DeviceId == null)
                DeviceId = Guid.NewGuid().ToString();
        }
    }
}