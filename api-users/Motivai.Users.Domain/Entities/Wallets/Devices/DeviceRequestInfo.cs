using System;

namespace Motivai.Users.Domain.Entities.Wallets.Devices
{
	///<summary>
	/// Nota: Será movido para o SharedKernel após testes.
	///</summary>
	public class DeviceRequestInfo
	{
		public Guid CampaignId { get; set; }
		public DateTime RequestDate { get; set; }
		public string Timezone { get; set; }

		public DeviceDetails Device { get; set; }
		public NetworkInfo Network { get; set; }

		public DeviceAppInfo AppInfo { get; set; }
		public String UserAgent { get; set; }
	}

	public class NetworkInfo
	{
		public String RemoteAddress { get; set; }
		public int RemotePort { get; set; }
		public int LocalPort { get; set; }
		public String UserAgent { get; set; }
	}

	public class DeviceAppInfo
	{
		public String Version { get; set; }
		public String BuildNumber { get; set; }
	}
}