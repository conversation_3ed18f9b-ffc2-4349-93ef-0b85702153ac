using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Users.Domain.Entities.CreditCards;
using Motivai.Users.Domain.Entities.Wallets.Devices;

namespace Motivai.Users.Domain.Entities.Wallets
{
    public class Wallet
    {
        ///<summary>
        /// Senha transacional no aplicativo e, posteriormente, na plataforma.
        ///</summary>
        public TransactionalConfiguration Transactional { get; set; }

        ///<summary>
        /// Dispositivos autorizados para transacionar pelo aplicativo.
        ///</summary>
        public List<RegisteredDevice> AuthorizedDevices { get; set; }

        ///<summary>
        ///
        ///</summary>
        public List<UserCreditCard> CreditCards { get; set; }

        ///<summary>
        /// Cartões usados no app VR-FLEX.
        ///</summary>
        public List<Card> Cards { get; set; }
    }
}