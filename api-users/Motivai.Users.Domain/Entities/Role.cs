using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Enums.UsersAdministration;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities
{
    public class Role : BaseEntity
    {
        public string Name { get; set; }
        ///<summary>
        /// Se for Master então listará todas as permissões do sistema na tela de Perfil.
        ///</summary>
        public bool Master { get; set; }
        public List<string> Permissions { get; set; }
        public bool GenerateToken { get; set; }
        public string Token { get; set; }
        public Guid BuId { get; set; }
        public OperationUser OperationUser { get; set; }
        public LocationInfo LocationInfo { get; set; }

        [BsonRepresentation(BsonType.String)]
        public StartComponentType? StartComponent { get; set; }

        [BsonRepresentation(BsonType.String)]
        public LayoutType? LayoutType { get; set; }

        public override void Validate()
        {
            Name.ForNullOrEmpty("Nome do perfil é obrigatório.");
            if (Permissions == null || Permissions.Count == 0)
                throw MotivaiException.ofValidation("Perfil deve ter pelo menos uma permissão.");
        }
    }
}