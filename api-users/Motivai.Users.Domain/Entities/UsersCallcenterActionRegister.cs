using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;

namespace Motivai.Users.Domain.Entities {
    public class UsersCallcenterAction {
        public Guid Id { get; set; }
        public DateTime CreateDate { get; set; }
        public Guid UserId { get; set; }
        public Guid CampaignId { get; set; }
        public string Action { get; set; }
        public string Reason { get; set; }
        public OperationUser OperationUser { get; set; }
        public Dictionary<string, string> Before { get; set; }
        public Dictionary<string, string> After { get; set; }

        public Contact ToContact()
        {
            string mainEmail = null;
            string mobilePhone = null;

            if (After != null)
            {
                After.TryGetValue("mainEmail", out mainEmail);
                After.TryGetValue("mobilePhone", out mobilePhone);
            }

            return new Contact()
            {
                MainEmail = mainEmail,
                MobilePhone = mobilePhone,
                MainEmailLastUpdateDate = DateTime.UtcNow,
                MobilePhoneLastUpdateDate = DateTime.UtcNow
            };
        }

        public void Validate() {
            if (Id == Guid.Empty) {
                Id = Guid.NewGuid();
                CreateDate = DateTime.UtcNow;
            }
        }

    }
}