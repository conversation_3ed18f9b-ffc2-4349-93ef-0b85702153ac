﻿using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Time;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using System.Linq;
using Motivai.Users.Domain.Entities.ParticipantHistoryFiles;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Enums;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.SharedKernel.Domain.Entities.References.Events;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.Models.PrivacyPolicy;
using Motivai.SharedKernel.Domain.Entities.Users;

namespace Motivai.Users.Domain.Entities
{
    public class UserParticipantCampaign : BaseEntity {
        ///<summary>
        /// Identificar externo.
        ///</summary>
        // public ObjectId Identifier { get; set; }

        public Guid UserId { get; set; }

        /// <summary>
        /// Referência com a tabela: Campanha
        /// </summary>
        public Guid CampaignId { get; set; }

        // public ObjectId CampaignIdentifier { get; set; }

        [BsonRepresentation(BsonType.String)]
        public ParticipantType Type { get; set; }

        ///<summary>
        /// Origem do participante.
        /// - ParticipantsImport
        /// - ParticipantImportApi
        /// - IntegratedLogin
        /// - PreRegistration
        /// - CampaignsGroupReplication
        /// - ExternalParticipantsApi
        /// - AdminParticipantManagement
        /// - CashbackApp
        /// - ManualHandled
        ///</summary>
        public string Origin { get; set; }

        public Guid? RankingId { get; set; }

        /// <summary>
        /// Login do participante na campanha associada.
        /// </summary>
        public string Login { get; set; }

        ///<summary>
        /// Define se o login veio e é integrado.
        ///</summary>
        public bool IntegratedLogin { get; set; }

        /// <summary>
        /// Senha do participante na campanha associada.
        /// </summary>
        public Senha Password { get; set; }

        /// <summary>
        /// Permite que o participante se logue no site com usuário e senha.
        /// </summary>
        public bool AllowLoginSite { get; set; }

        /// <summary>
        /// Saldo total disponível do Participante
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// Representante da conta CNPJ
        /// </summary>
        public AccountRepresentative AccountRepresentative { get; set; }

        public Guid? ParentUserId { get; set; }
        public UserBasicInfo ParentUserDetails { get; set; }

        /// <summary>
        /// Saldo disponível por Mecânicas de pontos do Participante.
        /// </summary>
        public List<MechanicTotalPoints> TotalBalanceMechanic { get; set; }

        /// <summary>
        /// Informa se será o Primeiro Acesso do Participante, ao importar
        /// um novo participante deve-se setar com True.
        /// </summary>
        public bool FirstAccess { get; set; }
        public DateTime? FirstAccessDate { get; set; }

        public DateTime? LastAccessDate { get; set; }
        public string LastAccessTimezone { get; set; }

        ///<summary>
        /// Contador de tentativas de login excedidas.
        ///</summary>
        public int? LoginAttempts { get; set; }

        public bool Blocked { get; set; }
        public BlockingDetails BlockingDetails { get; set; }

        ///<summary>
        /// Data de início do bloqueio do participante.
        /// Será verificado no momento do login até que seja feito o serviço.
        ///</summary>
        public DateTime? BlockAccessAfterDate { get; set; }

        [BsonRepresentation(BsonType.String)]
        public BlockingOrigin? BlockingOrigin { get; set; }

        public DateTime? LastRegistrationReviewDate { get; set; }
        // Próxima data para forçar revisão dos dados (utilizado no site campanha com layout BenefitsClub)
        public DateTime? NextUpdateDate { get; set; }

        public Guid? BatchId { get; set; }
        public bool CreatedByIntegration { get; set; }
        public bool CreatedByImport { get; set; }

        public Contact Contact { get; set; }
        public List<Address> Addresses { get; set; }

        public string ClientUserId { get; set; }

        // Ate finalizar migration
        public Employee Employee { get; set; }

        /// <summary>
        /// Contém as Hierarquias dos Usuários. Exemplos: Cargo: Diretor; Departamento: xyz, Posicao: xyz
        /// </summary>
        public List<KeyValueGP> Hierarchies { get; set; }

        /// <summary>
        /// Dados da empresa do usuário que foi integrado.
        /// </summary>
        /// <returns></returns>
        public IntegrationCompany IntegrationCompany { get; set; }

        public List<ParticipantHistoryFile> ParticipantHistoryFiles { get; set; }

        ///<summary>
        /// Informações contendo a origem e o rastreio da criação do participante
        ///</summary>
        public EventInfo CreateEventInfo { get; set; }

        ///<summary>
        /// Informações contendo a origem e o rastreio da atualização do participante
        ///</summary>
        public EventInfo UpdateEventInfo { get; set; }

		public RequestInformation RequestInformation { get; set; }
        public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }

        public PrivacySettings PrivacySettings { get; set; }

        public static UserParticipantCampaign CreateForCampaign(Guid campaignId, CampaignSettingsModel campaignSettings,
            User user, string login = null)
        {
            var generatedPassword = AlphanumericGenerator.GenerateId16();
            return new UserParticipantCampaign()
            {
                Id = Guid.NewGuid(),
                // Identifier = ObjectId.GenerateNewId(),
                Active = true,
                AllowLoginSite = true,
                UserId = user.Id,
                CampaignId = campaignId,
                Type = campaignSettings.Type.IsB2B() ? ParticipantType.B2B : ParticipantType.Rewards,
                Addresses = new List<Address>(1),
                CreateDate = DateTime.UtcNow,
                Login = string.IsNullOrEmpty(login) ? user.GetDocument() : login,
                Password = new Senha(generatedPassword, generatedPassword),
                FirstAccess = campaignSettings.Parametrizations.EnableFirstAccess,
                NextUpdateDate = DateTime.UtcNow.AtStartOfDay()
            };
        }

		public void OverridePassword(string rawPassword)
        {
            this.Password = new Senha(rawPassword, rawPassword);
        }

        public string GetMainEmail()
        {
            if (Contact == null)
                return null;
            if (!string.IsNullOrEmpty(Contact.MainEmail))
                return Contact.MainEmail;
            if (!string.IsNullOrEmpty(Contact.PersonalEmail))
                return Contact.PersonalEmail;
            if (!string.IsNullOrEmpty(Contact.CommercialEmail))
                return Contact.CommercialEmail;
            return null;
        }

        public string GetPersonalEmail()
        {
            if (Contact == null) return null;
            return Contact.PersonalEmail;
        }

        public string GetCommercialEmail()
        {
            if (Contact == null) return null;
            return Contact.CommercialEmail;
        }

        public string GetMobilePhone()
        {
            if (Contact == null) return null;
            return Contact.MobilePhone;
        }

        public string GetMainPhone()
        {
            if (Contact == null) return null;
            return Contact.MainPhone;
        }

        public string GetHomePhone()
        {
            if (Contact == null) return null;
            return Contact.HomePhone;
        }

        public string GetCommercialPhone()
        {
            if (Contact == null) return null;
            return Contact.CommercialPhone;
        }

        public DateTime? GetMainPhoneLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.MainPhoneLastUpdateDate;
        }

        public DateTime? GetMobilePhoneLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.MobilePhoneLastUpdateDate;
        }

        public DateTime? GetHomePhoneLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.HomePhoneLastUpdateDate;
        }

        public DateTime? GetCommercialPhoneLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.CommercialPhoneLastUpdateDate;
        }

        public DateTime? GetMainEmailLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.MainEmailLastUpdateDate;
        }

        public DateTime? GetPersonalEmailLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.PersonalEmailLastUpdateDate;
        }

        public DateTime? GetCommercialEmailLastUpdateDate()
        {
            if (Contact == null) return null;
            return Contact.CommercialEmailLastUpdateDate;
        }

        public string GetCompanyIdentifier()
        {
            if (Employee != null && Employee.CompanyIdentifier != null)
            {
                return Employee.CompanyIdentifier;
            }
            if (IntegrationCompany != null && IntegrationCompany.CompanyIdentifier != null)
            {
                return IntegrationCompany.CompanyIdentifier;
            }
            return null;
        }

        public override void Validate()
        {
            this.ValidateByCampaignSettings(null);
        }

        public void ValidateByCampaignSettings(CampaignSettingsModel campaignSettings = null)
        {
            Login.ForNullOrEmpty("LOGIN_REQUIRED", "Deve ser informado o Login do participante");
            Login = Login.ToLower().Trim();

            if (CampaignId == Guid.Empty)
                throw MotivaiException.of("CAMPAIGN_INVALID", "Deve ser informado o ID da Campanha");

            if (campaignSettings == null)
            {
                if (AllowLoginSite && (Password == null || Password.Descricao == null))
                    throw MotivaiException.of("PASSWORD_REQUIRED", "Deve ser informado a senha do participante.");
            }
            else
            {
                if (campaignSettings.Parametrizations.LoginType.IsLoginWithPassword())
                {
                    if (AllowLoginSite && (Password == null || Password.Descricao == null))
                        throw MotivaiException.of("PASSWORD_REQUIRED", "Deve ser informado a senha do participante.");
                }
            }
        }

        public DateTime GetLastAccess()
        {
            if (LastAccessDate.HasValue)
                return LastAccessDate.Value;

            return DateTime.UtcNow;
        }

        public void RegisterFirstAccess()
        {
            FirstAccess = false;
            FirstAccessDate = DateTime.UtcNow;
            if (!this.LastRegistrationReviewDate.HasValue)
            {
                this.LastRegistrationReviewDate = FirstAccessDate;
            }
        }

        public void UpdateNextReviewDate(Periodicity periodicity)
        {
            if (periodicity == Periodicity.Never) return;
            var today = DateTime.UtcNow;
            this.LastRegistrationReviewDate = today;
            this.NextUpdateDate = periodicity.CalculateNextDate(today);
        }

        // Atualiza as datas de acesso se ele logar em um dia diferente do anterior
        public void UpdateLastAccess()
        {
            var dateNow = DateTime.UtcNow;

            if (LastAccessDate.HasValue)
            {
                // Se logou em um dia diferente atualiza a data
                if (dateNow.DayOfYear != LastAccessDate.Value.DayOfYear || dateNow.Year != LastAccessDate.Value.Year)
                    LastAccessDate = dateNow;
            }
            else
            {
                LastAccessDate = dateNow;
            }
        }

        public bool IsPrivacyPolicyPendingToAccept()
        {
            return PrivacySettings == null || PrivacySettings.IsPendingToAccept();
        }

		public void UpdatePrivacySettings(PrivacyPolicyResult privacyPolicyResult,
			bool acceptedCampaignCommunications, bool acceptedPartnerCommunications)
		{
			if (this.PrivacySettings == null)
			{
				this.PrivacySettings = PrivacySettings.Create();
			}
			if (privacyPolicyResult != null)
			{
				PrivacySettings.ContentId = privacyPolicyResult.ContentId;
				PrivacySettings.Version = privacyPolicyResult.Version;
				PrivacySettings.PrivacyPolicyAccepted = privacyPolicyResult.Accepted;
				PrivacySettings.EssentialsCookies = privacyPolicyResult.EssentialsCookies;
				PrivacySettings.AnalyticsCookies = privacyPolicyResult.AnalyticsCookies;
			}
			PrivacySettings.CampaignCommunicationsAccepted = acceptedCampaignCommunications;
			PrivacySettings.PartnerCommunicationsAccepted = acceptedPartnerCommunications;
			PrivacySettings.UpdateDate = DateTime.UtcNow;
		}

        public Address GetMainAddress()
        {
            var mainAddress = this.Addresses.Where(a => a.Active && a.MainAddress).FirstOrDefault();

            if (mainAddress == null)
                throw MotivaiException.of("ADDRESS_MAIN_NOT_FOUND", "Endereço não encontrado.");

            return mainAddress;
        }

        public void UpdateMainAddress(Address address)
        {
            if (address == null)
                return;
            int index = -1;
            if (!Addresses.IsNullOrEmpty())
            {
                index = Addresses.FindIndex(a => a.Active && a.MainAddress);
            }
            if (index >= 0)
            {
                address.Active = true;
                address.MainAddress = true;
                this.Addresses[index] = address;
            }
            else
            {
                this.AddAddress(address);
            }

        }

        public void AddAddress(Address address, bool validate = true) {
            if (address == null)
                throw MotivaiException.of("ADDRESS_INVALID", "Endereço inválido.");
            if (Addresses == null)
                Addresses = new List<Address>(1);

            address.OnInit();
            if (validate) {
                address.Validate();
            }

            address.UserId = UserId;
            address.CampaignId = CampaignId;
            address.UserParticipantCampaignId = Id;

            if (Addresses.Count == 0)
                address.MainAddress = true;
            else if (address.MainAddress)
                SetMainAddress(address);
            else
                VerifyMainAddress();
            Addresses.Add(address);
        }

        public void SetMainAddress(Address dbAddress)
        {
            if (Addresses != null)
            {
                foreach (var address in Addresses)
                {
                    address.MainAddress = false;
                }
            }
            dbAddress.MainAddress = true;
        }

        public void VerifyMainAddress()
        {
            if (Addresses == null || Addresses.Count == 0) return;
            if (Addresses.Any(a => a.Active && a.MainAddress)) return;
            var first = Addresses.FirstOrDefault(a => a.Active);
            if (first != null)
            {
                first.MainAddress = true;
            }
        }

        private void ValidateStateForSession() {
            if (!Active)
                throw MotivaiException.of("PARTICIPANT_INACTIVE", "Participante desativado.");
            if (Blocked)
                throw MotivaiException.of("PARTICIPANT_BLOCKED", "Participante bloqueado.");
            if (BlockAccessAfterDate.HasValue && BlockAccessAfterDate.Value < DateTime.UtcNow) {
                throw MotivaiException.of("PARTICIPANT_BLOCKED", "Participante bloqueado.");
            }
        }

        public void ValidateForSession() {
            this.ValidateStateForSession();
            if (!AllowLoginSite)
                throw MotivaiException.of("PARTICIPANT_CANNOT_LOGIN", "O usuário não está permitido para logar no site.");
        }

        public void ValidateForMobileSession() {
            this.ValidateStateForSession();
        }

		public bool NeedSetupAuthenticationMfa()
		{
            return AuthenticationMfaSettings == null || AuthenticationMfaSettings.NeedSetupAuthenticationMfa;
		}

		public void Block(Enums.BlockingOrigin blockingOrigin, String reason, OperationUser operationUser) {
            this.BlockingOrigin = blockingOrigin;
            this.Blocked = true;
            this.BlockingDetails = BlockingDetails.Of(reason, operationUser);
		}

        public void UpdateFromUserExternal(UserExternalModel userExternalModel)
        {
            this.IntegrationCompany = userExternalModel.IntegrationCompany;

            if (userExternalModel.Address != null)
            {
                this.UpdateMainAddress(userExternalModel.Address);
            }

            if (userExternalModel.HasCompanyIntegrationData())
            {
                this.Employee = new Employee()
                {
                    CompanyName = userExternalModel.IntegrationCompany.CompanyName,
                    CompanyIdentifier = userExternalModel.IntegrationCompany.CompanyIdentifier
                };
            }
        }

		public UserParticipantCampaign Clone() {
            return new UserParticipantCampaign() {
				Id = Guid.NewGuid(),
                // Identifier = ObjectId.GenerateNewId(),
				Active = this.Active,
				CreateDate = this.CreateDate,
				UpdateDate = this.UpdateDate,
				UserId = this.UserId,
				CampaignId = this.CampaignId,
				Login = this.Login,
				Password = this.Password,
				Balance = this.Balance,
				AllowLoginSite = this.AllowLoginSite,
				FirstAccess = this.FirstAccess,
				FirstAccessDate = this.FirstAccessDate,
				LastAccessDate = this.LastAccessDate,
				LastAccessTimezone = this.LastAccessTimezone,
				LastRegistrationReviewDate = this.LastRegistrationReviewDate,
				NextUpdateDate = this.NextUpdateDate,
				LoginAttempts = this.LoginAttempts,
				Blocked = this.Blocked,
				BlockingOrigin = this.BlockingOrigin,
                BlockingDetails = this.BlockingDetails,
				BlockAccessAfterDate = this.BlockAccessAfterDate,
				RankingId = this.RankingId,
				IntegratedLogin = this.IntegratedLogin,
				CreatedByImport = this.CreatedByImport,
				ParentUserId = this.ParentUserId,
				ParentUserDetails = this.ParentUserDetails,
				ParticipantHistoryFiles = this.ParticipantHistoryFiles,
				Contact =  this.Contact,
				Addresses = this.Addresses,
				IntegrationCompany = this.IntegrationCompany,
				CreatedByIntegration = this.CreatedByIntegration,
				Employee = this.Employee,
				Hierarchies = this.Hierarchies,
                CreateEventInfo = this.CreateEventInfo,
                UpdateEventInfo = this.UpdateEventInfo,
                AccountRepresentative = this.AccountRepresentative,
                ClientUserId = this.ClientUserId,
                BatchId = this.BatchId,
                Origin = this.Origin
			};
        }

        public UserParticipantCampaign CloneForCampaign(Guid targetCampaignId, CampaignSettingsModel targetCampaignSettings) {
            var participant = Clone();
            participant.CampaignId = targetCampaignId;
            participant.Balance = 0;
            participant.FirstAccess = targetCampaignSettings.Parametrizations.EnableFirstAccess;
            participant.FirstAccessDate = null;
            participant.LastAccessDate = null;
            participant.LoginAttempts = 0;
            participant.LastAccessTimezone = null;
            if (participant.ParentUserDetails != null)
            {
                participant.ParentUserDetails.CampaignId = targetCampaignId;
            }
            if (!participant.Addresses.IsNullOrEmpty())
            {
                participant.Addresses.ForEach(a => {
                    a.CampaignId = targetCampaignId;
                    a.UserParticipantCampaignId = participant.Id;
                });
            }
            return participant;
        }
    }

    // Ate finalizar migration
    public class Employee
    {
        public string CompanyName { get; set; }
        public string CompanyIdentifier { get; set; }
        public string Direction { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
    }
}
