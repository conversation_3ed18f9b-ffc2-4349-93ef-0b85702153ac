using System;
using Motivai.Users.Domain.Models.PrivacyPolicy;

namespace Motivai.Users.Domain.Entities.Privacy
{
	public class PrivacySettings
	{
		public DateTime CreateDate { get; set; }
		public DateTime UpdateDate { get; set; }

		public string ContentId { get; set; }
		public string Version { get; set; }
		///<summary>
		/// Registra o status atual do aceite da privacidade.
		/// Pode revogar o consentimento.
		///</summary>
		public bool PrivacyPolicyAccepted { get; set; }

		public bool EssentialsCookies { get; set; }
		public bool AnalyticsCookies { get; set; }

		///<summary>
		/// Registra a solicitação da remoção dos dados.
		/// `A eliminação pode ser revogada com base legal ou no Marco Civil`.
		///</summary>
		public bool RequestedInformationRemoval { get; set; }
		public DateTime? InformationRemovalRequestDate { get; set; }

		public bool CampaignCommunicationsAccepted { get; set; }
		public bool PartnerCommunicationsAccepted { get; set; }

		public static PrivacySettings Create()
		{
			return new PrivacySettings()
			{
				CreateDate = DateTime.UtcNow,
				UpdateDate = DateTime.UtcNow,
			};
		}

		internal bool IsNewerThan(PrivacySettings privacy)
		{
			return !this.Version.Equals(privacy.Version) && this.UpdateDate > privacy.UpdateDate;
		}

		public bool IsPendingToAccept()
		{
			// não está usando `PrivacyPolicyAccepted` porque pode ser revogado, nesse caso vamos ter a versão que ele rejeitou.
			return string.IsNullOrEmpty(ContentId) || string.IsNullOrEmpty(Version);
		}
	}
}