using System;

namespace Motivai.Users.Domain.Entities.Participants {
	public class UserParentDetails {
		public Guid UserId { get; set; }
		public Guid? ParentUserId { get; set; }
		public UserBasicInfo ParentDetails { get; set; }

		public bool HasAnyParentInfo() {
			return HasParent() || HasParentDocument();
		}

		public bool HasParent() {
			return ParentUserId.HasValue && ParentUserId.Value != Guid.Empty;
		}

		public bool HasParentDocument() {
			return ParentDetails != null && !string.IsNullOrEmpty(ParentDetails.Document);
		}

		public string GetParentDocument() {
			if (ParentDetails == null)
				return null;
			return ParentDetails.Document;
		}

		public void SetParent(UserBasicInfo parent) {
			if (parent == null)
				return;
			this.ParentUserId = parent.UserId;
			this.ParentDetails = parent;
		}

		public bool IsUserParent(Guid userId) {
			return HasParent() && ParentUserId == userId;
		}

		public bool HasSameParenthierarchy(UserParentDetails userParent) {
			if (userParent == null)
				return false;
			if (this.HasParent()) {
				if (userParent.HasParent())
					return this.ParentUserId == userParent.ParentUserId;
				else
					return this.ParentUserId == userParent.UserId;
			} else if (userParent.HasParent()) {
				return this.UserId == userParent.ParentUserId;
			}
			return false;
		}
	}
}