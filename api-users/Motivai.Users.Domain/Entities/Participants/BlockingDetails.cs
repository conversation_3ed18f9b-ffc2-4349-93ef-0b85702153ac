using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Enums;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.Participants
{
    public class BlockingDetails
    {
        public string Reason { get; set; }
        public OperationUser OperationUser { get; set; }
        public DateTime BlockingDate { get; set; }

        public static BlockingDetails Of(string reason, OperationUser operationUser)
        {
            return new BlockingDetails()
            {
                Reason = reason,
                OperationUser = operationUser,
                BlockingDate = DateTime.UtcNow,
            };
        }

        public static BlockingDetails OfLogin(string reason) {
            return new BlockingDetails() {
                Reason = reason,
                BlockingDate = DateTime.UtcNow,
            };
        }
    }
}
