using System;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.ParticipantHistoryFiles {
    public class ParticipantHistoryFile : BaseEntity {
        public string Name { get; set; }
        public string Url { get; set; }
        public UserCreation UserCreation { get; set; }
        public DateTime? DeleteAt { get; set; }

        public override void Validate() {
            Name.ForNullOrEmpty("Nome é obrigatório");
            Url.ForNullOrEmpty("Erro ao gerar URL do arquivo");

            if (Id == Guid.Empty) {
                Id = Guid.NewGuid();
                Active = true;
                CreateDate = DateTime.UtcNow;
            }

            if (UserCreation == null) {
                throw MotivaiException.ofValidation("Usuário do Admin não encontrado.");
            }
        }
    }
}