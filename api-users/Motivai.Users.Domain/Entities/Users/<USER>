using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.Users.Domain.Entities.Participants;

namespace Motivai.Users.Domain.Entities.Users
{
    public class ParticipantManagementModel
    {
        public Guid UserId { get; set; }
        public Guid ParticipantID { get; set; }
        public Guid CampaignId { get; set; }
        public Guid? RankingId { get; set; }

        public DateTime? CreateDate { get; set; }
        public DateTime? FirstAccessDate { get; set; }
        public DateTime? LastAccessDate { get; set; }

        public string BirthDate { get; set; }
        public String ClientUserId { get; set; }
        public string Cnpj { get; set; }
        public string CompanyName { get; set; }
        public string Cpf { get; set; }
        public string Gender { get; set; }
        public String Login { get; set; }
        public string MaritalStatus { get; set; }
        public string Name { get; set; }
        public string PhotoUrl { get; set; }
        public string Rg { get; set; }
        public string StateInscription { get; set; }
        public string StateInscriptionUf { get; set; }

        public Contact Contact { get; set; }
        public AccountRepresentative AccountRepresentative { get; set; }

        public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }
        public PersonType Type { get; set; }

        public bool Active { get; set; }
        public bool AllowLoginSite { get; set; }
        public bool Blocked { get; set; }
        public bool FirstAccess { get; set; }
        public bool GpInf { get; set; }
        public bool GpPartnerInf { get; set; }
        public bool StateInscriptionExempt { get; set; }

        public Dictionary<string, string> Metadata { get; set; }
		public List<string> GroupsCodes { get; set; }

        public OperationUser OperationUser { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public BlockingDetails BlockingDetails { get; set; }

        public bool FillDocument
        {
            get
            {
                return Cpf == null && Cnpj == null;
            }
        }

        public bool IsPF()
        {
            return Type == PersonType.Fisica;
        }

        public bool IsPJ()
        {
            return Type == PersonType.Juridica;
        }

		public String GetDocument() {
			return Type == PersonType.Fisica ? Cpf : Cnpj;
		}

		public bool HasAnyGroup()
		{
			return !GroupsCodes.IsNullOrEmpty();
		}

        public bool HasMetadata()
        {
            return Metadata != null && Metadata.Count > 0;
        }
    }
}
