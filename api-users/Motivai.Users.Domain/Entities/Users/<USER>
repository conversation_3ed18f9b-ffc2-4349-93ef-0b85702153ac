using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.Users
{
    public class UnblockingParticipantDetails
    {
        public string Reason { get; set; }
        public OperationUser OperationUser { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public void Validate()
        {
            if (string.IsNullOrEmpty(Reason))
                throw MotivaiException.ofValidation("PARTICIPANT_UNBLOCKING_ERROR",
						"Motivo do desbloqueio não pode ser vázio.");
        }
    }
}
