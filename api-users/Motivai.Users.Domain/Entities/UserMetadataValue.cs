using System;
using System.Collections.Generic;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities {
	public class UserMetadataValue {

		[BsonElement("id")]
		public Guid Id { get; set; }

		[BsonElement("origin")]
		public string Origin { get; set; }

		[BsonElement("createDate")]
		public DateTime CreateDate { get; set; }

		[BsonElement("updateDate")]
		public DateTime UpdateDate { get; set; }

		[BsonElement("campaignId")]
		public Guid CampaignId { get; set; }

		[BsonElement("userId")]
		public Guid UserId { get; set; }

		[BsonElement("participantId")]
		public Guid ParticipantId { get; set; }

		[BsonElement("metadata")]
		public Dictionary<string, string> Metadata { get; set; }

		public UserMetadataValue Clone() {
			return new UserMetadataValue() {
				Id = Guid.NewGuid(),
				Origin = this.Origin,
				CreateDate = DateTime.UtcNow,
				UpdateDate = DateTime.UtcNow,
				CampaignId = this.CampaignId,
				UserId = this.UserId,
				ParticipantId = this.ParticipantId,
				Metadata = this.Metadata
			};
		}
	}
}