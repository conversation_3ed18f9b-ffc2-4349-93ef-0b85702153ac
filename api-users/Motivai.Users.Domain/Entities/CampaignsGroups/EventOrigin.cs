using System;
using Motivai.Users.Domain.Entities.AccountOperators;

namespace Motivai.Users.Domain.Entities.CampaignsGroups
{
	public class EventOrigin
	{
		public Guid? OriginId { get; set; }
		public Guid CampaignId { get; set; }
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public Guid? AccountOperatorId { get; set; }
		public OperationOrigin? CreationOrigin { get; set; }

		public static EventOrigin Of(Guid originId, Guid campaignId, Guid userId, Guid participantId, Guid? accountOperatorId, OperationOrigin? creationOrigin)
		{
			return new EventOrigin()
			{
				OriginId = originId,
				CampaignId = campaignId,
				UserId = userId,
				ParticipantId = participantId,
				AccountOperatorId = accountOperatorId.HasValue && accountOperatorId.Value != Guid.Empty ? accountOperatorId : null,
				CreationOrigin = creationOrigin
			};
		}
	}
}
