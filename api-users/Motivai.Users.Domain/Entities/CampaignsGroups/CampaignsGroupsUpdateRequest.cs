using System;
using System.Security.Cryptography.X509Certificates;
using Motivai.Users.Domain.Entities.AccountOperators;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.CampaignsGroups
{
	public class CampaignsGroupsUpdateRequest
	{
		public DateTime EventDate { get; set; }

		[BsonRepresentation(BsonType.String)]
		public EventType EventType { get; set; }
		public EventOrigin EventOrigin { get; set; }
		public Guid TraceId { get; set; }

		public static CampaignsGroupsUpdateRequest Of(EventType eventType, Guid originId, Guid campaignId,
			Guid userId, Guid participantId, Guid? accountOperatorId, OperationOrigin? creationOrigin)
		{
			return new CampaignsGroupsUpdateRequest()
			{
				EventDate = DateTime.UtcNow,
				EventType = eventType,
				EventOrigin = EventOrigin.Of(originId, campaignId, userId, participantId, accountOperatorId, creationOrigin),
				TraceId = Guid.NewGuid()
			};
		}
	}
}
