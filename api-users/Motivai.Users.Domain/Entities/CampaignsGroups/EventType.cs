using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Users.Domain.Entities.CampaignsGroups
{
    [JsonConverter(typeof(StringEnumConverter))]
    public enum EventType
    {
        REGISTRATION_UPDATE = 0,
        ADDRESS_UPDATE = 1,
        PARTICIPANT_IMPORTED = 2,
        PARTICIPANT_PRE_REGISTER = 3,
        SSO_CREATION = 4,
        PASSWORD_UPDATE = 5,
        EXTERNAL_CREATION = 6,
        EXTERNAL_UPDATE = 7,
        CALLCENTER_CONTACT_UPDATE = 8,
        ACCOUNT_OPERATOR_UPDATE = 9
    }
}
