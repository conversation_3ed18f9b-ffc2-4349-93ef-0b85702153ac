using System;

using Motivai.Users.Domain.Entities.Wallets.Devices;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.CreditCards
{
    public class UserCreditCard
    {
        /// <summary>
        /// System ID
        /// </summary>
        /// <value></value>
        public Guid Id { get; set; }

        /// <summary>
        /// ID do Cartão de Crédito do Gateway de Pagamento
        /// </summary>
        /// <value></value>
        public string CardId { get; set; }

        /// <summary>
        /// Data de Criação do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// Data de Criação do Cartão de Crédito no Sistema.
        /// </summary>
        /// <value></value>
        public DateTime SystemCreatedDate { get; set; }

        /// <summary>
        /// Data de Alteração do Cartão de Crédito no Sistema.
        /// </summary>
        /// <value></value>
        public DateTime? SystemUpdateddDate { get; set; }

        /// <summary>
        /// Data de Atualização do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public DateTime? UpdatedDate { get; set; }

        /// <summary>
        /// Bandeira do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string Brand { get; set; }

        /// <summary>
        /// Nome do Titular do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string HolderName { get; set; }

        /// <summary>
        /// Primeiros dígitos do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string FirstDigits { get; set; }

        /// <summary>
        /// Últimos dígitos do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string LastDigits { get; set; }

        /// <summary>
        /// Data de expiração do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string ExpirationDate { get; set; }

        /// <summary>
        /// Informa se o Cartão de Crédito está ativo ou não para uso
        /// </summary>
        /// <value></value>
        public Boolean Active { get; set; }

        /// <summary>
        /// Data da desativação do Cartão de crédito.
        /// </summary>
        /// <value></value>
        public DateTime? SystemDeletedDate { get; set; }

        /// <summary>
        /// Detalhes do Device no momento do cadastro do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public DeviceRequestInfo DeviceRequestInfo { get; set; }

        /// <summary>
        /// Informações de faturamento do cartão de crédito. (Customer, Address)
        /// </summary>
        /// <value></value>
        public UserCreditCardBilling Billing { get; set; }


        public void Validate(bool isUpdate = false)
        {
            if (string.IsNullOrEmpty(this.CardId))
                throw MotivaiException.ofValidation("Deve ser informado o ID do Cartão de Crédito.");

            if (this.CreatedDate == null)
                throw MotivaiException.ofValidation("Deve ser informado a Data de Criação do Cartão de Crédito.");

            if (this.DeviceRequestInfo == null || this.DeviceRequestInfo.Device == null)
                throw MotivaiException.ofValidation("Deve ser informado os dados da requisição do Device.");

            if (this.Billing == null)
                throw MotivaiException.ofValidation("Deve ser informado as informações de Billing do Cartão de Crédito.");

            this.Billing.Validate();

            this.SystemCreatedDate = DateTime.UtcNow;

            if (isUpdate)
                this.SystemUpdateddDate = DateTime.UtcNow;

            if (this.Id == Guid.Empty)
            {
                this.Id = Guid.NewGuid();
                this.Active = true;
            }
        }
    }
}