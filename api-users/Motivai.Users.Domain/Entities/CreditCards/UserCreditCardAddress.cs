using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.CreditCards
{
    public class UserCreditCardAddress
    {
        /// <summary>
        /// País do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string Country { get; set; }

        /// <summary>
        /// Estado do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string State { get; set; }

        /// <summary>
        /// Cidade do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string City { get; set; }

        /// <summary>
        /// Bairro do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string Neighborhood { get; set; }

        /// <summary>
        /// Rua do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string Street { get; set; }

        /// <summary>
        /// Número do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string Number { get; set; }

        /// <summary>
        /// CEP do endereço cadastrado no Cartão de Crédito
        /// </summary>
        /// <value></value>
        public string Zipcode { get; set; }

        public void Validate()
        {
            if (string.IsNullOrEmpty(this.State))
                throw MotivaiException.ofValidation("Deve ser informado o Estado do Endereço do Cartão de Crédito");

            if (string.IsNullOrEmpty(this.City))
                throw MotivaiException.ofValidation("Deve ser informado a Cidade do Endereço do Cartão de Crédito");

            if (string.IsNullOrEmpty(this.Neighborhood))
                throw MotivaiException.ofValidation("Deve ser informado o Bairro do Endereço do Cartão de Crédito");

            if (string.IsNullOrEmpty(this.Street))
                throw MotivaiException.ofValidation("Deve ser informado a Rua do Endereço do Cartão de Crédito");

            if (string.IsNullOrEmpty(this.Number))
                throw MotivaiException.ofValidation("Deve ser informado o Número do Endereço do Cartão de Crédito");

            if (string.IsNullOrEmpty(this.Zipcode))
                throw MotivaiException.ofValidation("Deve ser informado o CEP do Endereço do Cartão de Crédito");
        }
    }
}