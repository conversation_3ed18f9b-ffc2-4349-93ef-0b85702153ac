using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.CreditCards
{
    public class UserCreditCardBilling
    {
        /// <summary>
        /// Endereço do portador Cartão de Crédito
        /// </summary>
        /// <value></value>
        public UserCreditCardAddress Address { get; set; }

        /// <summary>
        /// Informações dos portador do Cartão de Crédito
        /// </summary>
        /// <value></value>
        public UserCreditCardCustomer Customer { get; set; }

        public void Validate()
        {
            if (this.Address == null)
                throw MotivaiException.ofValidation("Deve ser informado o Endereço do Cartão de Crédito.");

            if (this.Customer == null)
                throw MotivaiException.ofValidation("Deve ser informado os dados do portador do Cartão de Crédito.");

            this.Address.Validate();
            this.Customer.Validate();
        }
    }
}