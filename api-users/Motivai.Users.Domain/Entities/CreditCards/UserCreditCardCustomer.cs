using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.CreditCards
{
    public class UserCreditCardCustomer
    {
        /// <summary>
        /// CPF ou CNPJ
        /// </summary>
        /// <value></value>
        public string Document { get; set; }

        /// <summary>
        /// Nome do Cliente
        /// </summary>
        /// <value></value>
        public string Name { get; set; }

        /// <summary>
        /// Telefone do Cliente
        /// </summary>
        /// <value></value>
        public string Telephone { get; set; }

        public void Validate()
        {
            if (string.IsNullOrEmpty(this.Document))
                throw MotivaiException.ofValidation("Deve ser informado o CPF ou CNPJ do portador do Cartão de Crédito.");

            if (string.IsNullOrEmpty(this.Name))
                throw MotivaiException.ofValidation("Deve ser informado o Nome do portador do Cartão de Crédito.");

            if (string.IsNullOrEmpty(this.Telephone))
                throw MotivaiException.ofValidation("Deve ser informado o Telefone do portador do Cartão de Crédito.");
        }
    }
}