using System;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.Configurations.Security {
    public class GeneratedSecurityCode {
        public Guid CampaignId { get; set; }
        ///<summary>
        /// Solicitante do código de segurança: DeviceID, outros
        ///</summary>
        public string Subject { get; set; }
        public string HashedSecurityCode { get; set; }
        public DateTime CreateDate { get; set; }

        public static GeneratedSecurityCode Of(Guid campaignId, string subject, string securityCode) {
            return new GeneratedSecurityCode() {
                CampaignId = campaignId,
                Subject = subject,
                HashedSecurityCode = Hashing.HashString(securityCode),
                CreateDate = DateTime.UtcNow
            };
        }

        public void ValidateSecurityCodeAndSubject(string securityCode, string subject) {
            if (DateTime.UtcNow.Subtract(CreateDate).Minutes > 15) {
                throw MotivaiException.ofValidation("Código de segurança expirado.");
            }
            if (!Hashing.ValidateHash(securityCode, HashedSecurityCode))
                throw MotivaiException.ofValidation("Código de segurança inválido.");
            if (Subject != subject)
                throw MotivaiException.ofValidation("Solicitante não autorizado.");
        }
    }
}