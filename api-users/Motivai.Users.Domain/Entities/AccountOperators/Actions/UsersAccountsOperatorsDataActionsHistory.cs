using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Models.AccountOperators;
using Motivai.SharedKernel.Domain.Entities;

namespace Motivai.Users.Domain.Entities.AccountOperators.Actions {
    public class UsersAccountsOperatorsDataActionsHistory : BaseEntity {
        [BsonRepresentation(BsonType.String)]
        public AccountOperatorChangedField ChangedField { get; set; }
        public String FromValue { get; set; }
        public String ToValue { get; set; }
        public string Timezone { get; set; }
        public ConnectionInfo ConnectionInfo { get; set; }
        public OperatorUser OperationUserDataAction { get; set; }

        public UsersAccountsOperatorsDataActionsHistory() {
        }

        public UsersAccountsOperatorsDataActionsHistory CopyFrom(UpdateDataAccountOperator updateDataAccountOperator) {
            return new UsersAccountsOperatorsDataActionsHistory {
                Id = Guid.NewGuid(),
                Active = true,
                CreateDate = DateTime.UtcNow,
                ChangedField = updateDataAccountOperator.ChangedField,
                FromValue = updateDataAccountOperator.FromValue,
                ToValue = updateDataAccountOperator.ToValue,
                Timezone = updateDataAccountOperator.Timezone,
                ConnectionInfo = updateDataAccountOperator.ConnectionInfo,
                OperationUserDataAction = updateDataAccountOperator.OperationUserDataAction
            };
        }

        public override void Validate() {

        }
    }

}