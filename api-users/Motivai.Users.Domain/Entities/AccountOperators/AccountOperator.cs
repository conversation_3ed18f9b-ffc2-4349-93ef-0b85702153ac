using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.AccountOperators
{
	public class AccountOperator : BaseEntity
	{
		public string Name { get; set; }

		[BsonRepresentation(BsonType.String)]
		public PersonType PersonType { get; set; }
		///<summary>
		/// CPF/CNPJ do operador
		///</summary>
		public string Document { get; set; }
		public OperatorUser OperationUserCreation { get; set; }
		public List<AccountOperatorLogin> Logins { get; set; }

		public bool HasLogin()
		{
			return Logins != null && Logins.Count > 0;
		}

		public bool HasLoginInCampaign(Guid campaignId)
		{
			return Logins.Any(x => x.CampaignId == campaignId);
		}

		public bool HasLoginWithEmail(string email)
		{
			return Logins.Any(l => l.IsEmailMatch(email));
		}

		public override void Validate()
		{
			Name.ForNullOrEmpty("Nome é obrigatório");
			Document.ForNullOrEmpty("CPF é obrigatório");
			PersonType = PersonTypeHelper.FromDocument(Document);
		}

		public void AddLogin(AccountOperatorLogin accountOperatorLogin)
		{
			if (Logins == null)
			{
				Logins = new List<AccountOperatorLogin>(1);
			}
			accountOperatorLogin.StartValidation();
			Logins.Add(accountOperatorLogin);
		}

		public AccountOperatorLogin GetLoginById(Guid accountOperatorLoginId)
		{
			var operatorLogin = Logins.FirstOrDefault(l => l.Id == accountOperatorLoginId);
			if (operatorLogin == null)
			{
				throw MotivaiException.ofValidation("Operador não encontrado.");
			}
			return operatorLogin;
		}

		public bool IsMigatedOperatorByCampaign(Guid campaignId)
		{
			var operatorLogin = Logins.FirstOrDefault(c => c.CampaignId == campaignId);
			if (operatorLogin != null &&
				operatorLogin.AuthenticationAccess != null)
			{
				return operatorLogin.AuthenticationAccess.Migrated;
			}
			return false;
		}

		public AccountOperatorLogin GetLoginByCampaignAndLogin(Guid campaignId, string login)
		{
			if (Logins == null) return null;
			return Logins.FirstOrDefault(l => l.CampaignId == campaignId && l.Login.ToUpper() == login.ToUpper()); // && l.AccessibleAccounts.Count > 0
		}

		public AccountOperatorLogin GetLoginByCampaignAndUser(Guid campaignId, Guid userId)
		{
			if (Logins == null) return null;
			return Logins.FirstOrDefault(l => l.CampaignId == campaignId && l.AccessibleAccounts.Any(aa => aa.UserId == userId));
		}

		public AccountOperatorLogin GetLoginByCampaignUserAndLogin(Guid campaignId, Guid userId, string login)
		{
			if (Logins == null) return null;
			return Logins.FirstOrDefault(l => l.CampaignId == campaignId && l.Login.ToUpper() == login.ToUpper() && l.AccessibleAccounts.Any(aa => aa.UserId == userId));
		}

		public AccountOperatorLogin GetLoginByCampaign(Guid campaignId)
		{
			return Logins?.FirstOrDefault(l => l.CampaignId == campaignId);
		}
	}
}
