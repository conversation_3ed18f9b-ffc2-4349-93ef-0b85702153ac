using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Users.Domain.Entities.AccountOperators {
    [JsonConverter(typeof(StringEnumConverter))]
    public enum OperatorRole {
        ///<summary>
        /// Pode gerenciar somente a conta
        ///</summary>
        MANAGER,
        ///<summary>
        /// Pode gerenciar a conta e os operadores
        ///</summary>
        ADMIN,
        ///<summary>
        /// Operador que vem no arquivo e pode fazer tudo
        ///</summary>
        MASTER
    }
}