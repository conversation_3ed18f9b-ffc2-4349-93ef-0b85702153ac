using System;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.AccountOperators {
    public class AccountOperatorAccessChange {
        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }
        public OperatorUser OperationUserBlocking { get; set; }
        public string Reason { get; set; }

        public void Validate() {
            if (AccountOperatorId == Guid.Empty || AccountOperatorLoginId == Guid.Empty)
                throw MotivaiException.ofValidation("Selecione o operador para bloqueio");

            if (OperationUserBlocking != null && OperationUserBlocking.OperationChannelOrigin == OperationOrigin.CAMPAIGN_SITE) {
                if (OperationUserBlocking.AccountOperatorId == AccountOperatorId && OperationUserBlocking.AccountOperatorLoginId == AccountOperatorLoginId) {
                    throw MotivaiException.ofValidation("Não é permitido efetuar bloqueio da própria conta.");
                }
            }

            if (String.IsNullOrEmpty(Reason))
                throw MotivaiException.ofValidation("Preencha o motivo.");

        }
    }
}
