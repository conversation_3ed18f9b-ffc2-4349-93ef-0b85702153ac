using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.Users.Domain.Entities.OperatorMigration;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.AccountOperators
{
    ///<summary>
    /// Operador de uma conta de participante.
    /// Deve ser único por (AccountOperatorId, UserId e CampaignId).
    ///
    /// Login ou Email é único por CampaignId.
    ///</summary>
    public class AccountOperatorLogin : BaseEntity
	{
		public Guid CampaignId { get; set; }
		public string Email { get; set; }
		public string MobilePhone { get; set; }
		public string Login { get; set; }
		public string Password { get; set; }

		[BsonRepresentation(BsonType.String)]
		public OperationOrigin CreationOrigin { get; set; }

		public bool AcceptReceiveCommunications { get; set; }

		public AuthenticationAccess AuthenticationAccess { get; set; }
		public List<AccessibleAccount> AccessibleAccounts { get; set; }
		public string CommunicationOptIn { get; set; }
		public OperatorUser UpdateOperationUser { get; set; }

		public static AccountOperatorLogin Create(Guid userId, Guid campaignId, string accountDocument, string email, string mobilePhone,
			bool acceptReceiveCommunications, string login, string password, OperationOrigin origin, OperatorRole role, OperatorUser operatorUser)
		{
			return new AccountOperatorLogin()
			{
				Id = Guid.NewGuid(),
				Active = true,
				CreateDate = DateTime.UtcNow,
				CampaignId = campaignId,
				Email = email,
				MobilePhone = mobilePhone,
				AcceptReceiveCommunications = acceptReceiveCommunications,
				Login = login,
				CreationOrigin = origin,
				Password = Hashing.HashString(password),
				AccessibleAccounts = new List<AccessibleAccount>(1) {
					AccessibleAccount.OfUser(operatorUser, userId, accountDocument, role)
				}
			};
		}

		public void ResetPassword(string generatedPassword)
		{
			this.Password = Hashing.HashString(generatedPassword);
		}

		public void ValidatePassword(string password)
		{
			if (!Hashing.ValidateHash(password, Password))
			{
				throw MotivaiException.ofValidation("Usuário e/ou senha inválido(s).");
			}
		}

		public bool IsEmailMatch(string email)
        {
			return Email == email;
        }

		public override void Validate()
		{
			Email.ForNullOrEmpty("E-mail é obrigatório");
			Login.ForNullOrEmpty("Login é obrigatório");

			Password.ForNullOrEmpty("Senha é obrigatória");

			Email = Email.Trim().ToLower();
		}

		public bool HasValidAccessibleAccountUserId(Guid userId)
		{
			if (AccessibleAccounts.IsNullOrEmpty())
				return false;
			return AccessibleAccounts.Exists(a => a.UserId == userId && a.Active && !a.Blocked);
		}

		public AccessibleAccount GetAccessibleAccountByUserId(Guid userId)
		{
			if (AccessibleAccounts.IsNullOrEmpty())
				return null;
			return AccessibleAccounts.FirstOrDefault(a => a.UserId == userId);
		}

		public void AddAccessibleAccount(OperatorUser operatorUser, Guid userId, string accountDocument, OperatorRole role)
		{
			if (AccessibleAccounts == null)
			{
				AccessibleAccounts = new List<AccessibleAccount>(1);
			}
			else if (AccessibleAccounts.Any(a => a.UserId == userId))
			{
				throw MotivaiException.ofValidation("Operador já tem acesso a essa conta.");
			}
			AccessibleAccounts.Add(AccessibleAccount.OfUser(operatorUser, userId, accountDocument, role));
		}

        public List<AccessibleAccount> GetValidAccessibleAccounts()
        {
			return AccessibleAccounts?.Where(a => a.Active && !a.Blocked).ToList();
        }

		public AccountOperatorLogin CloneToCampaign(Guid campaignId)
		{
			return new AccountOperatorLogin()
			{
				Id = Guid.NewGuid(),
				CreateDate = DateTime.UtcNow,
				Active = true,
				CampaignId = campaignId,
				Email = this.Email,
				MobilePhone = this.MobilePhone,
				Login = this.Login,
				Password = this.Password,
				AcceptReceiveCommunications = this.AcceptReceiveCommunications,
				AccessibleAccounts = this.AccessibleAccounts,
				UpdateOperationUser = this.UpdateOperationUser
			};
		}

		public void MigrateIfEligible(Guid campaignId, OperatorMigrationModel operatorMigration)
		{
			if (this.IsEligibleForMigration(campaignId, operatorMigration.Email))
			{
				this.MigrateOperatorLogin(operatorMigration);
			}
		}

		public bool IsEligibleForMigration(Guid campaignId, string email)
		{
			return this.MatchesCampaignAndEmail(campaignId, email) &&
				(this.AuthenticationAccess == null || !this.AuthenticationAccess.Migrated);
		}

        private bool MatchesCampaignAndEmail(Guid campaignId, string email)
		{
			return this.CampaignId == campaignId && this.Email == email;
		}

		private void MigrateOperatorLogin(OperatorMigrationModel operatorMigration)
		{
			this.AuthenticationAccess = new AuthenticationAccess()
			{
				Migrated = true,
				MigrationDate = DateTime.UtcNow,
				AuthenticationType = operatorMigration.AccessType,
			};
			this.CommunicationOptIn = operatorMigration.CommunicationOptIn;
			this.UpdateDate = DateTime.UtcNow;
		}

		public bool IsMigrated()
		{
			return this.AuthenticationAccess != null && this.AuthenticationAccess.Migrated;
		}
	}

	public class AccessibleAccount
	{
		public Guid UserId { get; set; }
		public string UserDocument { get; set; }
		public bool Active { get; set; }

		[BsonRepresentation(BsonType.String)]
		public OperatorRole Role { get; set; }

		public DateTime LinkDate { get; set; }
		public DateTime? UnlinkeDate { get; set; }

		public bool Blocked { get; set; }
		public string BlockingReason { get; set; }
		public string ActiveReason { get; set; }

		public OperatorUser OperationUserCreation { get; set; }
		public OperatorUser OperationUserBlocking { get; set; }
		public OperatorUser OperationUserActiving { get; set; }

		public static AccessibleAccount OfUser(OperatorUser operatorUser, Guid userId, string accountDocument, OperatorRole role)
		{
			return new AccessibleAccount()
			{
				UserId = userId,
				UserDocument = accountDocument,
				Role = role,
				Active = true,
				LinkDate = DateTime.UtcNow,
				OperationUserCreation = operatorUser
			};
		}

		public void Block(OperatorUser operatorUser, string reason)
		{
			this.Blocked = true;
			this.BlockingReason = reason;
			this.OperationUserBlocking = operatorUser;
		}

		public void ActiveOperator(OperatorUser operatorUser, string reason)
		{
			this.Blocked = false;
			this.ActiveReason = reason;
			this.OperationUserActiving = operatorUser;
		}
	}
	public class AuthenticationAccess
	{
		public string AuthenticationType { get; set; }
		public bool Migrated { get; set; }
		public DateTime MigrationDate { get; set; }

		public AuthenticationAccess()
		{
			this.Migrated = false;
		}
	}
}
