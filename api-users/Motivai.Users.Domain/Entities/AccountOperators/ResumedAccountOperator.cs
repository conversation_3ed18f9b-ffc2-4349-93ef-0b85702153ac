using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Validators;

namespace Motivai.Users.Domain.Entities.AccountOperators {
    public class ResumedAccountOperator {
        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }
        public bool Blocked { get; set; }

        public string Name { get; set; }
        public string Document { get; set; }
        public string AccountDocument { get; set; }
        public PersonType PersonType { get; set; }

        public string Email { get; set; }
        public string MobilePhone { get; set; }
        public bool AcceptReceiveCommunications { get; set; }
        public string Login { get; set; }
        public bool IsMigrated { get; set; }
        public DateTime MigrationDate { get; set; }

        public OperationOrigin CreationOrigin { get; set; }

        public OperatorRole? Role { get; set; }

        public OperatorUser OperationUserCreation { get; set; }

        ///<summary>
        /// Se no cadastro via API receber o valor então é usado.
        ///</summary>
        public bool? Active { get; set; }

        public List<AccessibleAccount> AccessibleAccounts { get; set; }

        public static ResumedAccountOperator FromOperator(AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin) {
            return new ResumedAccountOperator() {
                AccountOperatorId = accountOperator.Id,
                AccountOperatorLoginId = accountOperatorLogin.Id,
                Name = accountOperator.Name,
                Document = accountOperator.Document,
                Email = accountOperatorLogin.Email,
                MobilePhone = accountOperatorLogin.MobilePhone,
                Login = accountOperatorLogin.Login
            };
        }

        public OperatorRole GetRole() {
            return Role ?? OperatorRole.MANAGER;
        }

        public void Validate() {
            Name.ForNullOrEmpty("OPERATOR_NAME_REQUIRED", "Nome é obrigatório");
            Document.ForNullOrEmpty("OPERATOR_CPF_INVALID", "CPF é obrigatório");
            Email.ForNullOrEmpty("OPERATOR_EMAIL_REQUIRED", "E-mail é obrigatório");
            if (!EmailValidator.IsEmail(Email)) {
                throw MotivaiException.of("OPERATOR_EMAIL_INVALID", "E-mail inválido");
            }
            if (string.IsNullOrEmpty(Login)) {
                Login = Email;
            }
            PersonType = PersonTypeHelper.FromDocument(Document);

            if (OperationUserCreation != null) {
                OperationUserCreation.OperationDate = DateTime.UtcNow;
            }
        }
    }
}
