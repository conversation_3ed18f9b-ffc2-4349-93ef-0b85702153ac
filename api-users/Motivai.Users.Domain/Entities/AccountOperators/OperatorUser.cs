using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.AccountOperators {
    public class OperatorUser {
        public DateTime OperationDate { get; set; }

        [BsonRepresentation(BsonType.String)]
        public OperationOrigin? OperationChannelOrigin { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }

        ///<summary>
        /// Dados do operador da conta.
        ///</summary>
        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }
    }
}
