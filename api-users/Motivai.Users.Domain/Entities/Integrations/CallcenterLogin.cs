using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Entities.Integrations
{
	public class CallcenterLogin
	{
		///<summary>
		/// Origens:
		/// - Admin
		/// - Site Campanha
		/// - Catálogo
		///</summary>
		public string Origin { get; set; }
		public Guid CallcenterUserId { get; set; }
		public string CallcenterUsername { get; set; }

		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }

		public string Timezone { get; set; }
		public ConnectionInfo ConnectionInfo { get; set; }

		public LocationInfo GetLocationInfo()
		{
			return LocationInfo.Of(Timezone, ConnectionInfo);
		}

		public void Validate()
		{
			if (CallcenterUserId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário do call center inválido.");
			if (UserId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			// if (ParticipantId == Guid.Empty)
			//     throw MotivaiException.ofValidation("Participante inválido.");
		}
	}
}