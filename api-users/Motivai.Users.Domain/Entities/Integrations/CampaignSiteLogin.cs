using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Entities.Integrations {
    public class CampaignSiteLogin {
        public string Origin { get; set; }
        public Guid? SessionId { get; set; }

        public Guid UserId { get; set; }
        public string Timezone { get; set; }
        public Guid? AccountOperatorId { get; set; }
        public Guid? AccountOperatorLoginId { get; set; }

        public ConnectionInfo ConnectionInfo { get; set; }

		public LocationInfo GetLocationInfo()
		{
			return LocationInfo.Of(Timezone, ConnectionInfo);
		}

        public void Validate() {
            if (UserId == Guid.Empty) {
                throw MotivaiException.ofValidation("Deve ser informado o usuário");
            }
            ValidateAccountOperator();
        }

        public bool IsAccountOperator() {
            return AccountOperatorId.HasValue && AccountOperatorLoginId.HasValue;
        }

        private void ValidateAccountOperator(bool requireOperator = false) {
            if (AccountOperatorId.HasValue || AccountOperatorLoginId.HasValue) {
                if (!AccountOperatorId.HasValue || AccountOperatorId.Value == Guid.Empty) {
                    throw MotivaiException.ofValidation("Sessão do operador de conta inválida.");
                }
                if (!AccountOperatorLoginId.HasValue || AccountOperatorLoginId.Value == Guid.Empty) {
                    throw MotivaiException.ofValidation("Sessão do operador de conta inválida.");
                }
            } else if (requireOperator) {
                throw MotivaiException.ofValidation("Sessão do operador de conta inválida.");
            }
        }

        public OperatorAuthenticationModel CreateOperatorAuthentication() {
            ValidateAccountOperator(true);
            return new OperatorAuthenticationModel() {
                Origin = this.Origin,
                UserId = this.UserId,
                AccountOperatorId = this.AccountOperatorId.Value,
                AccountOperatorLoginId = this.AccountOperatorLoginId.Value,
                Timezone = this.Timezone,
                ConnectionInfo = this.ConnectionInfo
            };
        }
    }
}