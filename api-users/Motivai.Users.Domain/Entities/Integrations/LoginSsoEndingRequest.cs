using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;

namespace Motivai.Users.Domain.Entities.Integrations
{
	public class LoginSsoEndingRequest
	{
		public string Origin { get; set; }
		public Guid Token { get; set; }
		public string Timezone { get; set; }
		public ConnectionInfo ConnectionInfo { get; set; }
		public LocationInfo GetLocationInfo()
		{
			return LocationInfo.Of(Timezone, ConnectionInfo);
		}
	}
}