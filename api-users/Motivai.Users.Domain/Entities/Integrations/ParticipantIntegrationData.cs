using System;
using System.Collections.Generic;
using System.Text;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.SharedKernel.Helpers.Validators;
using Motivai.Users.Domain.Entities.AccountOperators;
using Newtonsoft.Json;

namespace Motivai.Users.Domain.Entities.Integrations
{
	public class ParticipantIntegrationData
	{
		///<summary>
		/// Token de autenticação.
		/// Formato: <Token Cliente>.<Token Campanha>
		///</summary>
		public string Token { get; set; }
		///<summary>
		/// Documento do participante (CPF ou CNPJ).
		///</summary>
		public string Document { get; set; }

		public bool SkipDocumentValidation { get; set; }
		public bool SkipPasswordGeneration { get; set; }
		public bool UpdateMetadataAndGroups { get; set; }

		public string Login { get; set; }
		public Dictionary<string, string> Metadata { get; set; }
		public string Origin { get; set; }

		public string Name { get; set; }
		public string Rg { get; set; }

		public string CompanyName { get; set; }

		public bool? StateInscriptionExempt { get; set; }
		public string StateInscriptionUf { get; set; }
		public string StateInscription { get; set; }

		public string Telephone { get; set; }
		public string Cellphone { get; set; }
		public string Email { get; set; }

		public Address Address { get; set; }

		public string ClientUserId { get; set; }
		public IntegrationCompany IntegrationCompany { get; set; }

		public AccountOperatorInfo Operator { get; set; }

		public List<string> GroupsCodes { get; set; }

		public string Timezone { get; set; }
		public ConnectionInfo ConnectionInfo { get; set; }

		public DateTime? BirthDate { get; set; }
		public string ComercialEmail { get; set; }
		public string ComercialPhone { get; set; }
		public CampaignEventType? EventType { get; set; }
		public string Gender { get; set; }
		public bool IntegratedLogin { get; set; }
		public LocationInfo LocationInfo { get; set; }
		public string MobileOperator { get; set; }
		public Guid ParentUserId { get; set; }
		[JsonIgnore]
		public PersonType? PersonType { get; set; }

		///<summary>
		/// Recebe via payload apenas internamente quando utilizado em scripts.
		///</summary>
		public string Password { get; set; }
		public decimal? Points { get; set; }
		public bool SkipPostProcessing { get; set; }
		public bool ValidateLoginAndDocument { get; set; }

		public LocationInfo GetLocationInfo()
		{
			return LocationInfo.Of(Timezone, ConnectionInfo);
		}

		public bool HasAnyContactInfo()
		{
			return !string.IsNullOrEmpty(Email) || !string.IsNullOrEmpty(Telephone) || !string.IsNullOrEmpty(Cellphone);
		}

		public string GetEmail()
		{
			return string.IsNullOrEmpty(Email) ? ComercialEmail : Email;
		}

		public bool HasDocument()
		{
			return !string.IsNullOrEmpty(Document);
		}

		public bool HasLogin()
		{
			return !string.IsNullOrEmpty(Login);
		}

		public bool ShouldValidateDocument()
		{
			return ValidateLoginAndDocument && HasDocument();
		}

		public bool ShouldValidateLogin()
		{
			return ValidateLoginAndDocument && (HasLogin() || HasDocument());
		}

		public void EnsureSameDocument(string userDocument)
		{
			if (Document != userDocument)
			{
				throw MotivaiException.ofValidation(
					"USER_DIFFERENT_DOCUMENT",
					"O CPF/CNPJ informado está divergente do que está cadastrado na campanha, por favor, entre em contato com o atendimento."
				);
			}
		}

		public void EnsureSameLogin(string participantLogin)
		{
			var differentLogin = false;

			if (HasLogin())
			{
				differentLogin = Login != participantLogin;
			}
			else if (HasDocument())
			{
				differentLogin = Document != participantLogin;
			}

			if (differentLogin)
			{
				throw MotivaiException.ofValidation(
					"USER_DIFFERENT_LOGIN",
					"O login informado está divergente do que está cadastrado na campanha, por favor, entre em contato com o atendimento."
				);
			}
		}

		public bool HasAnyGroup()
		{
			return !GroupsCodes.IsNullOrEmpty();
		}

		public void Validate()
		{
			RemoveMasks();
			if (!string.IsNullOrEmpty(Email))
			{
				Email = Email.ToLower();
			}
			if (!string.IsNullOrEmpty(ComercialEmail))
			{
				ComercialEmail = ComercialEmail.ToLower();
			}

			if (SkipDocumentValidation && string.IsNullOrEmpty(Login))
			{
				throw MotivaiException.of("USER_LOGIN_REQUIRED", "Login é obrigatório.");
			}

			if (!string.IsNullOrEmpty(Document))
			{
				this.PersonType = PersonTypeHelper.FromDocument(Document);
			}
			else if (!SkipDocumentValidation)
			{
				throw MotivaiException.of("USER_DOCUMENT_REQUIRED", "CPF/CNPJ é obrigatório.");
			}

			if (Address != null)
			{
				Address.Validate();
			}
			if (Operator != null)
			{
				Operator.Validate();
			}
		}

		public bool HasCompanyIntegrationData()
		{
			return IntegrationCompany != null && (!string.IsNullOrEmpty(IntegrationCompany.CompanyName) || !string.IsNullOrEmpty(IntegrationCompany.CompanyIdentifier));
		}

		public void RemoveMasks()
		{
			Document = Extractor.RemoveMasks(Document);
			if (Address != null)
			{
				Address.Cep = Extractor.RemoveMasks(Address.Cep);
			}
			Telephone = Extractor.RemoveMasks(Telephone);
			Cellphone = Extractor.RemoveMasks(Cellphone);
		}
	}

	public class AccountOperatorInfo
	{
		public string Name { get; set; }
		public string Document { get; set; }
		public string AccountDocument { get; set; }
		public string Login { get; set; }
		public string Email { get; set; }
		public string MobilePhone { get; set; }
		public string CreationOrigin { get; set; }

		public void RemoveMasks()
		{
			Document = Extractor.RemoveMasks(Document);
			MobilePhone = Extractor.RemoveMasks(MobilePhone);
		}

		public OperationOrigin GetMappedCreationOrigin()
		{
			switch (CreationOrigin)
			{
				case "ADMIN":
					return OperationOrigin.ADMIN;
				case "CAMPAIGN_SITE":
					return OperationOrigin.CAMPAIGN_SITE;
				case "PARTICIPANTS_IMPORT_VR":
					return OperationOrigin.PARTICIPANTS_IMPORT_VR;
				case "REPLICATION":
					return OperationOrigin.REPLICATION;
				default:
					return OperationOrigin.UNKNOWN;
			}
		}

		public void Validate()
		{
			RemoveMasks();

			Document.ForNullOrEmpty("OPERATOR_CPF_REQUIRED", "CPF do operador é obrigatório");
			if (!Cpf.IsCpf(Document))
			{
				throw MotivaiException.of("OPERATOR_CPF_INVALID", "CPF do operador inválido");
			}
			Email.ForNullOrEmpty("OPERATOR_EMAIL_REQUIRED", "E-mail do operador é obrigatório");
			if (!EmailValidator.IsEmail(Email))
			{
				throw MotivaiException.of("OPERATOR_EMAIL_INVALID", "E-mail do operador inválido");
			}
			Email = Email.ToLower().Trim();
		}
	}
}