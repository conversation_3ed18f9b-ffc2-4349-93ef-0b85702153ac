using System;

namespace Motivai.Users.Domain.Entities.Integrations.CampaignsGroup
{
	public class CampaignGroupSsoResult
	{
		public string RedirectUrl { get; set; }
		public Guid Token { get; set; }

		public static CampaignGroupSsoResult OfUrlAndToken(string campaignUrl, Guid token)
		{
			return new CampaignGroupSsoResult
			{
				RedirectUrl = $"{campaignUrl}/integration/sso?token={token}",
				Token = token
			};
		}
	}
}