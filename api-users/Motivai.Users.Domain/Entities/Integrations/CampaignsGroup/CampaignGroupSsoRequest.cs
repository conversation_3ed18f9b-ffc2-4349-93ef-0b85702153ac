using System;
using System.Text;
using Motivai.SharedKernel.Domain.Entities.References.Security;

namespace Motivai.Users.Domain.Entities.Integrations.CampaignsGroup
{
	public class CampaignGroupSsoRequest
	{
		public string Origin { get; set; }

		public Guid UserId { get; set; }
		public Guid? AccountOperatorId { get; set; }
		public Guid? AccountOperatorLoginId { get; set; }
		public Guid TargetCampaignId { get; set; }

		public string Timezone { get; set; }
		public ConnectionInfo ConnectionInfo { get; set; }

		public bool HasAccountOperator()
		{
			return AccountOperatorId.HasValue && AccountOperatorLoginId.HasValue
				&& AccountOperatorId != Guid.Empty && AccountOperatorLoginId != Guid.Empty;
		}
	}
}