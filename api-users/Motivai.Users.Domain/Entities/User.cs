﻿using System;
using System.Collections.Generic;
using System.Linq;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Motivai.Users.Domain.Entities.Configurations;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Domain.Entities.References.Events;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.Users.Domain.Entities
{
    public class User : BaseEntity
    {
        ///<summary>
        /// Identificar externo.
        ///</summary>
        // public ObjectId Identifier { get; set; }

        ///<summary>
        /// Data da última operação de atualização no usuário/participante.
        ///</summary>
        public DateTime? LastOperationDate { get; set; }

        // Nome ou Nome Fantasia
        public string Name { get; set; }
        public string Document { get; set; }
        public Dictionary<string, string> Metadata { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        [BsonRepresentation(BsonType.String)]
        public PersonType Type { get; set; }

        ///<summary>
        /// Tipo para informar se foi cadastrado via B2B.
        /// Para manter o filtro na tela de pedidos do Representante
        /// apenas com participantes do tipo da campanha.
        ///</summary>
        [BsonRepresentation(BsonType.String)]
        public UserBusinessType BusinessType { get; set; }

        // PJ
        public string Cnpj { get; set; }
        public bool StateInscriptionExempt { get; set; }
        public string StateInscription { get; set; }
        public string StateInscriptionUf { get; set; }
        /// Razao Social
        public string CompanyName { get; set; }

        // PF
        public string Cpf { get; set; }
        public string Rg { get; set; }
        public DateTime BirthDate { get; set; }
        public string MaritalStatus { get; set; }
        public string Gender { get; set; }

        public string PhotoUrl { get; set; }

        /// <summary>
        /// Aceita receber informações da Motivai
        /// </summary>
        public bool GpInf { get; set; }
        /// <summary>
        /// Aceita receber informações de Parceiros da Motivai
        /// </summary>
        public bool GpPartnerInf { get; set; }

        public bool Blocked { get; set; }
        public List<UserParticipantCampaign> UsersParticipantCampaign { get; set; }

        ///<summary>
        /// Configurações definidas pelo usuário.
        /// Inicialmente contém config do Mottiva.
        ///</summary>
        public UserConfigurations Configurations { get; set; }

        ///<summary>
        /// Configurações e detalhes da carteira.
        ///</summary>
        public Wallet Wallet { get; set; }


        ///<summary>
        /// Informações contendo a origem e o rastreio da criação do usuário
        ///</summary>
        public EventInfo CreateEventInfo { get; set; }

        ///<summary>
        /// Informações contendo a origem e o rastreio da atualização do usuário
        ///</summary>
        public EventInfo UpdateEventInfo { get; set; }
        public RequestInformation RequestInformation { get; set; }

        public User() {}

        public static User CreateUser(CampaignType campaignType, string document)
        {
            var personType = PersonTypeHelper.FromDocument(document);
            return CreateUser(campaignType, personType, document);
        }

        public static User CreateUser(CampaignType campaignType)
        {
            return new User()
            {
                Id = Guid.NewGuid(),
                // Identifier = ObjectId.GenerateNewId(),
                Active = true,
                CreateDate = DateTime.UtcNow,
                BusinessType = campaignType.IsB2B() ? UserBusinessType.B2B : UserBusinessType.Rewards,
                UsersParticipantCampaign = new List<UserParticipantCampaign>(1)
            };
        }

        public static User CreateUser(CampaignType campaignType, PersonType personType, string document)
        {
            return new User()
            {
                Id = Guid.NewGuid(),
                // Identifier = ObjectId.GenerateNewId(),
                Type = personType,
                Active = true,
                CreateDate = DateTime.UtcNow,
                Cpf = personType == PersonType.Fisica ? Extractor.RemoveMasks(document) : null,
                Cnpj = personType == PersonType.Juridica ? Extractor.RemoveMasks(document) : null,
                BusinessType = campaignType.IsB2B() ? UserBusinessType.B2B : UserBusinessType.Rewards,
                UsersParticipantCampaign = new List<UserParticipantCampaign>(1)
            };
        }

        public override void Validate()
        {
            this.Validate(null);
        }

        public void Validate(CampaignSettingsModel campaignSettings = null)
        {
            if (Type == PersonType.Fisica)
            {
                if (string.IsNullOrEmpty(Cpf))
                    throw MotivaiException.of("CPF_CNPJ_REQUIRED", "Deve ser informado o CPF do usuário.");
                if (!SharedKernel.Domain.ValuesObject.Cpf.IsCpf(Cpf))
                    throw MotivaiException.of("CPF_CNPJ_INVALID", "CPF inválido.");
                if (string.IsNullOrEmpty(Document))
                    Document = Cpf;
            }
            else if (Type == PersonType.Juridica)
            {
                if (string.IsNullOrEmpty(Cnpj))
                    throw MotivaiException.of("CPF_CNPJ_REQUIRED", "Deve ser informado o CNPJ do usuário.");
                if (!SharedKernel.Domain.ValuesObject.Cnpj.IsCnpj(Cnpj))
                    throw MotivaiException.of("CPF_CNPJ_INVALID", "CNPJ inválido.");
                if (string.IsNullOrEmpty(Document))
                    Document = Cnpj;
            }
            else
            {
                LoggerFactory.GetLogger().Warn("Usr {} - Sem tipo de pessoa", Id);
            }

            if (UsersParticipantCampaign != null)
                UsersParticipantCampaign.ForEach(p => p.ValidateByCampaignSettings(campaignSettings));
        }

        public string GetName()
        {
            if (Type == PersonType.Fisica)
                return Name;
            return string.IsNullOrEmpty(CompanyName) ? Name : CompanyName;
        }

        public string GetDocument()
        {
            return Type == PersonType.Fisica ? Cpf : Cnpj;
        }

        public bool IsFisicPerson()
        {
            return Type == PersonType.Fisica;
        }

        public bool IsJuridicPerson()
        {
            return Type == PersonType.Juridica;
        }

        public UserParticipantCampaign GetParticipantById(Guid participantId)
        {
            if (UsersParticipantCampaign == null)
                UsersParticipantCampaign = new List<UserParticipantCampaign>();
            return UsersParticipantCampaign.FirstOrDefault(u => u.Id == participantId);
        }

        public UserParticipantCampaign GetParticipantByCampaign(Guid campaignId)
        {
            if (UsersParticipantCampaign == null)
                UsersParticipantCampaign = new List<UserParticipantCampaign>(1);
            return UsersParticipantCampaign.FirstOrDefault(u => u.CampaignId == campaignId);
        }

        public List<Guid> GetActiveCampaigns()
        {
            if (UsersParticipantCampaign == null) return null;
            return UsersParticipantCampaign.Where(p => p.Active).Select(p => p.CampaignId).ToList();
        }

        public bool GetUserParticipantActive(Guid campaignId)
        {
            if (this.UsersParticipantCampaign == null) return false;
            return this.UsersParticipantCampaign.FirstOrDefault(a => a.CampaignId == campaignId).Active;
        }

        public void AddParticipant(UserParticipantCampaign participant)
        {
            if (participant == null)
            {
                throw MotivaiException.ofValidation("Participante inválido.");
            }
            if (UsersParticipantCampaign == null)
            {
                UsersParticipantCampaign = new List<UserParticipantCampaign>(1);
            }
            else if (UsersParticipantCampaign.Any(p => p.CampaignId == participant.CampaignId))
            {
                throw MotivaiException.ofValidation("Já existe um participante nesta campanha.");
            }
            UsersParticipantCampaign.Add(participant);
        }

        public void ValidateForSession()
        {
            if (!Active)
                throw MotivaiException.of("PARTICIPANT_INACTIVE", "Participante desativado.");
            if (Blocked)
                throw MotivaiException.of("PARTICIPANT_BLOCKED", "Participante bloqueado.");
        }
    }
}