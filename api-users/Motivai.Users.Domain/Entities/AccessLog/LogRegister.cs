using System;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Entities.Wallets.Devices;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.AccessLog
{
	public class LogRegister : BaseEntity
	{
		[BsonRepresentation(BsonType.String)]
		public LogRegisterAction Action { get; set; }
		public Guid? SessionId { get; set; }
		public Guid UserId { get; set; }
		public Guid CampaignId { get; set; }
		public string Document { get; set; }

		public DateTime AccessDate { get; set; }
		public string Timezone { get; set; }

		public Guid? CallcenterId { get; set; }
		public string CallcenterName { get; set; }
		public string Message {get;set;}

		public Guid? AccountOperatorId { get; set; }
		public string AccountOperatorDocument { get; set; }
		public Guid? AccountOperatorLoginId { get; set; }

		public ConnectionInfo ConnectionInfo { get; set; }

		public DeviceRequestInfo DeviceRequestInfo { get; set; }

		public LogRegister(){}

		public override void Validate()
		{
		}

		public static LogRegister CallcenterLogged(CallcenterLogin callcenterLogin,
			UserParticipantModel participantSession, LocationInfo locationInfo)
		{
			return new LogRegister()
			{
				SessionId = participantSession.SessionId,
				Id = Guid.NewGuid(),
				Active = true,
				CreateDate = DateTime.UtcNow,
				Action = LogRegisterAction.LoginWithCallcenter,
				CampaignId = participantSession.CampaignId,
				CallcenterId = callcenterLogin.CallcenterUserId,
				CallcenterName = callcenterLogin.CallcenterUsername,
				UserId = participantSession.UserId,
				Document = participantSession.Document,
				AccessDate = DateTime.UtcNow,
				Timezone = locationInfo?.Timezone,
				ConnectionInfo = locationInfo?.ConnectionInfo
			};
		}

		public static LogRegister JustLogged(LogRegisterAction action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo)
		{
			return new LogRegister()
			{
				Id = Guid.NewGuid(),
				Active = true,
				CreateDate = DateTime.UtcNow,
				Action = action,
				UserId = userId,
				CampaignId = campaignId,
				Document = document,
				AccessDate = DateTime.UtcNow,
				Timezone = locationInfo?.Timezone,
				ConnectionInfo = locationInfo?.ConnectionInfo
			};
		}

		public static LogRegister AccountOperatorLogged(LogRegisterAction action, Guid userId, Guid campaignId, string userDocument,
				Guid accountOperatorId, string operatorDocument, Guid accountOperatorLoginId, LocationInfo locationInfo)
		{
			return new LogRegister()
			{
				Id = Guid.NewGuid(),
				Active = userId != Guid.Empty, // no caso de login de operador não temos um user selecionado ainda
				CreateDate = DateTime.UtcNow,
				Action = action,
				UserId = userId,
				CampaignId = campaignId,
				Document = userDocument,
				AccountOperatorId = accountOperatorId,
				AccountOperatorDocument = operatorDocument,
				AccountOperatorLoginId = accountOperatorLoginId,
				AccessDate = DateTime.UtcNow,
				Timezone = locationInfo?.Timezone,
				ConnectionInfo = locationInfo?.ConnectionInfo
			};
		}

		public static LogRegister OfSuccessfulAuthentication(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo)
		{
			return new LogRegister()
			{
				Id = Guid.NewGuid(),
				Active = true,
				CreateDate = DateTime.UtcNow,
				SessionId = participantSession.SessionId,
				Action = action,
				UserId = participantSession.UserId,
				CampaignId = participantSession.CampaignId,
				Document = participantSession.Document,
				AccountOperatorId = participantSession.AccountOperatorId,
				AccountOperatorLoginId = participantSession.AccountOperatorLoginId,
				AccountOperatorDocument = participantSession.AccountOperatorDocument,
				AccessDate = DateTime.UtcNow,
				Timezone = participantSession.Timezone,
				ConnectionInfo = connectionInfo
			};
		}
    }
}