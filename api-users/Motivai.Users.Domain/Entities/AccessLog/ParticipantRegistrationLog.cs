using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Collections.Generic;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.Models.PrivacyPolicy;
using Motivai.SharedKernel.Helpers.Extensions;
using System.Linq;
using Motivai.Users.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;

namespace Motivai.Users.Domain.Entities.AccessLog
{
	public class ParticipantRegistrationLog
	{
		public Guid Id { get; set; }
		public DateTime CreateDate { get; set; }

		[BsonRepresentation(BsonType.String)]
		public RegistrationAction Action { get; set; }

		///<summary>
		/// ID do sujeito/objeto alterado.
		/// Alteração de endereço: Address.Id
		/// Alteração de dados cadastrais: UserParticipantCampaign.Id
		///</summary>
		public Guid SubjectId { get; set; }

		public Guid UserId { get; set; }
		public Guid CampaignId { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public OperationUser OperationUser { get; set; }

		public LocationInfo LocationInfo { get; set; }

		public Dictionary<string, string> AdditionalInformation { get; set; }

		public ParticipantRegistrationLog() { }

		public ParticipantRegistrationLog(RegistrationAction action, Guid userId, Guid campaignId,
			OperationUser operationUser, LocationInfo locationInfo)
		{
			Id = Guid.NewGuid();
			CreateDate = DateTime.UtcNow;
			Action = action;
			UserId = userId;
			CampaignId = campaignId;
			OperationUser = operationUser;
			LocationInfo = locationInfo;
		}

		public ParticipantRegistrationLog(RegistrationAction action, Guid userId, Guid campaignId,
			AccountOperator accountOperator, LocationInfo locationInfo)
		{
			Id = Guid.NewGuid();
			CreateDate = DateTime.UtcNow;
			Action = action;
			UserId = userId;
			CampaignId = campaignId;
			AccountOperator = accountOperator;
			LocationInfo = locationInfo;
		}

		public ParticipantRegistrationLog(RegistrationAction action,
			Motivai.Users.Domain.Entities.AccountOperators.AccountOperator accountOperator,
			string email, string document)
		{
			Id = Guid.NewGuid();
			CreateDate = DateTime.UtcNow;
			Action = action;
			AccountOperator = new AccountOperator() {
				AccountOperatorId = accountOperator.Id,
				AccountOperatorLoginId = Guid.Empty,
				AccountOperatorDocument = document,
				AccountOperatorEmail = email
			};
		}

		public ParticipantRegistrationLog(RegistrationAction action, Guid userId, Guid campaignId,
			OperationUser operationUser, LocationInfo locationInfo, Dictionary<string, string> additionalInformation)
		{
			Id = Guid.NewGuid();
			CreateDate = DateTime.UtcNow;
			Action = action;
			UserId = userId;
			CampaignId = campaignId;
			OperationUser = operationUser;
			LocationInfo = locationInfo;
			AdditionalInformation = additionalInformation;
		}

		public static ParticipantRegistrationLog OfAction(RegistrationAction action, Guid userId, Guid campaignId,
			OperationUser operationUser, LocationInfo locationInfo)
		{
			return new ParticipantRegistrationLog(action, userId, campaignId, operationUser, locationInfo);
		}

		public static ParticipantRegistrationLog OfAction(RegistrationAction action, Guid userId, Guid campaignId,
			OperationUser operationUser, LocationInfo locationInfo, Dictionary<string, string> AdditionalInformation)
		{
			return new ParticipantRegistrationLog(action, userId, campaignId, operationUser, locationInfo, AdditionalInformation);
		}

		public static ParticipantRegistrationLog ofAction(RegistrationAction action,
			Motivai.Users.Domain.Entities.AccountOperators.AccountOperator accountOperator,
			string email, string document)
		{
			return new ParticipantRegistrationLog(action, accountOperator, email, document);
		}

		public static ParticipantRegistrationLog OfAction(RegistrationAction action, string origin, Guid userId, Guid campaignId,
			string newEmail, string oldEmail, string newMobilePhone, string oldMobilePhone)
		{
			return new ParticipantRegistrationLog(action, userId, campaignId, null, null,
				new Dictionary<string, string>() {
					{"origin", origin},
					{"E-mail Novo", newEmail},
					{"E-mail Antigo", oldEmail},
					// Anterior estava com letra mínuscula
					{"Celular Novo", newMobilePhone},
					{"Celular Antigo", oldMobilePhone}
				});
		}

		public static ParticipantRegistrationLog OfAction(RegistrationAction action, string origin, Guid userId, Guid campaignId,
			string newEmail, string oldEmail, string newMobilePhone, string oldMobilePhone, string newName, string oldName)
		{
			return new ParticipantRegistrationLog(action, userId, campaignId, null, null,
				new Dictionary<string, string>() {
					{"origin", origin},
					{"Nome Novo", newName},
					{"Nome Antigo", oldName},
					{"E-mail Novo", newEmail},
					{"E-mail Antigo", oldEmail},
					{"Celular Novo", newMobilePhone},
					{"Celular Antigo", oldMobilePhone},
				});
		}

		public static ParticipantRegistrationLog OfContactUpdate(RegistrationAction action, string origin, Guid userId, Guid campaignId,
			Contact currentContact, Contact newContact, LocationInfo locationInfo = null)
		{
			return new ParticipantRegistrationLog(action, userId, campaignId, null, locationInfo,
				new Dictionary<string, string>() {
					{"origin", origin},
					{"E-mail Princial Novo", newContact?.MainEmail ?? ""},
					{"E-mail Princial Antigo", currentContact?.MainEmail ?? ""},
					{"E-mail Pessoal Novo", newContact?.PersonalEmail ?? ""},
					{"E-mail Pessoal Antigo", currentContact?.PersonalEmail ?? ""},
					{"E-mail Comercial Novo", newContact?.CommercialEmail ?? ""},
					{"E-mail Comercial Antigo", currentContact?.CommercialEmail ?? ""},
					{"Celular Novo", newContact?.MobilePhone ?? ""},
					{"Celular Antigo", currentContact?.MobilePhone ?? ""},
					{"Telefone Principal Novo", newContact?.MainPhone ?? ""},
					{"Telefone Principal Antigo", currentContact?.MainPhone ?? ""},
					{"Telefone Comercial Novo", newContact?.CommercialPhone ?? ""},
					{"Telefone Comercial Antigo", currentContact?.CommercialPhone ?? ""},
					{"Telefone Residencial Novo", newContact?.HomePhone ?? ""},
					{"Telefone Residencial Antigo", currentContact?.HomePhone ?? ""},
					{"Última Data de Atualização", DateTime.Now.ToString()}
				});
		}

		public static ParticipantRegistrationLog OfAddressUpdate(RegistrationAction registrationAction, Guid userId, Guid campaignId, AccountOperator accountOperator, Guid addressId,
			LocationInfo locationInfo)
		{
			return new ParticipantRegistrationLog(registrationAction, userId, campaignId, accountOperator, locationInfo)
			{
				SubjectId = addressId
			};
		}

		public static ParticipantRegistrationLog OfParticipantDataUpdate(Guid userId, Guid campaignId, AccountOperator accountOperator,
			 LocationInfo locationInfo)
		{
			return new ParticipantRegistrationLog(RegistrationAction.UPDATE_REGISTRATION_DATA, userId, campaignId, accountOperator, locationInfo);
		}

		public static ParticipantRegistrationLog OfAdminParticipantDataUpdate(Guid userId, Guid campaignId,
			ParticipantManagementModel participantManagementModel)
		{
			return new ParticipantRegistrationLog(RegistrationAction.UPDATE_REGISTRATION_DATA_FROM_ADMIN, userId, campaignId,
				participantManagementModel.OperationUser, participantManagementModel.LocationInfo,
				new Dictionary<string, string>() {
					 { "E-mail Principal", participantManagementModel.Contact?.MainEmail },
					 { "Celular", participantManagementModel.Contact?.MobilePhone },
					 { "Formato MFA", participantManagementModel.AuthenticationMfaSettings?.AuthenticationMfaFormat?.ToString() },
					 { "E-mail MFA", participantManagementModel.AuthenticationMfaSettings?.Email },
					 { "Celular MFA", participantManagementModel.AuthenticationMfaSettings?.MobilePhone },
				});
		}

		public static ParticipantRegistrationLog OfPrivacySettings(Guid userId, Guid campaignId,
			PrivacySettings privacySettings, AccountOperator accountOperator = null, LocationInfo locationInfo = null)
		{
			return new ParticipantRegistrationLog(RegistrationAction.PRIVACY_POLICY_ACCEPTANCE_CHANGED,
				userId, campaignId, accountOperator, locationInfo)
			{
				AdditionalInformation = new Dictionary<string, string>()
				{
					{ "ID", privacySettings.ContentId },
					{ "Versão Política Privacidade", privacySettings.Version },
					{ "Política de Privacidade Aceita", privacySettings.PrivacyPolicyAccepted ? "Sim" : "Não" },
					{ "Cookies Essenciais", privacySettings.EssentialsCookies ? "Sim" : "Não" },
					{ "Cookies Analytics", privacySettings.AnalyticsCookies ? "Sim" : "Não" },
					{ "Comunicação da Campanha Aceita", privacySettings.CampaignCommunicationsAccepted ? "Sim" : "Não" },
					{ "Comunicação do Parceiro Aceita", privacySettings.PartnerCommunicationsAccepted ? "Sim" : "Não" }
				}
			};
		}

		public static ParticipantRegistrationLog OfPrivacySettingsChange(Guid userId, Guid campaignId,
			PrivacyPolicyResult privacyPolicyResult, AccountOperator accountOperator = null, LocationInfo locationInfo = null)
		{
			return new ParticipantRegistrationLog(RegistrationAction.PRIVACY_POLICY_ACCEPTANCE_CHANGED,
				userId, campaignId, accountOperator, locationInfo)
			{
				AdditionalInformation = new Dictionary<string, string>()
				{
					{ "ID", privacyPolicyResult.ContentId },
					{ "Versão Política Privacidade", privacyPolicyResult.Version },
					{ "Política de Privacidade Aceita", privacyPolicyResult.Accepted ? "Sim" : "Não" },
					{ "Cookies Essenciais", privacyPolicyResult.EssentialsCookies ? "Sim" : "Não" },
					{ "Cookies Analytics", privacyPolicyResult.AnalyticsCookies ? "Sim" : "Não" },
				}
			};
		}

		public static ParticipantRegistrationLog OfCardUpdate(Guid userId, Guid campaignId,
			Card card, List<Card> userCardsDb, LocationInfo locationInfo = null, AccountOperator accountOperator = null)
		{
			var cardCode = "";
			var cardCodes = new List<string>();

			if ( !string.IsNullOrEmpty(card.CardCode) ) {
				cardCode = card.CardCode;
				cardCodes.Add(cardCode);

			} else if (!card.CardCodes.IsNullOrEmpty()) {
				cardCode = string.Join(" - ", card.CardCodes);
				cardCodes.AddRange(card.CardCodes);
			}

			var cardId = string.Join(" - ", userCardsDb
				.Where(c => c.Active && cardCodes.Contains(c.CardCode) &&
					c.LastDigits == card.LastDigits &&
					c.ExternalCardToken == card.ExternalCardToken
				)
				.Select(c => c.Id)
				.ToList()
			);

			return new ParticipantRegistrationLog(RegistrationAction.UPDATE_CARD,
				userId, campaignId, accountOperator, card.LocationInfo)
			{
				AdditionalInformation = new Dictionary<string, string>()
				{
					{ "ID", cardId },
					{ "Código Cartão", cardCode },
					{ "Últimos dígitos Cartão", card.LastDigits },
					{ "Identificador Cartão", card.CardId }
				}
			};
		}
	}

	[JsonConverter(typeof(StringEnumConverter))]
	public enum RegistrationAction
	{
		ACCOUNT_OPERATOR_ACCESS_BLOCKED,
		ACCOUNT_OPERATOR_ACCESS_UNBLOCKED,
		DELETE_ADDRESS,
		FIRST_ACCESS,
		FIRST_ACCESS_RESET,
		FIRST_ACCESS_AND_WALLET_RESET,
		PRIVACY_POLICY_ACCEPTANCE_CHANGED,
		NEW_ADDRESS,
		VR_ACCESS_ACCOUNT_OPERATOR_AUTHENTICATION_MIGRATION,
		UPDATE_ADDRESS,
		UPDATE_CONTACT,
		UPDATE_REGISTRATION_DATA,
		UPDATE_REGISTRATION_DATA_FROM_ADMIN,
		UPDATE_CARD
	}
}
