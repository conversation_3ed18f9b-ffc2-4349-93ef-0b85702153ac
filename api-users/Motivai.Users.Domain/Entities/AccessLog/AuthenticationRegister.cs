using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Users.Domain.Entities.AccessLog
{
    public class CampaignAuthenticationLog
    {
        public Guid Id { get; set; }
        public DateTime CreateDate { get; set; }

        [BsonRepresentation(BsonType.String)]
        public AuthenticationActionLog ActionLog { get; set; }
        public DateTime AccessDate { get; set; }
        public Guid CampaignId { get; set; }
        public Guid UserId { get; set; }
        public Guid? SessionId { get; set; }

        public Guid? AccountOperatorId { get; set; }
        public Guid? AccountOperatorLoginId { get; set; }

        public Guid? CallcenterId { get; set; }
        public string CallcenterName { get; set; }

        public Dictionary<string, string> Metadata { get; set; }

        public LocationInfo LocationInfo { get; set; }

        public CampaignAuthenticationLog() { }

        private static Dictionary<string, string> CreateMetadata(UserParticipantModel participantSession)
        {
            return new Dictionary<string, string>()
            {
                { "origem", participantSession.SessionOrigin },
                { "email", participantSession.GetEmail() },
            };
        }

        public static CampaignAuthenticationLog OfSuccessfulAuthentication(AuthenticationActionLog action,
            UserParticipantModel participantSession, ConnectionInfo connectionInfo)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                SessionId = participantSession.SessionId,
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = action,
                UserId = participantSession.UserId,
                CampaignId = participantSession.CampaignId,
                AccountOperatorId = participantSession.AccountOperatorId,
                AccountOperatorLoginId = participantSession.AccountOperatorLoginId,
                LocationInfo = LocationInfo.Of(participantSession.Timezone, connectionInfo),
                Metadata = CreateMetadata(participantSession),
            };
        }

        ///<summary>
        /// Só utilizar para registrar login fora da API Users.
        ///</summary>
        public static CampaignAuthenticationLog JustLogged(AuthenticationActionLog action, Guid userId, Guid campaignId,
            LocationInfo locationInfo)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = action,
                CampaignId = campaignId,
                UserId = userId,
                LocationInfo = locationInfo
            };
        }

        public static CampaignAuthenticationLog CallcenterLogged(CallcenterLogin callcenterLogin,
                UserParticipantModel participantSession, LocationInfo locationInfo)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = AuthenticationActionLog.LOGIN_WITH_CALLCENTER,
                CampaignId = participantSession.CampaignId,
                UserId = participantSession.UserId,
                CallcenterId = callcenterLogin.CallcenterUserId,
                CallcenterName = callcenterLogin.CallcenterUsername,
                LocationInfo = locationInfo,
                Metadata = CreateMetadata(participantSession),
            };
        }

        public static CampaignAuthenticationLog AccountOperatorLogged(AuthenticationActionLog action,
            AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin,
            string sessionOrigin, LocationInfo locationInfo)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = action,
                CampaignId = accountOperatorLogin.CampaignId,
                AccountOperatorId = accountOperator.Id,
                AccountOperatorLoginId = accountOperatorLogin.Id,
                LocationInfo = locationInfo,
                Metadata = new Dictionary<string, string>()
                {
                    { "origem", sessionOrigin },
                    { "email", accountOperatorLogin.Email }
                }
            };
        }

        public static CampaignAuthenticationLog OfAuthenticationTried(Guid campaignId, AuthenticationActionLog actionLog,
            UserParticipantCampaign user)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = actionLog,
                UserId = user.UserId,
                CampaignId = campaignId,
                Metadata = new Dictionary<string, string>() {
                    { "email", user.GetMainEmail() },
                },
            };
        }

        public static CampaignAuthenticationLog OfCallcenterAuthenticationTried(Guid campaignId,
                AuthenticationActionLog actionLog, CallcenterLogin callcenterLogin)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = actionLog,
                UserId = callcenterLogin.UserId,
                CampaignId = campaignId,
                CallcenterId = callcenterLogin.CallcenterUserId,
                CallcenterName = callcenterLogin.CallcenterUsername,
                LocationInfo = callcenterLogin.GetLocationInfo(),
                Metadata = new Dictionary<string, string>() {
                    { "origem", "Callcenter" },
                },
            };
        }

        public static CampaignAuthenticationLog OfAuthenticationTried(Guid sourceCampaignId, AuthenticationActionLog actionLog,
            Guid targetCampaignId)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = actionLog,
                CampaignId = sourceCampaignId,
                Metadata = new Dictionary<string, string>() {
                    { "campanha de destino", targetCampaignId.ToString()},
                },
            };
        }

        public static CampaignAuthenticationLog OfAuthenticationTried(Guid campaignId, AuthenticationActionLog actionLog,
            ParticipantLoginModel participantLogin)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = actionLog,
                CampaignId = campaignId,
                Metadata = new Dictionary<string, string>() {
                    { "origem", participantLogin.Origin ?? "Plataforma" },
                    { "login", participantLogin.Login }
                },
                LocationInfo = participantLogin.GetLocationInfo()
            };
        }

        public static CampaignAuthenticationLog OfAuthenticationTried(Guid campaignId, AuthenticationActionLog actionLog,
            User user)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                ActionLog = actionLog,
                CampaignId = campaignId,
                Metadata = new Dictionary<string, string>() {
                    { "documento", user?.Document }
                }
            };
        }

        public static CampaignAuthenticationLog CampaignSiteAuthenticationTried(Guid campaignId,
            AuthenticationActionLog actionLog, CampaignSiteLogin login)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                ActionLog = actionLog,
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                CampaignId = campaignId,
                UserId = login.UserId,
                AccountOperatorId = login.AccountOperatorId,
                AccountOperatorLoginId = login.AccountOperatorLoginId,
                LocationInfo = login.GetLocationInfo(),
                Metadata = new Dictionary<string, string>() {
                    { "origem", "Site Campanha" },
                }
            };
        }

        public static CampaignAuthenticationLog TryLogged(Guid campaignId, AuthenticationActionLog actionLog,
                UserParticipantModel participant)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                ActionLog = actionLog,
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                CampaignId = campaignId,
                UserId = participant.UserId,
                AccountOperatorId = participant.AccountOperatorId,
                AccountOperatorLoginId = participant.AccountOperatorLoginId,
                Metadata = CreateMetadata(participant),
            };
        }

        public static CampaignAuthenticationLog OfIntegrationAuthenticationTried(Guid campaignId,
                LogRegisterAction logAction, AuthenticationActionLog actionLog,
                ParticipantIntegrationData participantIntegrationData)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                AccessDate = DateTime.UtcNow,
                CampaignId = campaignId,
                ActionLog = actionLog,
                Metadata = new Dictionary<string, string>() {
                    { "origem", participantIntegrationData.Origin ?? "Integração SSO" },
                    { "action", logAction.ToString() },
                    { "token", participantIntegrationData.Token },
                    { "documento", participantIntegrationData.Document },
                    { "login", participantIntegrationData.Login },
                    { "email", participantIntegrationData.Email },
                },
                LocationInfo = participantIntegrationData.GetLocationInfo()
            };
        }

        public static CampaignAuthenticationLog OfIntegrationEndingAuthenticationTried(Guid campaignId,
                AuthenticationActionLog actionLog, LoginSsoEndingRequest loginSso)
        {
            return new CampaignAuthenticationLog()
            {
                Id = Guid.NewGuid(),
                CreateDate = DateTime.UtcNow,
                ActionLog = actionLog,
                AccessDate = DateTime.UtcNow,
                CampaignId = campaignId,
                Metadata = new Dictionary<string, string>() {
                    { "origem", "Finalização SSO" },
                    { "token", loginSso?.Token.ToString() },
                },
                LocationInfo = loginSso?.GetLocationInfo()
            };
        }
    }
}