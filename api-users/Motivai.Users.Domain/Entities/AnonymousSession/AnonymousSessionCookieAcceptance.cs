using System;
using Motivai.SharedKernel.Domain.Entities;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Models.AnonymousSession;

namespace Motivai.Users.Domain.Entities.AnonymousSession
{
	public class AnonymousSessionCookieAcceptance : BaseEntity
	{
		public bool CookieAccepted { get; set; }
		public LocationInfo LocationInfo;

		public static AnonymousSessionCookieAcceptance Of(AnonymousSessionAcceptanceInfo session)
		{
			var acceptance = new AnonymousSessionCookieAcceptance
			{
				CookieAccepted = session.CookieAccepted,
				LocationInfo = LocationInfo.Of(session.Timezone, session.ConnectionInfo)
			};
			acceptance.StartValidation();
			return acceptance;
		}

		public override void Validate()
		{
			if (this.LocationInfo == null)
				throw MotivaiException.ofValidation("Sessao anonima nao possui locationInfo");
		}
	}
}