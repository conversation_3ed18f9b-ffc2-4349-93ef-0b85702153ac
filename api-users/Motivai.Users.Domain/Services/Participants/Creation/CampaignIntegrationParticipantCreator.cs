using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Values;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.IApp.AccountOperators;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.Domain.Services.Participants.Creation
{
	public class CampaignIntegrationParticipantCreator
	{
		private readonly IUserRepository _userRepository;
		private readonly IAccountOperatorManager accountOperatorManager;
		private readonly IUserMetadataApp userMetadataApp;
		private readonly ParticipantsGroupService participantsGroupService;
		private readonly CampaignParticipantConfigurator campaignParticipantConfigurator;
		private readonly ITransactionApiRepository transactionApiRepository;

		public CampaignIntegrationParticipantCreator(IUserRepository userRepository,
				IAccountOperatorManager accountOperatorManager, IUserMetadataApp userMetadataApp,
				ParticipantsGroupService participantsGroupService,
				CampaignParticipantConfigurator campaignParticipantConfigurator,
				ITransactionApiRepository transactionApiRepository) {
			this._userRepository = userRepository;
			this.accountOperatorManager = accountOperatorManager;
			this.userMetadataApp = userMetadataApp;
			this.participantsGroupService = participantsGroupService;
			this.campaignParticipantConfigurator = campaignParticipantConfigurator;
			this.transactionApiRepository = transactionApiRepository;
		}

		public async Task CreateAccountOperatorIfNeeded(Guid campaignId, CampaignSettingsModel campaignSettings, Guid userId, AccountOperatorInfo operatorLogin) {
			if (!campaignSettings.Parametrizations.AllowAccountOperators || operatorLogin == null) {
				return;
			}

			var login = operatorLogin.Login;
			if (string.IsNullOrEmpty(login)) {
				login = operatorLogin.Email;
			}

			try {
				await accountOperatorManager.CreateAccountOperator(userId, campaignId, new ResumedAccountOperator() {
					Name = operatorLogin.Name,
					Document = operatorLogin.Document,
					AccountDocument = operatorLogin.AccountDocument,
					CreationOrigin = operatorLogin.GetMappedCreationOrigin(),
					Email = operatorLogin.Email,
					Login = login
				});
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "ParticipantCreator - Operator", "Erro ao cadastrar operador.");
			}
		}

		public Task<ResumedAccountOperator> GetOperatorBy(Guid userId, Guid campaignId, string document) {
			return accountOperatorManager.GetOperatorBy(userId, campaignId, document);
		}

		public Task SetUpdateDateToNow(Guid userId, Guid campaignId) {
			return _userRepository.SetUpdateDateToNow(userId, campaignId);
		}

		public async Task<UserParticipantCampaign> CreateParticipantFrom(Guid campaignId, CampaignSettingsModel campaignSettings,
			User user, ParticipantIntegrationData participantIntegrationData)
        {
            LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Doc {2} - Login {3} - Skip Doc Val {4} - Criando participante",
                campaignId, user.Id, participantIntegrationData.Document, participantIntegrationData.Login,
                participantIntegrationData.SkipDocumentValidation);

            UserParticipantCampaign participant = UserParticipantCampaign.CreateForCampaign(campaignId, campaignSettings, user, participantIntegrationData.Login);

            await VerifyIfLoginAlreadyInUse(campaignId, user.GetDocument(), participant.Login);

            if (string.IsNullOrEmpty(participantIntegrationData.Origin))
            {
                participant.Origin = "ParticipantImportApi";
            }
            else
            {
                participant.Origin = participantIntegrationData.Origin;
            }
            participant.ClientUserId = participantIntegrationData.ClientUserId;
            participant.IntegratedLogin = participantIntegrationData.IntegratedLogin;
            if (!participantIntegrationData.SkipPasswordGeneration || string.IsNullOrEmpty(participantIntegrationData.Password))
            {
                participantIntegrationData.Password = AlphanumericGenerator.GenerateId16();
            }
            participant.OverridePassword(participantIntegrationData.Password);

            var mobilePhone = TelephoneHelper.IsMobilePhone(participantIntegrationData.Cellphone) ? participantIntegrationData.Cellphone : null;
            participant.Contact = Contact.Of(participantIntegrationData.Telephone, mobilePhone, participantIntegrationData.Email);

            if (participantIntegrationData.Address != null)
            {
                participantIntegrationData.Address.MainAddress = true;
                participant.AddAddress(participantIntegrationData.Address);
            }

            if (participantIntegrationData.HasCompanyIntegrationData())
            {
                participant.IntegrationCompany = participantIntegrationData.IntegrationCompany;
                // Até finalizar a migration
                participant.Employee = new Employee()
                {
                    CompanyName = participantIntegrationData.IntegrationCompany.CompanyName,
                    CompanyIdentifier = participantIntegrationData.IntegrationCompany.CompanyIdentifier
                };
            }

            if (participantIntegrationData.ParentUserId != Guid.Empty)
            {
                participant.ParentUserId = participantIntegrationData.ParentUserId;
                participant.ParentUserDetails = await _userRepository.GetUserBasicInfo(campaignId, participantIntegrationData.ParentUserId);
            }

            var wasUpdate = await this._userRepository.CreateParticipant(user.Id, participant);

            await SaveUserMetadata(campaignId, user, participantIntegrationData, participant);

            await this.campaignParticipantConfigurator.ExecutePostCreationActions(campaignSettings, user, participant, participantIntegrationData);

            if (!wasUpdate)
                return null;

			await AddParticipantToGroups(campaignId, user, participantIntegrationData);

            return participant;
        }

        public async Task VerifyIfLoginAlreadyInUse(Guid campaignId, string document, string login)
		{
			if (string.IsNullOrEmpty(login))
			{
				return;
			}

			var existsParticipantWithLogin = await _userRepository.ExistParticipantWithLogin(campaignId, login);
			if (existsParticipantWithLogin)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Doc {1} - Login {2} - Login já existe na campanha",
					campaignId, document, login);
				throw MotivaiException.ofValidation(
					"USER_LOGIN_ALREADY_EXISTS",
					"Já existe outro participante com o mesmo login, por favor, entre em contato com o atendimento."
				);
			}
		}

        public async Task SaveUserMetadata(Guid campaignId, User user, ParticipantIntegrationData participantIntegrationData, UserParticipantCampaign participant)
        {
            if (participantIntegrationData.Metadata == null || participantIntegrationData.Metadata.Count == 0)
            {
				return;
            }
			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Doc {2} - Salvando metadata", campaignId, user.Id, participantIntegrationData.Document);
			await userMetadataApp.SaveUserMetadata(user.Id, campaignId, participant.Id, participantIntegrationData.Metadata, "ParticipantImportApi");
        }

		public async Task AddParticipantToGroups(Guid campaignId, User user, ParticipantIntegrationData participantIntegrationData)
		{
            if (!participantIntegrationData.HasAnyGroup())
            {
				return;
            }
			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Doc {2} - Inserindo participante nos grupos",
					campaignId, user.Id, participantIntegrationData.Document);
			try
			{
				var result = await this.participantsGroupService.AddParticipantToGroupIfNeed(campaignId, user.Id, participantIntegrationData.GroupsCodes);
				if (!result) {
					throw MotivaiException.ofValidation("PARTICIPANT_GROUPS_ERROR",
						"Não foi possível adicionar o participante no grupo.");
				}
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "ParticipantImporter - Grupos", "Erro ao atualizar o participante nos grupos", true);
				// throw ex.WrapIfNotValidationException("PARTICIPANT_GROUPS_ERROR", "Erro ao atualizar os grupos do participante");
			}
		}

		public async Task<Guid?> CreateAccountOperatorIfNeeded(Guid campaignId, Guid userId, AccountOperatorInfo accountOperator)
		{
			if (accountOperator == null)
			{
				return null;
			}

			var login = accountOperator.Login;
			if (string.IsNullOrEmpty(login))
			{
				login = string.IsNullOrEmpty(accountOperator.Email) ? accountOperator.Document : accountOperator.Email;
			}

			try
			{
				return await accountOperatorManager.CreateOrUpdateAccountOperator(userId, campaignId, new ResumedAccountOperator()
				{
					Name = accountOperator.Name,
					Document = accountOperator.Document,
					AccountDocument = accountOperator.AccountDocument,
					CreationOrigin = accountOperator.GetMappedCreationOrigin(),
					Email = accountOperator.Email,
					MobilePhone = accountOperator.MobilePhone,
					Login = login,
					Role = OperatorRole.MASTER
				});
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "ParticipantImporter - Operator", "Erro ao cadastrar operador.");
				LoggerFactory.GetLogger().Error("ParticipantImporter - Cmp {0} - User {1} - Operator ({2} - {3}) - Erro ao cadastrar operador: {4}",
					campaignId, userId, accountOperator.Document, accountOperator.Email, ex.Message);
				return null;
			}
		}

		// TODO: A mesma logica existe também no BaseAuthenticator, utilizado pelo AccountOperatorAuthenticator e MobileAuthenticator
		// verificar é o caso de mudar a estrutura
		public async Task UpdateParticipantBalance(User user, UserParticipantCampaign participant)
        {
            try
            {
                var balance = await transactionApiRepository.GetBalance(participant.CampaignId, user.Id);
                participant.Balance = balance;
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "IntegrationParticipantCreator", "Erro ao carregar saldo.");
            }
        }
	}
}
