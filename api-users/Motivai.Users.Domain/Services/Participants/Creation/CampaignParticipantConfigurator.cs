using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.Models.Import;
using Motivai.Users.Domain.Services.Notifications;

namespace Motivai.Users.Domain.Services.Participants.Creation {
	public class CampaignParticipantConfigurator {
		private readonly ParticipantNotificator participantNotificator;
		private readonly ICampaignGoalsRepository campaignGoalsRepository;
		private readonly ICampaignTargetAudienceRepository campaignTargetAudienceRepository;

		public CampaignParticipantConfigurator(ParticipantNotificator participantNotificator,
			ICampaignGoalsRepository campaignGoalsRepository,
			ICampaignTargetAudienceRepository campaignTargetAudienceRepository) {
			this.participantNotificator = participantNotificator;
			this.campaignGoalsRepository = campaignGoalsRepository;
			this.campaignTargetAudienceRepository = campaignTargetAudienceRepository;
		}

		public async Task ExecutePostCreationActions(CampaignSettingsModel campaignSettings,
			User user, UserParticipantCampaign participant, ParticipantIntegrationData participantIntegrationData) {
			var createdParticipant = CreatedParticipant.Of(user, participant, participantIntegrationData);
			await ExecutePostCreationActions(campaignSettings, createdParticipant, participantIntegrationData.SkipPostProcessing);
		}

		public async Task ExecutePostCreationActions(CampaignSettingsModel campaignSettings,
			User user, UserParticipantCampaign participant, string generatedPassword) {
			var createdParticipant = CreatedParticipant.Of(user, participant, generatedPassword);
			await ExecutePostCreationActions(campaignSettings, createdParticipant);
		}

		private async Task ExecutePostCreationActions(CampaignSettingsModel campaignSettings,
			CreatedParticipant createdParticipant, bool skipPostProcessing = false) {

			await participantNotificator.SendNotification(createdParticipant);

			if (campaignSettings.Parametrizations.ExecuteTargetAudienceForNewParticipant) {
				await ScheduleTargetAudienceExecutionFor(createdParticipant);
			}

			if (!skipPostProcessing) {
				await ExecuteCampaignConfiguration(campaignSettings, createdParticipant);
			}
		}

		private async Task ScheduleTargetAudienceExecutionFor(CreatedParticipant createdParticipant) {
			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Enviando para execução do público alvo",
				createdParticipant.CampaignId, createdParticipant.UserId);
			try {
				await campaignTargetAudienceRepository.ExecuteTargetAudienceFor(createdParticipant.CampaignId, createdParticipant.UserId);
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "ParticipantConfigurator - Post Action", "Erro ao enviar participante para rodar público alvo");
			}
		}

		private async Task ExecuteCampaignConfiguration(CampaignSettingsModel campaignSettings, CreatedParticipant createdParticipant) {
			if (campaignSettings.Parametrizations.ConfigureCampaignGoalsForNewParticipant) {
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Enviando para configuração das metas",
					createdParticipant.CampaignId, createdParticipant.UserId);
				try {
					// TODO: mover pra fila
					await campaignGoalsRepository.ConfigureParticipantGoals(createdParticipant.CampaignId,
						createdParticipant.UserId, createdParticipant.ParticipantId);
				} catch (Exception ex) {
					await ExceptionLogger.LogException(ex, "ParticipantConfigurator - Post Action", "Erro ao configurar metas do participante");
				}
			}
		}
	}
}