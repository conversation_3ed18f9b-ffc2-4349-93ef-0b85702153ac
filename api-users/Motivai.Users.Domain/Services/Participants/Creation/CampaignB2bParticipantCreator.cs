using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.B2b;
using Motivai.Users.Domain.IApp.B2b;
using Motivai.Users.Domain.IRepository.B2b;
using Motivai.Users.Domain.IRepository.ClientIntegrations;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.Domain.Services.Participants.Creation {
    public class CampaignB2bParticipantCreator {
        private readonly IUserRepository _userRepository;
        private readonly IClientIntegrations _clientsIntegrations;
        private readonly IShopkeeperRepository _shopkeeperRepository;
        private readonly IShopKeeperApp _shopkeeperApp;

        public CampaignB2bParticipantCreator(IUserRepository userRepository, IClientIntegrations clientsIntegrations,
                IShopkeeperRepository shopkeeperRepository, IShopKeeperApp shopkeeperApp) {
            this._userRepository = userRepository;
            this._clientsIntegrations = clientsIntegrations;
            this._shopkeeperRepository = shopkeeperRepository;
            this._shopkeeperApp = shopkeeperApp;
        }

        public async Task<UserParticipantCampaign> CreateParticipantFromShopkeeper(Guid campaignId, string document, CampaignSettingsModel campaignSettings) {
            if (!campaignSettings.Parametrizations.CreateParticipantOnIntegration)
                throw MotivaiException.ofValidation("Participante não encontrado.");

            var integrationData = await _clientsIntegrations.GetParticipantInfo(campaignId, document);
            if (integrationData == null)
                throw MotivaiException.ofValidation("Usuário não encontrado na integração.", true);

            IntegrationCompany integrationCompany = null;
            if (!string.IsNullOrEmpty(integrationData.CompanyName) || !string.IsNullOrEmpty(integrationData.CompanyIdentifier)) {
                integrationCompany = new IntegrationCompany() {
                    CompanyName = integrationData.CompanyName,
                    CompanyIdentifier = integrationData.CompanyIdentifier
                };
            }

            var shopkeeper = new Shopkeeper() {
                Document = document,
                Email = integrationData.MainEmail,
                Name = integrationData.Name ?? integrationData.CompanyName,
                MainPhone = integrationData.MainPhone,
                MobilePhone = integrationData.MobilePhone,
                Address = integrationData.MainAddress,
                Campaigns = new List<ShopkeeperCampaign>() {
                    new ShopkeeperCampaign() { CampaignId = campaignId, Active = true }
                },
                CreatedByIntegration = true,
                IntegrationCompany = integrationCompany
            };

            var dbShopkeeper = await _shopkeeperRepository.FindByDocument(document, null);
            if (dbShopkeeper == null) {
                var createdShopkeeper = await _shopkeeperApp.Create(shopkeeper, campaignSettings.BuId);
                if (createdShopkeeper == null || createdShopkeeper.UserId == Guid.Empty)
                    throw MotivaiException.ofValidation("Ocorreu um erro ao criar o lojista", true);

                var createdUser = await _userRepository.Get(createdShopkeeper.UserId);
                if (createdUser == null)
                    throw MotivaiException.ofValidation("Usuário não encontrado para o lojista informado", true);

                var participant = createdUser.GetParticipantByCampaign(campaignId);
                participant.ClientUserId = integrationData.ClientUserId;
                participant.CreatedByIntegration = true;
                await _userRepository.Save(createdUser);

                return participant;
            } else {
                var shopkeeperUpdated = await _shopkeeperApp.Update(dbShopkeeper.Id, shopkeeper, dbShopkeeper.BuId);
                if (!shopkeeperUpdated)
                    throw MotivaiException.ofValidation("Ocorreu um erro ao atualizar o cadastro do lojista", true);

                var user = await _userRepository.Get(dbShopkeeper.UserId);
                if (user == null)
                    throw MotivaiException.ofValidation("Usuário não encontrado para o lojista informado", true);

                var participant = user.GetParticipantByCampaign(campaignId);
                participant.ClientUserId = integrationData.ClientUserId;
                participant.CreatedByIntegration = true;
                await _userRepository.Save(user);

                return participant;
            }
        }
    }
}