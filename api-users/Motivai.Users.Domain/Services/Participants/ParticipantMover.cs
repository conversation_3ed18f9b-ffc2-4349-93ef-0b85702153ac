using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.Models.CampaignParticipantMigration;
using System.Linq;

namespace Motivai.Users.Domain.Services.Participants
{
	public class ParticipantMover
	{
        private readonly ICampaignRepository campaignRepository;
        private readonly IUserRepository userRepository;
		private readonly IUserMetadataValueRepository metadataRepository;
		private readonly IUserParticipationCampaignRepository participantRepository;
		private readonly ITransactionApiRepository transactionRepository;
		private readonly ICampaignTermsAcceptanceRepository campaignTermsAcceptanceRepository;
		private readonly ITransactionManager transactionManager;

		public ParticipantMover(ICampaignRepository campaignRepository, IUserRepository userRepository,
				IUserMetadataValueRepository metadataRepository, IUserParticipationCampaignRepository participantRepository,
				ITransactionApiRepository transactionRepository, ICampaignTermsAcceptanceRepository campaignTermsAcceptanceRepository,
				ITransactionManager transactionManager)
		{
            this.campaignRepository = campaignRepository;
            this.userRepository = userRepository;
			this.metadataRepository = metadataRepository;
			this.participantRepository = participantRepository;
			this.transactionRepository = transactionRepository;
			this.campaignTermsAcceptanceRepository = campaignTermsAcceptanceRepository;
			this.transactionManager = transactionManager;
		}

		public async Task<bool> MoveParticipant(Guid campaignId, Guid sourceUserId, UserMoveRequest moveRequest)
		{
			if (moveRequest == null)
				throw MotivaiException.ofValidation("Selecione o usuário de destino.");
			moveRequest.Validate();

			var currentUser = await userRepository.Get(sourceUserId);
			if (currentUser == null)
				throw MotivaiException.ofValidation("Participante a ser movido não foi encontrado.");
			if (!string.IsNullOrEmpty(currentUser.GetDocument()))
				throw MotivaiException.ofValidation("Só é permitido mover participante que não tenha CPF/CNPJ vinculado.");

			User targetUser;
			if (moveRequest.UserId == Guid.Empty)
			{
				targetUser = await userRepository.GetByDocument(moveRequest.UserDocument);
			}
			else
			{
				targetUser = await userRepository.Get(moveRequest.UserId);
			}

			// TODO: se o CPF/CNPJ não existir então atualizar o participante existente
			if (targetUser == null)
				throw MotivaiException.ofValidation("Participante de destino não foi encontrado.");

			await MoveParticipantToAnotherUser(campaignId, moveRequest.OperationUser, currentUser, targetUser);

			return true;
		}

		private async Task MoveParticipantToAnotherUser(Guid campaignId, OperationUser operationUser, User currentUser, User targetUser)
		{
			var existingParticipant = targetUser.GetParticipantByCampaign(campaignId);
			if (existingParticipant != null)
			{
				throw MotivaiException.ofValidation("Já existe um participante na campanha com o mesmo CPF/CNPJ.");
			}

			LoggerFactory.GetLogger().Info("ParticipantMover - Cmp {0} - Usr {1} - Iniciando mudança do participante para {2} - {3}",
				campaignId, currentUser.Id, targetUser.GetDocument(), targetUser.Id);

			await ValidateParticipantToMove(campaignId, currentUser);

			// Atualiza o UserId do participante atual
			var currentParticipant = currentUser.GetParticipantByCampaign(campaignId);
			var newParticipant = currentParticipant.Clone();
			newParticipant.UserId = targetUser.Id;
			if (!newParticipant.Addresses.IsNullOrEmpty())
			{
				newParticipant.Addresses.ForEach(address => address.UserId = targetUser.Id);
			}
			if (targetUser.UsersParticipantCampaign == null)
			{
				targetUser.UsersParticipantCampaign = new List<UserParticipantCampaign>(1);
			}
			targetUser.AddParticipant(newParticipant);

			var userMetadata = await this.metadataRepository.GetUsersMetadataValue(currentUser.Id, campaignId);
			if (userMetadata != null)
			{
				userMetadata = userMetadata.Clone();
				userMetadata.UserId = targetUser.Id;
				userMetadata.ParticipantId = newParticipant.Id;
				userMetadata.Origin = "ParticipantMover";
			}

			LoggerFactory.GetLogger().Info("ParticipantMover - Cmp {0} - Usr {1} - Salvando o user de destino",
				campaignId, currentUser.Id);
			await this.userRepository.Save(targetUser);

			if (userMetadata != null)
			{
				await this.metadataRepository.CreateUserMetadata(userMetadata);
			}

			LoggerFactory.GetLogger().Info("ParticipantMover - Cmp {0} - Usr {1} - Atualizando filhos para {2}",
				campaignId, currentUser.Id, targetUser.Id);
			// atualizar os participantes filhos com o novo ID
			await participantRepository.ChangeParticipantsParent(currentUser.Id, targetUser.Id, new UserBasicInfo()
			{
				UserId = targetUser.Id,
				ParticipantId = newParticipant.Id,
				Name = targetUser.Name,
				Document = targetUser.GetDocument()
			});

			LoggerFactory.GetLogger().Info("ParticipantMover - Cmp {0} - Usr {1} - Desativando o user antigo",
				campaignId, currentUser.Id);
			// remover ou desativar o User antigo (inativa o User somente se tiver apenas um participante)
			currentUser.Active = currentUser.UsersParticipantCampaign.Count() != 1;
			currentParticipant.Active = false;
			currentParticipant.Login += "-DESATIVADO";
			if (!string.IsNullOrEmpty(currentParticipant.ClientUserId))
			{
				currentParticipant.ClientUserId += "-DESATIVADO";
			}
			currentParticipant.Block(BlockingOrigin.Admin, $"Participante foi movido para outro usuário com CPF/CNPJ {targetUser.GetDocument()}", operationUser);
			await this.userRepository.Save(currentUser);

			var wasUpdated = await this.participantRepository.UpdateParticipant(currentParticipant);
			if (wasUpdated)
            {
                await MigrateLinkedParticipantsGroups(campaignId, currentUser, targetUser);
            }
            else
			{
				LoggerFactory.GetLogger().Error("ParticipantMover - Cmp {0} - Usr {1} - Participante antigo não foi atualizado",
					campaignId, currentUser.Id);
			}
		}

        private async Task MigrateLinkedParticipantsGroups(Guid campaignId, User currentUser, User targetUser)
        {
			LoggerFactory.GetLogger().Info("ParticipantMover - Cmp {0} - Usr {1} - Migrando os grupos para UserId {2}",
				campaignId, currentUser.Id, targetUser.Id);
			try
			{
				var migration = new CampaignParticipantMigration(currentUser.Id, targetUser.Id);
				await this.campaignRepository.MigrateUserParticipantsGroups(campaignId, migration);
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("ParticipantMover - Cmp {0} - Usr {1} - Erro na migração dos grupos: {2}",
					campaignId, currentUser.Id, ex.Message);
				await ExceptionLogger.LogException(ex, "Participant Mover", "Erro na migração dos grupos", true);
			}
        }

        private async Task ValidateParticipantToMove(Guid campaignId, User currentUser)
		{
			LoggerFactory.GetLogger().Error("ParticipantMover - Cmp {0} - Usr {1} - Validando histórico do participante",
				campaignId, currentUser.Id);

			// Não permite mudar o participante caso já tenha feito o primeiro acesso
			if (await this.campaignTermsAcceptanceRepository.HasAcceptedAnyTerm(currentUser.Id, campaignId))
			{
				throw MotivaiException.ofValidation("Participante já realizou o aceito do regulamento.");
			}

			var campaignIntegrationSettings = await campaignRepository.GetIntegrationSettings(campaignId);

			// somente campanha com saldo integrado será permitido migrar com saldo
			if (campaignIntegrationSettings.HasClientIntegration() && campaignIntegrationSettings.IntegrationSettings.EnableBalance)
			{
				LoggerFactory.GetLogger().Info("ParticipantMover - Cmp {0} - Usr {1} - Campanha com saldo integrado - permitindo mover",
					campaignId, currentUser.Id);
			}
			else
			{
				// casos que não poderá mover o participante: tem mecânica de saldo disponível;
				if (await this.transactionRepository.GetBalance(campaignId, currentUser.Id) != 0)
				{
					throw MotivaiException.ofValidation("Não é permitido mover participante que tenha saldo.");
				}

				// valida se existe pelo menos uma transação (não permite perder o histórico)
				var extract = await this.transactionManager.GetTransactions(currentUser.Id, campaignId);
				if (extract != null && !extract.Transactions.IsNullOrEmpty())
				{
					throw MotivaiException.ofValidation("Não é permitido mover participante que tenha movimentações.");
				}
			}
		}
	}
}
