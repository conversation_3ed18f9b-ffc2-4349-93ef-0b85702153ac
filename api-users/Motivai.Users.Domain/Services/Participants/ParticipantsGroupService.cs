using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.IRepository.Camapaigns;

namespace Motivai.Users.Domain.Services.Participants
{
	public class ParticipantsGroupService
	{
		private readonly ICampaignRepository campaignRepository;

		public ParticipantsGroupService(ICampaignRepository campaignRepository)
		{
			this.campaignRepository = campaignRepository;
		}

		public async Task<bool> AddParticipantToGroupIfNeed(Guid campaignId, Guid userId, List<string> groupsCodes)
		{
			if (groupsCodes.IsNullOrEmpty())
			{
				return false;
			}

			try
			{
				return await this.campaignRepository.AddParticipantToGroups(campaignId, userId, groupsCodes);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Participants Groups", "Erro ao atualizar os grupos do participante");
				throw ex.WrapIfNotValidationException("PARTICIPANT_GROUPS_ERROR", "Erro ao atualizar os grupos do participante");
			}
		}
	}
}