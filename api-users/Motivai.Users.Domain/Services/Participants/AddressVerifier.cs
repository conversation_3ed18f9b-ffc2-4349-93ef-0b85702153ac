using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.IRepository.Correios;

namespace Motivai.Users.Domain.Services.Participants {
    public class AddressVerifier {
        private readonly ICorreiosRepository correiosRepository;

        public AddressVerifier(ICorreiosRepository correiosRepository) {
            this.correiosRepository = correiosRepository;
        }

        /// <summary>
        /// Corrige os dados de endereço.
        /// </summary>
        public async Task CorrectAddress(Guid campaignId, string userDocument, Address address) {
            if (address == null) return;
            try {
                var correiosAddress = await correiosRepository.QueryCep(address.Cep);
                if (correiosAddress == null) {
                    LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Correção de Endereço - Endereço não encontrado pelo CEP {2}.",
                        campaignId, userDocument, address.Cep);
                    address.CepSearchMessage = "Endereço não encontrado pelo CEP informado";
                    return;
                }

                if (correiosAddress.Street != address.Street || correiosAddress.City != address.City) {
                    LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Correção de Endereço - Endereço do CEP {2} corrigido de '{3}' para '{4}'.",
                        campaignId, userDocument, address.Cep, address.Street, correiosAddress.Street);
                }

                if (!address.IsFilledManually()) {
                    if (!string.IsNullOrEmpty(correiosAddress.Street) && address.Street != correiosAddress.Street) {
                        address.CepSearchMessage = $"Logradouro do CEP {address.Cep} está diferente do Correios. Informado: '{address.Street}', Correios: '{correiosAddress.Street}'";
                        address.Street = correiosAddress.Street;
                    }
                    if (!string.IsNullOrEmpty(correiosAddress.Neighborhood) && address.Neighborhood != correiosAddress.Neighborhood) {
                        address.CepSearchMessage = $"Bairro do CEP {address.Cep} está diferente do Correios. Informado: '{address.Neighborhood}', Correios: '{correiosAddress.Neighborhood}'";
                        address.Neighborhood = correiosAddress.Neighborhood;
                    }
                }

                if (!string.IsNullOrEmpty(correiosAddress.City) && address.City != correiosAddress.City) {
                    LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Correção de Endereço - Cidade do CEP {2} corrigido de '{3}' para '{4}'.",
                        campaignId, userDocument, address.Cep, address.City, correiosAddress.City);
                    address.CepSearchMessage = $"Cidade do CEP {address.Cep} está diferente do Correios. Informado: '{address.City}', Correios: '{correiosAddress.City}'";
                    address.City = correiosAddress.City;
                }

                if (!string.IsNullOrEmpty(correiosAddress.State)) {
                    address.State = correiosAddress.State;
                    address.Uf = correiosAddress.Uf;
                }
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "AddressVerifier - Correção Endereço", "Erro ao verificar endereço.", true);
                LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Correção de Endereço - Erro durante verificação do endereço: {2}.",
                    campaignId, userDocument, ex.Message);
            }
        }
    }
}