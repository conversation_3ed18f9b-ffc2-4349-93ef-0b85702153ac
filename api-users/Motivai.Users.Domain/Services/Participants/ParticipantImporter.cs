using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.Services.Participants.Creation;
using Motivai.Users.Domain.Services.Users.Creation;

namespace Motivai.Users.Domain.Services.Participants
{
    public class ParticipantImporter
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
		private readonly CampaignIntegrationParticipantCreator campaignIntegrationParticipantCreator;
		private readonly CampaignIntegrationUserCreator campaignIntegrationUserCreator;

		public ParticipantImporter(ICampaignRepository campaignRepository, ICampaignsGroupsApp campaignsGroupsApp,
			CampaignIntegrationParticipantCreator campaignIntegrationParticipantCreator,
			CampaignIntegrationUserCreator campaignIntegrationUserCreator)
		{
			this.campaignRepository = campaignRepository;
			this.campaignsGroupsApp = campaignsGroupsApp;
			this.campaignIntegrationParticipantCreator = campaignIntegrationParticipantCreator;
			this.campaignIntegrationUserCreator = campaignIntegrationUserCreator;
		}

		public async Task<dynamic> ImportUserIfDoesntExist(Guid campaignId, ParticipantIntegrationData participantIntegrationData)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (participantIntegrationData == null)
				throw MotivaiException.ofValidation("Dados do participante são obrigatórios.");
			participantIntegrationData.Validate();

			//TODO Validar Origin correto para o importador
			if (string.IsNullOrEmpty(participantIntegrationData.Origin)) {
				participantIntegrationData.Origin = "ImportApi";
			}

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			User user = await this.campaignIntegrationUserCreator.FindOrCreateUser(campaignId, campaignSettings, participantIntegrationData);
			if (user == null)
				return null;

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant != null && participantIntegrationData.ShouldValidateLogin())
			{
				participantIntegrationData.EnsureSameLogin(participant.Login);
			}

			var wasCreated = false;
			if (participant == null)
			{
				wasCreated = true;
				participant = await this.campaignIntegrationParticipantCreator.CreateParticipantFrom(campaignId, campaignSettings, user, participantIntegrationData);
			}
			else if (participantIntegrationData.UpdateMetadataAndGroups)
			{
				LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Updating metadata and groups",
						campaignId, user.Id);
				await this.campaignIntegrationParticipantCreator.SaveUserMetadata(campaignId, user, participantIntegrationData, participant);
				await this.campaignIntegrationParticipantCreator.AddParticipantToGroups(campaignId, user, participantIntegrationData);
				await this.campaignIntegrationParticipantCreator.SetUpdateDateToNow(user.Id, campaignId);
			}

			if (participant == null)
				return null;

			if (campaignSettings.Parametrizations.AllowAccountOperators && participantIntegrationData.Operator != null)
			{
				await this.campaignIntegrationParticipantCreator.CreateAccountOperatorIfNeeded(campaignId, user.Id, participantIntegrationData.Operator);
			}

			if (wasCreated && campaignSettings.Parametrizations.IsEnableGroupCoalition())
			{
				await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(
					EventType.PARTICIPANT_IMPORTED, Guid.Empty, campaignId, user.Id, participant.Id,
					null, participantIntegrationData.Operator?.GetMappedCreationOrigin()
				);
			}

			if (wasCreated) {
				LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Usr {2} - Importação finalizada",
						campaignId, participantIntegrationData.Document, user.Id);
			}

			var userDocument = user.GetDocument();
			return new
			{
				NewParticipant = wasCreated,
				UserId = user.Id,
				ParticipantId = participant.Id,
				PersonType = string.IsNullOrEmpty(userDocument) ? (PersonType?)null : user.Type,
				UserDocument = userDocument,
				Name = user.GetName(),
				participant.FirstAccess
			};
		}
	}
}
