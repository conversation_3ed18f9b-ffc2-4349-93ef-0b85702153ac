using System.Text.RegularExpressions;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.Domain.Services.Validations
{
	public class CampaignParticipantValidations
	{
		public static void ValidateAddress(Address address, CampaignSettingsModel campaignSettings)
		{
			if (address == null)
				throw MotivaiException.ofValidation("Preencha os dados do endereço para continuar.");

			if (campaignSettings.Type.HasMarketplace())
			{
				// Não permite letras no campo número
				if (address.Number != null && !Regex.IsMatch(address.Number, @"^\d+$"))
				{
					throw MotivaiException.ofValidation("Não é permitido letras no campo número do endereço, por favor, preencha no campo complemento.");
				}
			}

			ValidateShippingAddressDetails(address, campaignSettings);
		}

		public static void ValidateFirstAccessAddress(Address address, ParticipantData participantSettings, CampaignSettingsModel campaignSettings)
		{
			if (address == null)
				throw MotivaiException.ofValidation("Preencha os dados do endereço para continuar.");
			participantSettings.Address.ValidateAddress(address);

			if (campaignSettings.Type.HasMarketplace())
			{
				// Não permite letras no campo número
				if (address.Number != null && !Regex.IsMatch(address.Number, @"^\d+$"))
				{
					throw MotivaiException.ofValidation("Não é permitido letras no campo número do endereço, por favor, preencha no campo complemento.");
				}
			}

			ValidateShippingAddressDetails(address, campaignSettings);

		}

		private static void ValidateShippingAddressDetails(Address address, CampaignSettingsModel campaignSettings)
		{
			if (campaignSettings.Parametrizations.RequiredShippingAddressDetails)
			{
				address.Complement.ForNullOrEmpty("Complemento é obrigatório.");
				address.Reference.ForNullOrEmpty("Ponto de referência é obrigatório.");
				if (address.Reference != null && address.Reference.Length < 4)
				{
					throw MotivaiException.ofValidation("Ponto de referência precisa ter no mínimo 4 caracteres.");
				}
			}
		}
	}

}