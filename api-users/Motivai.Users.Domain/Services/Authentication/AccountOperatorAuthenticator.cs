using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.ex;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Services.Authentication
{
	public class AccountOperatorAuthenticator : BaseAuthenticator
	{
		private readonly IAccessLogRepository accessLogRepository;
		private readonly IAccountOperatorRepository accountOperatorRepository;

		public AccountOperatorAuthenticator(IUserRepository userRepository, ICampaignRepository campaignRepository,
			ITransactionApiRepository transactionRepository, IAccessLogRepository accessLogRepository,
			IAccountOperatorRepository accountOperatorRepository, IAccessControlRegisterApp accessControlRegister,
            IBlockListedIpRepository blockListedIpRepository)
			: base(campaignRepository, userRepository, transactionRepository, accessControlRegister, blockListedIpRepository)
		{
			this.accessLogRepository = accessLogRepository;
			this.accountOperatorRepository = accountOperatorRepository;
		}

		public override async Task<UserParticipantModel> Authenticate(Guid campaignId, ParticipantLoginModel participantLogin)
        {
            if (participantLogin == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            participantLogin.Validate();

            await ValidateCampaignStatus(campaignId, participantLogin);
            LoggerFactory.GetLogger().Info("Cmp {} - Autenticando operador: {}", campaignId, participantLogin.Login);

            AccountOperator accountOperator = await GetAccountOperator(campaignId, participantLogin);
            AccountOperatorLogin accountOperatorLogin = await GetAccountOperatorLogin(campaignId, participantLogin, accountOperator);

            await RegisterSuccessfulAuthenticationLog(campaignId, participantLogin, accountOperator, accountOperatorLogin);

            var accessibleAccounts = accountOperatorLogin.GetValidAccessibleAccounts();
            if (accessibleAccounts.IsNullOrEmpty())
            {
                await RegisterActionLog(campaignId, AuthenticationActionLog.INVALID_ACESSIBLE_ACCOUNT, participantLogin);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
            }

            if (accessibleAccounts.Count == 1)
            {
                return await CreateOperatorSessionUsingUser(campaignId, LogRegisterAction.AccountOperatorLoginWithPassword,
                        accessibleAccounts[0].UserId, accountOperator, accountOperatorLogin,
                        participantLogin.ConnectionInfo, participantLogin.Origin, participantLogin.Timezone,
                        participantLogin.SkipBalance);
            }

            return new UserParticipantModel()
            {
                SessionOrigin = participantLogin.Origin,
                CampaignId = campaignId,
                Name = accountOperator.Name,
                Email = accountOperatorLogin.Email,
                Timezone = participantLogin.Timezone,
                AccountOperatorId = accountOperator.Id,
                AccountOperatorLoginId = accountOperatorLogin.Id,
                AccountOperatorDocument = accountOperator.Document,
                AccountOperatorEmail = accountOperatorLogin.Email,
                AuthenticationAccess = accountOperatorLogin.AuthenticationAccess,
                AccessibleAccounts = accessibleAccounts
            };
        }

        private async Task<AccountOperatorLogin> GetAccountOperatorLogin(Guid campaignId, ParticipantLoginModel participantLogin, AccountOperator accountOperator)
        {
			var accountOperatorLogin = accountOperator.GetLoginByCampaignAndLogin(campaignId, participantLogin.Login);
			try
            {
                if (accountOperatorLogin == null || accountOperatorLogin.AccessibleAccounts.IsNullOrEmpty())
                {
                    throw new AuthenticantionException(AuthenticationActionLog.INVALID_ACCOUNT_OPERATOR_LOGIN, "Usuário e/ou senha inválido(s).");
                }
                if (!accountOperatorLogin.Active)
                {
                    throw new AuthenticantionException(AuthenticationActionLog.INACTIVE_ACCOUNT_OPERATOR_LOGIN, "Usuário e/ou senha inválido(s).");
                }

                accountOperatorLogin.ValidatePassword(participantLogin.Password);

                await VerifyIfOriginIsBlockListed(participantLogin.ConnectionInfo);
            }
            catch (AuthenticantionException ex)
            {
                await RegisterActionLog(campaignId, ex.Action, participantLogin);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (MotivaiException ex)
            {
                await RegisterActionLog(campaignId, AuthenticationActionLog.INVALID_PASSWORD, participantLogin);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            return accountOperatorLogin;
        }

        private async Task<AccountOperator> GetAccountOperator(Guid campaignId, ParticipantLoginModel participantLogin)
        {
			var accountOperator = await accountOperatorRepository.GetAccountOperatorLoginByCampaignAndLogin(campaignId, participantLogin.Login);
            if (accountOperator == null)
            {
                await RegisterActionLog(campaignId, AuthenticationActionLog.INVALID_ACCOUNT_OPERATOR_LOGIN, participantLogin);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
            }
            if (!accountOperator.Active)
            {
                await RegisterActionLog(campaignId, AuthenticationActionLog.INACTIVE_ACCOUNT_OPERATOR, participantLogin);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
            }
            return accountOperator;
        }

        public Task<UserParticipantModel> AuthenticateOperatorInAccount(Guid campaignId, OperatorAuthenticationModel operatorAuthentication)
		{
			return AuthenticateOperatorInAccount(campaignId, LogRegisterAction.AccountOperatorLoginWithUser, operatorAuthentication);
		}

		public async Task<UserParticipantModel> AuthenticateOperatorInAccount(Guid campaignId, LogRegisterAction action,
            OperatorAuthenticationModel operatorAuthentication)
		{
			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			if (!campaignSettings.Parametrizations.AllowAccountOperators)
			{
				throw MotivaiException.ofValidation("Operador de conta não está habilitado nesta campanha.");
			}

			if (operatorAuthentication == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
			operatorAuthentication.Validate();

            LoggerFactory.GetLogger().Info("Cmp {} - Autenticando operador na conta: {} - User: {}",
                    campaignId, operatorAuthentication.AccountOperatorId, operatorAuthentication.UserId);
			var accountOperator = await accountOperatorRepository.GetById(operatorAuthentication.AccountOperatorId);
			if (accountOperator == null)
			{
				throw MotivaiException.ofValidation("Operador de conta inválido.");
			}

			var accountOperatorLogin = accountOperator.GetLoginById(operatorAuthentication.AccountOperatorLoginId);
			if (accountOperatorLogin == null)
			{
				throw MotivaiException.ofValidation("Operador de conta inválido.");
			}

            await VerifyIfOriginIsBlockListed(operatorAuthentication.ConnectionInfo);

			return await CreateOperatorSessionUsingUser(campaignId, action, operatorAuthentication.UserId,
                    accountOperator, accountOperatorLogin, operatorAuthentication.ConnectionInfo,
                    operatorAuthentication.Origin, operatorAuthentication.Timezone, operatorAuthentication.SkipBalance);
		}

		private async Task<UserParticipantModel> CreateOperatorSessionUsingUser(Guid campaignId,
            LogRegisterAction action, Guid userId, AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin,
			ConnectionInfo connectionInfo, string sessionOrigin = null, string timezone = null, bool skipBalance = false)
        {
			if (!accountOperatorLogin.HasValidAccessibleAccountUserId(userId))
			{
				throw MotivaiException.ofValidation("USER_ACCOUNT_NOT_ACCESSIBLE", "Operador não tem acesso a conta.");
			}

            var user = await userRepository.GetUserForSession(userId, campaignId);
            if (user == null)
            {
                LoggerFactory.GetLogger().Error("Cmp {} - Usuário não encontrado ao buscar para a sessão: {} - User: {}",
                    campaignId, accountOperatorLogin.Id, userId);
                await RegisterInvalidUserAuthentication(campaignId, AuthenticationActionLog.INVALID_USER, user);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
            }

            await ValidateUser(campaignId, user);

            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant == null)
            {
                await RegisterInvalidParticipantAuthentication(campaignId, AuthenticationActionLog.INVALID_PARTICIPANT, participant);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
            }

            await ValidateParticipant(campaignId, participant);

            // Define se precisa carregar o saldo agora (pode ser carregado em outro momento)
            if (!skipBalance)
            {
                // TODO: Mudar forma de leitura do saldo (demorado)
                await UpdateParticipantBalance(user, participant);
            }

            var participantSession = await CreateUserParticipantModel(campaignId, user, participant, sessionOrigin, timezone);
            participantSession.SetAccountOperator(accountOperator, accountOperatorLogin);

            await accessControlRegister.RegisterAccessAndNotificate(action, participantSession, connectionInfo);

            return participantSession;
        }

        private async Task ValidateParticipant(Guid campaignId, UserParticipantCampaign participant)
        {
            try
            {
                participant.ValidateForSession();

            } catch (MotivaiException ex)
            {
                await RegisterInvalidParticipantAuthentication(campaignId, AuthenticationActionLog.INVALID_USER, participant);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
                throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
            }
        }

        private async Task ValidateUser(Guid campaignId, User user)
        {
            try
            {
                user.ValidateForSession();

            } catch (MotivaiException ex)
            {
                await RegisterInvalidUserAuthentication(campaignId, AuthenticationActionLog.INVALID_USER, user);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
                throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
            }
        }

        private async Task RegisterSuccessfulAuthenticationLog(Guid campaignId, ParticipantLoginModel participantLogin,
            AccountOperator accountOperator, AccountOperatorLogin accountOperatorLogin)
        {
            var locationInfo = participantLogin.GetLocationInfo();

            await accessLogRepository.RegisterAccountOperatorLogin(
                LogRegisterAction.AccountOperatorLoginWithPassword, Guid.Empty, campaignId, null, accountOperator.Id,
                accountOperator.Document, accountOperatorLogin.Id, locationInfo);

            var authenticationLog = CampaignAuthenticationLog.AccountOperatorLogged(
                AuthenticationActionLog.ACCOUNT_OPERATOR_LOGIN_WITH_PASSWORD,
                accountOperator, accountOperatorLogin, participantLogin.Origin, locationInfo);
            await accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }

        private Task RegisterInvalidUserAuthentication(Guid campaignId, AuthenticationActionLog action, User user)
        {
            var authenticationLog = CampaignAuthenticationLog.OfAuthenticationTried(campaignId, action, user);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }

        private Task RegisterInvalidParticipantAuthentication(Guid campaignId, AuthenticationActionLog action, UserParticipantCampaign user)
        {
            var authenticationLog = CampaignAuthenticationLog.OfAuthenticationTried(campaignId, action, user);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }
	}
}