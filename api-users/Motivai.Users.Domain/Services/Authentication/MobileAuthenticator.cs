using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Services.Authentication
{
	public class MobileAuthenticator : BaseAuthenticator
	{
		private readonly IAccessLogRepository accessLogRepository;

		public MobileAuthenticator(IUserRepository userRepository, ICampaignRepository campaignRepository,
				ITransactionApiRepository transactionRepository, IAccessLogRepository accessLogRepository,
				IAccessControlRegisterApp accessControlRegister, IBlockListedIpRepository blockListedIpRepository)
				: base(campaignRepository, userRepository, transactionRepository, accessControlRegister, blockListedIpRepository)
		{
			this.accessLogRepository = accessLogRepository;
		}

		public override async Task<UserParticipantModel> Authenticate(Guid campaignId, ParticipantLoginModel participantLogin)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			participantLogin.Validate();

			// TODO: validar se a carteira está ativa na campanha
			await ValidateCampaignStatus(campaignId, participantLogin);

			var user = await userRepository.AuthenticateByDocumentAndCampaignPassword(participantLogin.Login, campaignId, participantLogin.Password);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário e/ou senha inválido(s).");
			user.ValidateForSession();

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário e/ou senha inválido(s).");
			participant.ValidateForSession();

			// Atualiza a data do último acesso
			await UpdateLastAccess(participant);

			await accessLogRepository.RegisterLogin(LogRegisterAction.LoginFromMobile, user.Id, campaignId, user.GetDocument(), participantLogin.GetLocationInfo());

			return await CreateUserParticipantModel(campaignId, user, participant, participantLogin.Origin);
		}
	}
}