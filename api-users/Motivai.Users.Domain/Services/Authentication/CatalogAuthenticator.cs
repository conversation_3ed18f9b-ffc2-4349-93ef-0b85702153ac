using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.Security;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.ex;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Services.Authentication
{
    public class CatalogAuthenticator : BaseAuthenticator
	{
        public CatalogAuthenticator(IUserRepository userRepository, ICampaignRepository campaignRepository,
				ITransactionApiRepository transactionRepository, IAccessControlRegisterApp accessControlRegister,
                IBlockListedIpRepository blockListedIpRepository)
				: base(campaignRepository, userRepository, transactionRepository, accessControlRegister, blockListedIpRepository)
		{
        }

		public override async Task<UserParticipantModel> Authenticate(Guid campaignId, ParticipantLoginModel participantLogin)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (participantLogin == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
			participantLogin.Validate();

            LoggerFactory.GetLogger().Info("Cmp {} - Autenticando participante: {}", campaignId, participantLogin.Login);
			await ValidateCampaignStatus(campaignId, participantLogin);

			var securitySettings = await campaignRepository.GetSecuritySettings(campaignId);

			User user = await GetUserByLogin(campaignId, participantLogin, securitySettings);
			var participant = user.GetParticipantByCampaign(campaignId);

			try
			{
				if (participant == null)
					throw new AuthenticantionException(AuthenticationActionLog.INVALID_PARTICIPANT, "Usuário e/ou senha inválido(s).");
				user.ValidateForSession();
				participant.ValidateForSession();

				// Somente com limte de tentativas que será validado o login e senha
				if (securitySettings.EnableMaximumAuthenticationAttempts)
                {
                    await ValidatePasswordAndUpdateLoginAttempts(campaignId, participantLogin, securitySettings, user, participant);
                }

				await VerifyIfOriginIsBlockListed(participantLogin.ConnectionInfo);
            }
			catch (AuthenticantionException ex)
            {
                await RegisterActionLog(campaignId, ex.Action, participantLogin);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (MotivaiException)
			{
				await RegisterActionLog(campaignId, AuthenticationActionLog.INVALID_USER, participantLogin);
				throw;
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
				throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
			}

			// Define se precisa carregar o saldo agora (pode ser carregado em outro momento)
			if (!participantLogin.SkipBalance)
			{
				// TODO: Mudar forma de leitura do saldo (demorado)
				await UpdateParticipantBalance(user, participant);
			}

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			var participantSession = UserParticipantModel.Of(campaignId, user, participant, participantLogin.Origin,
					participantLogin.Timezone, campaignSettings);
			await accessControlRegister.RegisterAccessAndNotificate(LogRegisterAction.LoginWithPassword,
					participantSession, participantLogin.ConnectionInfo, campaignSettings);

			return participantSession;
		}

        private async Task ValidatePasswordAndUpdateLoginAttempts(Guid campaignId,
				ParticipantLoginModel participantLogin, CampaignSecuritySettings securitySettings,
				User user, UserParticipantCampaign participant)
        {
            if (participant.LoginAttempts.HasValue && participant.LoginAttempts >= securitySettings.MaximumAuthenticationAttemptsAllowed)
            {
                throw new AuthenticantionException(AuthenticationActionLog.BLOCKED_USER, "Usuário e/ou senha inválido(s).");
            }

            try
            {
                participant.Password.ValidarSenha(participantLogin.Password);
                await userRepository.ResetParticipantLoginAttempts(user.Id, campaignId);
            }
            catch
            {
                await VerifyAndUpdateLoginAttempts(securitySettings, user, participant);
                throw new AuthenticantionException(AuthenticationActionLog.INVALID_PASSWORD, "Usuário e/ou senha inválido(s).");
            }
        }

        private async Task<User> GetUserByLogin(Guid campaignId, ParticipantLoginModel participantLogin, CampaignSecuritySettings securitySettings)
		{
			User user;
			try
			{
				if (securitySettings.EnableMaximumAuthenticationAttempts)
				{
					user = await userRepository.GetUserByCampaignAndLogin(campaignId, participantLogin.Login);
				}
				else
				{
					user = await userRepository.AuthenticateByCampaignLoginAndPassword(campaignId, participantLogin.Login, participantLogin.Password);
				}

				if (user == null)
					throw new AuthenticantionException(AuthenticationActionLog.INVALID_USER, "Usuário e/ou senha inválido.");
			}
			catch (AuthenticantionException ex)
			{
				await RegisterActionLog(campaignId, ex.Action, participantLogin);
				throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
				throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
			}

			return user;
		}

		private async Task VerifyAndUpdateLoginAttempts(CampaignSecuritySettings securitySettings, User user, UserParticipantCampaign participant)
		{
			if (!securitySettings.EnableMaximumAuthenticationAttempts)
				return;
			try
			{
				participant.LoginAttempts = (participant.LoginAttempts ?? 0) + 1;
				var loginAttemptsExceeded = participant.LoginAttempts >= securitySettings.MaximumAuthenticationAttemptsAllowed;
				await userRepository.UpdateParticipantLoginAttempts(user.Id, participant.CampaignId, participant.LoginAttempts.Value, BlockingDetails.OfLogin("Houve um excesso de tentativas ao efetuar o login."), loginAttemptsExceeded);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Authentication - Attempts", "Erro ao atualizar as tentativas de login");
			}
		}
	}
}
