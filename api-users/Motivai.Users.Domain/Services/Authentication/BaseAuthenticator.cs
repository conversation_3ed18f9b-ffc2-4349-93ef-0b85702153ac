using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.ex;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using MongoDB.Bson;

namespace Motivai.Users.Domain.Services.Authentication
{
	public abstract class BaseAuthenticator
	{
		protected readonly ICampaignRepository campaignRepository;
		protected readonly IUserRepository userRepository;
		private readonly ITransactionApiRepository transactionRepository;
		protected readonly IAccessControlRegisterApp accessControlRegister;
        private readonly IBlockListedIpRepository blockListedIpRepository;

        public BaseAuthenticator(ICampaignRepository campaignRepository, IUserRepository userRepository,
				ITransactionApiRepository transactionRepository, IAccessControlRegisterApp accessControlRegister,
				IBlockListedIpRepository blockListedIpRepository)
		{
			this.campaignRepository = campaignRepository;
			this.userRepository = userRepository;
			this.transactionRepository = transactionRepository;
			this.accessControlRegister = accessControlRegister;
            this.blockListedIpRepository = blockListedIpRepository;
        }

		protected async Task ValidateCampaignStatus(Guid campaignId, ParticipantLoginModel participantLogin)
		{
			var campaignActive = await campaignRepository.GetActive(campaignId);
			if (!campaignActive)
			{
				await RegisterActionLog(campaignId, AuthenticationActionLog.INACTIVE_CAMPAIGN, participantLogin);
				throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "A campanha não está ativa.");
			}
		}

		protected Task RegisterActionLog(Guid campaignId, AuthenticationActionLog action, ParticipantLoginModel participantLogin)
        {
			var authenticationLog = CampaignAuthenticationLog.OfAuthenticationTried(campaignId, action, participantLogin);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }

		protected async Task UpdateLastAccess(UserParticipantCampaign participant)
		{
			participant.UpdateLastAccess();
			await userRepository.UpdateAccess(participant);
		}

		protected async Task<UserParticipantModel> CreateUserParticipantModel(Guid campaignId, User user,
				UserParticipantCampaign participant, string sessionOrigin, string timezone = null)
		{
			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			return UserParticipantModel.Of(campaignId, user, participant, sessionOrigin, timezone, campaignSettings);
		}

		public async Task UpdateParticipantBalance(User user, UserParticipantCampaign participant)
		{
			try
			{
				var balance = await transactionRepository.GetBalance(participant.CampaignId, user.Id);
				participant.Balance = balance;
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "AuthenticatorApp", "Erro ao carregar saldo.");
			}
		}

		public async Task<bool> VerifyNewPrivacyPolicyVersion(UserParticipantCampaign participant)
		{
			var currentPrivacyPolicy = await this.campaignRepository.GetCampaignActivePrivacyPolicyForUser(participant.CampaignId, participant.UserId);
			return currentPrivacyPolicy.IsNewerThan(participant.PrivacySettings);
		}

		protected async Task VerifyIfOriginIsBlockListed(ConnectionInfo connectionInfo)
		{
			if (await blockListedIpRepository.ContainsIp(connectionInfo.GetRemoteIpAddress())) {
				throw new AuthenticantionException(AuthenticationActionLog.BLOCK_LISTED_IP, "Usuário e/ou senha inválido(s).");
			}
		}

		public abstract Task<UserParticipantModel> Authenticate(Guid campaignId, ParticipantLoginModel participantLogin);
    }
}