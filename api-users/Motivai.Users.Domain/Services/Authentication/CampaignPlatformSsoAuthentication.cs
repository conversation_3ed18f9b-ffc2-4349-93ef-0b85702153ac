using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.ex;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Services.Authentication
{
    public class CampaignPlatformSsoAuthentication
    {
        private static readonly string PARTICIPANT_SSO_KEY_PREFIX = "usr-sso-gp-";
        private static readonly int SESSION_EXPIRATION = 60;

        private readonly ICache cache;
        private readonly IAccessControlRegisterApp accessControlRegister;
        private readonly IBlockListedIpRepository blockListedIpRepository;

        public CampaignPlatformSsoAuthentication(ICache cache, IAccessControlRegisterApp accessControlRegister,
            IBlockListedIpRepository blockListedIpRepository)
        {
            this.cache = cache;
            this.accessControlRegister = accessControlRegister;
            this.blockListedIpRepository = blockListedIpRepository;
        }

        public async Task<Guid> CreateSsoSessionForParticipant(UserParticipantModel participant)
        {
            var token = Guid.NewGuid();
            try
            {
                await cache.Set(PARTICIPANT_SSO_KEY_PREFIX + token, participant, SESSION_EXPIRATION);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai SSO - Start", $"Erro ao carregar cachear session pelo token {token}", true);
                throw MotivaiException.of("Não foi possível iniciar a sessão, por favor, tente novamente.", ex);
            }
            return token;
        }


        public async Task<UserParticipantModel> FinalizeAuthenticatedUserUsingSsoToken(Guid campaignId, LoginSsoEndingRequest loginSso)
        {
            if (loginSso == null)
            {
                await RegisterInvalidAuthentication(campaignId, AuthenticationActionLog.INVALID_SSO_TOKEN, loginSso);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
            }

            UserParticipantModel participantSession = await GetParticipantSession(campaignId, loginSso);
            participantSession.Timezone = loginSso.Timezone;
            await cache.Remove(PARTICIPANT_SSO_KEY_PREFIX + loginSso.Token);
            await RegisterSuccessfulSso(loginSso, participantSession);

            return participantSession;
        }

        private async Task<UserParticipantModel> GetParticipantSession(Guid campaignId, LoginSsoEndingRequest loginSso)
        {
            try
            {
                UserParticipantModel participantSession = await GetParticipationSessionByToken(loginSso.Token);
                if (participantSession == null)
                    throw new AuthenticantionException(AuthenticationActionLog.INVALID_SSO_TOKEN, "Usuário e/ou senha inválido(s).");
                if (participantSession.CampaignId != campaignId)
                    throw new AuthenticantionException(AuthenticationActionLog.INVALID_CAMPAIGN, "Sessão do usuário inválida para campanha.");
                await VerifyIfOriginIsBlockListed(loginSso.ConnectionInfo);
                return participantSession;
            }
            catch (AuthenticantionException ex)
            {
                await RegisterInvalidAuthentication(campaignId, ex.Action, loginSso);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
                throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
            }
        }

        private Task<UserParticipantModel> GetParticipationSessionByToken(Guid ssoToken)
        {
            return cache.Get<UserParticipantModel>(PARTICIPANT_SSO_KEY_PREFIX + ssoToken);
        }

        private Task RegisterInvalidAuthentication(Guid campaignId, AuthenticationActionLog action, LoginSsoEndingRequest loginSso)
        {
            var authenticationLog = CampaignAuthenticationLog.OfIntegrationEndingAuthenticationTried(campaignId, action, loginSso);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }

        private async Task RegisterSuccessfulSso(LoginSsoEndingRequest loginSso, UserParticipantModel participantSession)
        {
            // alteração da origem para registrar o início e fim no log
            var sessionOrigin = participantSession.SessionOrigin;
            participantSession.SessionOrigin = loginSso.Origin;

            await accessControlRegister.RegisterAccessLog(LogRegisterAction.PlatformSsoLoginEnded, participantSession, loginSso.ConnectionInfo);

            var authenticationLog = CampaignAuthenticationLog.OfSuccessfulAuthentication(
                AuthenticationActionLog.PLATFORM_SSO_LOGIN_ENDED, participantSession, loginSso.ConnectionInfo);
            await accessControlRegister.RegisterAuthenticationLog(authenticationLog);

            participantSession.SessionOrigin = sessionOrigin;
        }

		private async Task VerifyIfOriginIsBlockListed(ConnectionInfo connectionInfo)
		{
			if (await blockListedIpRepository.ContainsIp(connectionInfo.GetRemoteIpAddress())) {
				throw new AuthenticantionException(AuthenticationActionLog.BLOCK_LISTED_IP, "Usuário e/ou senha inválido(s).");
			}
		}
    }
}