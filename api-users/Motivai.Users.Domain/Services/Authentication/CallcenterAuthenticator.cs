using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.ex;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Services.Authentication
{
	public class CallcenterAuthenticator
	{
		private readonly IUserRepository userRepository;
		private readonly ICampaignRepository campaignRepository;
		private readonly IAccessLogRepository accessLogRepository;
		private readonly IAuthenticationLogRepository authenticationLogRepository;
		private readonly CampaignPlatformSsoAuthentication ssoAuthentication;
		private readonly ITransactionApiRepository transactionRepository;
        private readonly IAccessControlRegisterApp accessControlRegister;
        private readonly IBlockListedIpRepository blockListedIpRepository;

        public CallcenterAuthenticator(IUserRepository userRepository, ICampaignRepository campaignRepository,
				IAccessLogRepository accessLogRepository, IAuthenticationLogRepository authenticationLogRepository,
				CampaignPlatformSsoAuthentication ssoAuthentication, ITransactionApiRepository transactionRepository,
				IAccessControlRegisterApp accessControlRegister, IBlockListedIpRepository blockListedIpRepository)
		{
			this.userRepository = userRepository;
			this.campaignRepository = campaignRepository;
			this.accessLogRepository = accessLogRepository;
			this.authenticationLogRepository = authenticationLogRepository;
			this.ssoAuthentication = ssoAuthentication;
			this.transactionRepository = transactionRepository;
            this.accessControlRegister = accessControlRegister;
            this.blockListedIpRepository = blockListedIpRepository;
        }

		public async Task<Guid> AuthenticateParticipantForCallcenterSso(Guid campaignId, CallcenterLogin login)
		{
			var participant = await AutheticateParticipantForCallcenter(campaignId, login);
			return await ssoAuthentication.CreateSsoSessionForParticipant(participant);
		}

		public async Task<UserParticipantModel> AutheticateParticipantForCallcenter(Guid campaignId, CallcenterLogin login)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (login == null)
				throw MotivaiException.ofValidation("Informe os dados para autenticação.");
			login.Validate();

            LoggerFactory.GetLogger().Info("Cmp {} - Autenticando callcenter: {} - {} - User: {}",
					campaignId, login.CallcenterUserId, login.CallcenterUsername, login.UserId);

			try
            {
                var user = await userRepository.Get(login.UserId);
                if (user == null)
                    throw MotivaiException.ofValidation("Participante não encontrado.");
                user.ValidateForSession();

                var participant = user.GetParticipantByCampaign(campaignId);
                if (participant == null)
                    throw MotivaiException.ofValidation("Participante não encontrado.");
                participant.ValidateForSession();

                await VerifyIfOriginIsBlockListed(login.ConnectionInfo);

                await UpdateParticipantBalance(user, participant);

                var participantSession = await CreateUserSession(campaignId, user, participant, login);

                await RegisterLogs(login, participantSession);
                return participantSession;
            }
            catch (AuthenticantionException ex)
            {
                await RegisterActionLog(campaignId, ex.Action, login);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (MotivaiException)
			{
				await RegisterActionLog(campaignId, AuthenticationActionLog.INVALID_USER, login);
				throw;
			}
		}

        private async Task<UserParticipantModel> CreateUserSession(Guid campaignId, User user, UserParticipantCampaign participant,
				CallcenterLogin login, CampaignSettingsModel campaignSettings = null)
		{
			if (campaignSettings == null)
			{
				campaignSettings = await campaignRepository.GetSettings(campaignId);
			}

			var session = UserParticipantModel.Of(campaignId, user, participant, login.Origin, login.Timezone, campaignSettings);
			session.FirstAccess = false;
			session.NeedSetupAuthenticationMfa = false;
			return session;
		}

		private async Task UpdateParticipantBalance(User user, UserParticipantCampaign participant)
		{
			try
			{
				var balance = await transactionRepository.GetBalance(participant.CampaignId, user.Id);
				participant.Balance = balance;
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "AuthenticatorApp", "Erro ao carregar saldo.");
			}
		}

		protected async Task VerifyIfOriginIsBlockListed(ConnectionInfo connectionInfo)
		{
			if (await blockListedIpRepository.ContainsIp(connectionInfo.GetRemoteIpAddress())) {
				throw new AuthenticantionException(AuthenticationActionLog.BLOCK_LISTED_IP, "Usuário e/ou senha inválido(s).");
			}
		}

        private async Task RegisterLogs(CallcenterLogin callcenterLogin, UserParticipantModel participantSession)
        {
            await accessLogRepository.RegisterLoginFromCallcenter(callcenterLogin, participantSession, callcenterLogin.GetLocationInfo());

            await authenticationLogRepository.RegisterLoginFromCallcenter(callcenterLogin, participantSession, callcenterLogin.GetLocationInfo());
        }

		protected Task RegisterActionLog(Guid campaignId, AuthenticationActionLog action, CallcenterLogin participantLogin)
        {
			var authenticationLog = CampaignAuthenticationLog.OfCallcenterAuthenticationTried(campaignId, action, participantLogin);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }
	}
}