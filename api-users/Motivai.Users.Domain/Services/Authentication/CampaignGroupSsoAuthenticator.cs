using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.Integrations.CampaignsGroup;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.Services.Authentication
{
	public class CampaignGroupSsoAuthenticator
	{
		private readonly IUserRepository userRepository;
		private readonly ICampaignRepository campaignRepository;
		private readonly IAccountOperatorRepository accountOperatorRepository;
		private readonly ITransactionApiRepository transactionRepository;
		private readonly CampaignPlatformSsoAuthentication platformSsoAuthenticator;
		protected readonly IAccessControlRegisterApp accessControlRegister;

		public CampaignGroupSsoAuthenticator(IUserRepository userRepository, ICampaignRepository campaignRepository,
			IAccountOperatorRepository accountOperatorRepository, ITransactionApiRepository transactionRepository,
			CampaignPlatformSsoAuthentication platformSsoAuthenticator, IAccessControlRegisterApp accessControlRegister)
		{
			this.userRepository = userRepository;
			this.campaignRepository = campaignRepository;
			this.accountOperatorRepository = accountOperatorRepository;
			this.transactionRepository = transactionRepository;
			this.platformSsoAuthenticator = platformSsoAuthenticator;
			this.accessControlRegister = accessControlRegister;
		}

		public async Task<CampaignGroupSsoResult> AuthenticateAtTargetGroupCampaign(Guid campaignId, CampaignGroupSsoRequest ssoRequest)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (ssoRequest == null || ssoRequest.UserId == Guid.Empty)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

			var targetCampaignUrl = await campaignRepository.GetCampaignGroupTargetAccessUrl(campaignId, ssoRequest.TargetCampaignId);
			var participantSession = await CreateParticipantSessionForTargetCampaign(campaignId, ssoRequest);
			var token = await platformSsoAuthenticator.CreateSsoSessionForParticipant(participantSession);
			return CampaignGroupSsoResult.OfUrlAndToken(targetCampaignUrl, token);
		}

		private async Task<UserParticipantModel> CreateParticipantSessionForTargetCampaign(Guid campaignId, CampaignGroupSsoRequest ssoRequest)
		{
			var targetCampaignSettings = await campaignRepository.GetSettings(ssoRequest.TargetCampaignId);
			// buscar usuário na campanha de destino
			var user = await userRepository.GetUserAndParticipant(ssoRequest.UserId, ssoRequest.TargetCampaignId);
			if (user == null)
			{
				user = await CreateParticipantAtTargetCampaign(ssoRequest.UserId, campaignId,
					ssoRequest.TargetCampaignId, targetCampaignSettings);
			}
			if (user == null)
			{
				await RegisterInvalidUserAuthentication(campaignId, AuthenticationActionLog.INVALID_USER, user);
				throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", "Usuário e/ou senha inválido(s).");
			}

			var participant = user.GetParticipantByCampaign(ssoRequest.TargetCampaignId);
			await UpdateParticipantBalance(user, participant);
			var participantSession = UserParticipantModel.Of(ssoRequest.TargetCampaignId, user, participant,
					ssoRequest.Origin, ssoRequest.Timezone, targetCampaignSettings);

			if (ssoRequest.HasAccountOperator())
			{
				await UpdateSessionWithAccountOperator(campaignId, ssoRequest, user, participantSession);
			}
			return participantSession;
		}

		private async Task UpdateSessionWithAccountOperator(Guid campaignId, CampaignGroupSsoRequest ssoRequest, User user, UserParticipantModel participantSession)
		{
			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Operator {2} - Logando com operador na campanha {3}",
				campaignId, user.Id, ssoRequest.AccountOperatorId, ssoRequest.TargetCampaignId);

			var accountOperator = await accountOperatorRepository.GetById(ssoRequest.AccountOperatorId.Value);

			AccountOperatorLogin targetSourceOperatorLogin;
			if (accountOperator.HasLoginInCampaign(ssoRequest.TargetCampaignId))
			{
				targetSourceOperatorLogin = accountOperator.GetLoginByCampaign(ssoRequest.TargetCampaignId);
			}
			else
			{
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Operator {2} - Criando novo login na campanha {3}",
					campaignId, user.Id, ssoRequest.AccountOperatorId, ssoRequest.TargetCampaignId);
				var sourceAccountOperatorLogin = accountOperator.GetLoginById(ssoRequest.AccountOperatorLoginId.Value);
				targetSourceOperatorLogin = sourceAccountOperatorLogin.CloneToCampaign(ssoRequest.TargetCampaignId);
				var updated = await accountOperatorRepository.InsertAccountOperatorLogin(accountOperator.Id, targetSourceOperatorLogin);
				if (!updated)
					throw MotivaiException.ofValidation("Não foi possível atualizar o operador de conta.");
			}
			participantSession.SetAccountOperatorForCampaignGroup(accountOperator, targetSourceOperatorLogin);
		}

		private async Task<User> CreateParticipantAtTargetCampaign(Guid userId, Guid sourceCampaignId,
			Guid targetCampaignId, CampaignSettingsModel targetCampaignSettings)
		{
			var user = await userRepository.GetUserAndParticipant(userId, sourceCampaignId);
			if (user == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");
			var sourceParticipant = user.GetParticipantByCampaign(sourceCampaignId);

			var targetParticipant = sourceParticipant.CloneForCampaign(targetCampaignId, targetCampaignSettings);

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Copiando participante para campanha {2}",
				sourceCampaignId, userId, targetCampaignId);
			bool created = await userRepository.CreateParticipant(user.Id, targetParticipant);
			if (!created)
				throw MotivaiException.ofValidation("Não foi possível criar o participante.");

			user = await userRepository.GetUserAndParticipant(userId, targetCampaignId);
			return user;
		}

		public async Task UpdateParticipantBalance(User user, UserParticipantCampaign participant)
		{
			try
			{
				var balance = await transactionRepository.GetBalance(participant.CampaignId, user.Id);
				participant.Balance = balance;
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "AuthenticatorApp", "Erro ao carregar saldo.");
			}
		}

		private Task RegisterInvalidUserAuthentication(Guid campaignId, AuthenticationActionLog action, User user)
        {
            var authenticationLog = CampaignAuthenticationLog.OfAuthenticationTried(campaignId, action, user);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }
	}
}
