using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.Services.Participants.Creation;
using Motivai.Users.Domain.Services.Users.Creation;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.ex;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.IRepository.Security;

namespace Motivai.Users.Domain.Services.Authentication
{
    public class CampaignIntegrationAuthenticator
	{
		private readonly ICampaignRepository _campaignRepository;
		private readonly CampaignPlatformSsoAuthentication CampaignPlatformSsoAuthentication;
		private readonly CampaignIntegrationParticipantCreator campaignIntegrationParticipantCreator;
		private readonly CampaignIntegrationUserCreator campaignIntegrationUserCreator;
		private readonly IAccessControlRegisterApp accessControlRegister;
        private readonly IBlockListedIpRepository blockListedIpRepository;

        public CampaignIntegrationAuthenticator(ICampaignRepository campaignApiRepository,
			CampaignPlatformSsoAuthentication CampaignPlatformSsoAuthentication,
			CampaignIntegrationParticipantCreator campaignIntegrationParticipantCreator,
			CampaignIntegrationUserCreator campaignIntegrationUserCreator,
			IAccessControlRegisterApp accessControlRegister,
			IBlockListedIpRepository blockListedIpRepository
		)
		{
			this._campaignRepository = campaignApiRepository;
			this.CampaignPlatformSsoAuthentication = CampaignPlatformSsoAuthentication;
			this.campaignIntegrationParticipantCreator = campaignIntegrationParticipantCreator;
			this.campaignIntegrationUserCreator = campaignIntegrationUserCreator;
			this.accessControlRegister = accessControlRegister;
            this.blockListedIpRepository = blockListedIpRepository;
        }

        public Task<UserParticipantModel> AuthenticateParticipantByIntegration(Guid campaignId, ParticipantIntegrationData login)
		{
			return AuthenticateParticipantByIntegration(campaignId, login, LogRegisterAction.LoginWithIntegration);
		}

		public async Task<Guid> AuthenticateByPlatformSso(Guid campaignId, ParticipantIntegrationData login)
		{
			var participant = await AuthenticateParticipantByIntegration(campaignId, login, LogRegisterAction.PlatformSsoLoginStarted);
			return await CampaignPlatformSsoAuthentication.CreateSsoSessionForParticipant(participant);
		}

		public Task<UserParticipantModel> FinalizeAuthenticatedUserUsingSsoToken(Guid campaignId, LoginSsoEndingRequest loginSso)
		{
			return CampaignPlatformSsoAuthentication.FinalizeAuthenticatedUserUsingSsoToken(campaignId, loginSso);
		}

		private async Task<UserParticipantModel> AuthenticateParticipantByIntegration(Guid campaignId,
			ParticipantIntegrationData participantIntegrationData, LogRegisterAction action)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (participantIntegrationData == null)
				throw MotivaiException.ofValidation("Dados de autenticação são obrigatórios.");

			LoggerFactory.GetLogger().Info("Cmp {} - Autenticando integração - Doc: {} - Login: {}",
				campaignId, participantIntegrationData.Document, participantIntegrationData.Login);


			var campaignSettings = await _campaignRepository.GetSettings(campaignId);
			try
			{
				await ValidateCampaign(campaignId, campaignSettings);
				ValidateIntegrationToken(campaignSettings, participantIntegrationData);
				await VerifyIfOriginIsBlockListed(participantIntegrationData.ConnectionInfo);
                await ValidateParticipantIntegrationData(campaignId, action, participantIntegrationData);
			}
			catch (AuthenticantionException ex)
			{
				LoggerFactory.GetLogger().Info("Cmp {} - Autenticando integração - Doc: {} - Login: {} - Erro: {}",
					campaignId, participantIntegrationData.Document, participantIntegrationData.Login, ex.Message);
				await RegisterSsoActionLog(campaignId, action, ex.Action, participantIntegrationData);
				throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
			}
			catch (MotivaiException ex)
			{
				LoggerFactory.GetLogger().Info("Cmp {} - Autenticando integração - Doc: {} - Login: {} - Erro: {}",
					campaignId, participantIntegrationData.Document, participantIntegrationData.Login, ex.Message);
				await RegisterSsoActionLog(campaignId, action, AuthenticationActionLog.INVALID_USER, participantIntegrationData);
				throw;
			}

			User user = await this.campaignIntegrationUserCreator.FindOrCreateUser(campaignId, campaignSettings, participantIntegrationData);
			UserParticipantCampaign participant = await GetOrCreateParticipant(campaignId, action, campaignSettings,
					user, participantIntegrationData);

			if (participantIntegrationData.Operator != null)
			{
				await campaignIntegrationParticipantCreator.CreateAccountOperatorIfNeeded(campaignId, campaignSettings, user.Id, participantIntegrationData.Operator);
			}

			await this.campaignIntegrationParticipantCreator.UpdateParticipantBalance(user, participant);

			var participantSession = UserParticipantModel.Of(campaignId, user, participant,
					participantIntegrationData.Origin, participantIntegrationData.Timezone, campaignSettings);

			if (campaignSettings.Parametrizations.AllowAccountOperators && participantIntegrationData.Operator != null)
			{
				var accountOperator = await campaignIntegrationParticipantCreator.GetOperatorBy(user.Id, campaignId, participantIntegrationData.Operator.Document);
				if (accountOperator != null)
				{
					participantSession.SetAccountOperator(accountOperator);
				}
			}

			await accessControlRegister.RegisterAccessAndNotificate(action, participantSession, participantIntegrationData.ConnectionInfo, campaignSettings);

			return participantSession;
		}

		private async Task ValidateCampaign(Guid campaignId, CampaignSettingsModel campaignSettings)
		{
			var campaignActive = await _campaignRepository.GetActive(campaignId);
			if (!campaignActive)
				throw new AuthenticantionException(AuthenticationActionLog.INVALID_CAMPAIGN, "Campanha não está ativa.");

            if (!campaignSettings.Parametrizations.EnableLoginIntegration)
				throw new AuthenticantionException(AuthenticationActionLog.INVALID_CAMPAIGN_SETTINGS, "Campanha não permite login integrado");
		}

        private async Task<UserParticipantCampaign> GetOrCreateParticipant(Guid campaignId, LogRegisterAction action,
				CampaignSettingsModel campaignSettings, User user, ParticipantIntegrationData participantIntegrationData)
        {
            var participant = user.GetParticipantByCampaign(campaignId);

            if (participant == null)
            {
				participant = await this.campaignIntegrationParticipantCreator.CreateParticipantFrom(campaignId, campaignSettings, user, participantIntegrationData);
            }
			else if (participantIntegrationData.ShouldValidateLogin())
			{
                participantIntegrationData.EnsureSameLogin(participant.Login);
			}

			try
			{
            	participant.ValidateForSession();
			}
			catch (MotivaiException ex)
            {
                await RegisterSsoActionLog(campaignId, action, AuthencationActionLogHelper.FromErrorCode(ex.ErrorCode), participantIntegrationData);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }

            return participant;
        }

        private async Task ValidateParticipantIntegrationData(Guid campaignId, LogRegisterAction action,
				ParticipantIntegrationData participantIntegrationData)
        {
			try
			{
            	participantIntegrationData.Validate();

				if (string.IsNullOrEmpty(participantIntegrationData.Origin))
				{
					participantIntegrationData.Origin = "Login Integrado";
				}
			}
			catch (MotivaiException ex)
            {
                await RegisterSsoActionLog(campaignId, action, AuthenticationActionLog.INVALID_PARTICIPANT, participantIntegrationData);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
                throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
            }
        }

        private void ValidateIntegrationToken(CampaignSettingsModel campaignSettings, ParticipantIntegrationData participantIntegrationData)
        {
			if (string.IsNullOrEmpty(participantIntegrationData.Token) || campaignSettings.Parametrizations.Token != participantIntegrationData.Token)
			{
				throw new AuthenticantionException(AuthenticationActionLog.INVALID_CAMPAIGN_TOKEN, "Usuário e/ou senha inválido(s).");
			}
        }

		private Task RegisterSsoActionLog(Guid campaignId, LogRegisterAction logAction,
				AuthenticationActionLog authenticationAction, ParticipantIntegrationData participantIntegrationData)
        {
			var authenticationLog = CampaignAuthenticationLog.OfIntegrationAuthenticationTried(campaignId, logAction,
					authenticationAction, participantIntegrationData);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }

		private async Task VerifyIfOriginIsBlockListed(ConnectionInfo connectionInfo)
		{
			if (await blockListedIpRepository.ContainsIp(connectionInfo.GetRemoteIpAddress())) {
				throw new AuthenticantionException(AuthenticationActionLog.BLOCK_LISTED_IP, "Usuário e/ou senha inválido(s).");
			}
		}
	}
}