using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.Privacy;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.PrivacyPolicy;

namespace Motivai.Users.Domain.Services.Security
{
	public class ParticipantPrivacySettingsManager
	{
		private readonly IUserRepository userRepository;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;

		public ParticipantPrivacySettingsManager(IUserRepository userRepository,
				IParticipantRegistrationLogRepository participantRegistrationLogRepository)
		{
			this.userRepository = userRepository;
			this.participantRegistrationLogRepository = participantRegistrationLogRepository;
		}

		public async Task RegisterPrivacySettingsChange(Guid userId, Guid campaignId,
			PrivacySettings privacySettings, LocationInfo locationInfo = null)
		{
			LoggerFactory.GetLogger().Info("Usr {} - Cmp {} - Registrando aceite da política de privacidade",
					userId, campaignId);
			await userRepository.SetPrivacySetttings(userId, campaignId, privacySettings);

			LoggerFactory.GetLogger().Info("Usr {} - Cmp {} - Registrando log do aceite da política de privacidade",
					userId, campaignId);
			var log = ParticipantRegistrationLog.OfPrivacySettings(userId, campaignId,
				privacySettings, null, locationInfo);
			await participantRegistrationLogRepository.RegisterLog(log);
		}

		public async Task<bool> RegisterPrivacySettingsChange(Guid userId, Guid campaignId,
			PrivacyPolicyChangeModel privacyPolicyChange)
		{
			LoggerFactory.GetLogger().Info("Usr {} - Cmp {} - Registrando aceite da política de privacidade",
					userId, campaignId);
			try {
				await userRepository.UpdatePrivacySettings(userId, campaignId, privacyPolicyChange.PrivacyPolicyResult);

				LoggerFactory.GetLogger().Info("Usr {} - Cmp {} - Registrando log do aceite da política de privacidade",
						userId, campaignId);
				var log = ParticipantRegistrationLog.OfPrivacySettingsChange(userId, campaignId,
					privacyPolicyChange.PrivacyPolicyResult, privacyPolicyChange.AccountOperator,
					privacyPolicyChange.LocationInfo);
				await participantRegistrationLogRepository.RegisterLog(log);
				return true;
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Account - Política Privacidade", "Erro ao registrar o aceite da política de privacidade");
				throw;
			}
		}
	}
}