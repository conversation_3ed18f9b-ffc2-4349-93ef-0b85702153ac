using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Sms;
using Motivai.Users.Domain.Models.Security;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;

namespace Motivai.Users.Domain.Services.Security {
    public class SecurityCodeSender {
        private static readonly Random RANDOM = new Random();

        private readonly IUserParticipationCampaignRepository participantRepository;
        private readonly IEmailRepository emailRepository;

        public SecurityCodeSender(IUserParticipationCampaignRepository participantRepository, IEmailRepository emailRepository) {
            this.participantRepository = participantRepository;
            this.emailRepository = emailRepository;
        }

        public string GenerateCode() {
            return RANDOM.Next(100000, 999999).ToString();
        }

        public async Task SendSecurityCodeByMethod(Guid userId, Guid campaignId, SecurityCodeSendMethod sendMethod, string securityCode) {
            var contactInfo = await participantRepository.GetMainContactInfo(userId, campaignId);

            if (sendMethod == SecurityCodeSendMethod.EMAIL && string.IsNullOrEmpty(contactInfo.Email)) {
                throw MotivaiException.of(ErrorCodes.NOTIFICATION_EMAIL_REQUIRED, "Nenhum e-mail cadastrado para receber o código de segurança.");
            } else if (sendMethod == SecurityCodeSendMethod.SMS && string.IsNullOrEmpty(contactInfo.MobilePhone)) {
                throw MotivaiException.of(ErrorCodes.NOTIFICATION_MOBILEPHONE_REQUIRED, "Nenhum celular cadastrado para receber o código de segurança.");
            }

            var sent = await emailRepository.SendSecurityCodeByMethod(userId, campaignId, contactInfo.Name, contactInfo.Email, contactInfo.MobilePhone, sendMethod, securityCode);
            if (!sent) {
                throw MotivaiException.ofValidation("Não foi possível enviar o código de segurança, por favor, tente novamente.");
            }
        }
    }
}