using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.Models.Import;

namespace Motivai.Users.Domain.Services.Notifications
{
	public class ParticipantNotificator
	{
		private readonly IEmailRepository emailRepository;

		public ParticipantNotificator(IEmailRepository emailRepository)
		{
			this.emailRepository = emailRepository;
		}

		public async Task SendNotification(CreatedParticipant createdParticipant)
		{
			if (string.IsNullOrEmpty(createdParticipant.Email))
			{
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Participante sem email", createdParticipant.CampaignId, createdParticipant.UserId);
				return;
			}

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Notificando participante", createdParticipant.CampaignId, createdParticipant.UserId);

			if (createdParticipant.HasEvent())
			{
				await SendNotificationByEvent(
					createdParticipant.CampaignId, createdParticipant.EventType.Value, createdParticipant.UserId,
					createdParticipant.Name, createdParticipant.Email, createdParticipant.MobilePhone,
					createdParticipant.Login, createdParticipant.Password, createdParticipant.Points
				);
			}
			else
			{
				await SendNotification(
					createdParticipant.CampaignId, createdParticipant.UserId,
					createdParticipant.Name, createdParticipant.Email, createdParticipant.MobilePhone,
					createdParticipant.Login, createdParticipant.Password
				);
			}
		}

		private async Task SendNotification(Guid campaignId, Guid userId, string userName, string userEmail, string userMobilePhone, string login, string password)
		{
			try
			{
				await emailRepository.SendNewParticipantNotification(campaignId, userId, userName, userEmail, userMobilePhone, login, password);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "ParticipantNotificator - Notification", "Não foi possível enviar o e-mail de novo participante.");
				LoggerFactory.GetLogger().Error("ParticipantNotificator - Cmp {0} - Usr {1} - Email {2} - Erro ao enviar notificação: {3}",
					campaignId, login, userEmail, userMobilePhone, ex.Message);
			}
		}

		private async Task SendNotificationByEvent(Guid campaignId, CampaignEventType eventType, Guid userId,
				string userName, string userEmail, string userMobilePhone, string login, string password, decimal? points = 0)
		{
			try
			{
				await emailRepository.SendNewParticipantNotificationByEvent(campaignId, eventType, userId, userName,
					userEmail, userMobilePhone, login, password, points ?? 0m);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "ParticipantNotificator - Notification", "Não foi possível enviar o e-mail de novo participante.");
				LoggerFactory.GetLogger().Error("ParticipantNotificator - Cmp {0} - Usr {1} - Email {2} - Erro ao enviar notificação: {3}",
					campaignId, login, userEmail, ex.Message);
			}
		}
	}
}