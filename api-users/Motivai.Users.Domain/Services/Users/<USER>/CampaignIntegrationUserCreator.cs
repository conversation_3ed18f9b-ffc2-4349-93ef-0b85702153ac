using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.IApp.Searches;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Services.Participants.Creation;

namespace Motivai.Users.Domain.Services.Users.Creation
{
    public class CampaignIntegrationUserCreator
    {
        private readonly IUserRepository userRepository;
        private readonly IParticipantSearchApp participantSearchApp;
        private readonly CampaignIntegrationParticipantCreator participantCreator;

        public CampaignIntegrationUserCreator(
            IUserRepository userRepository,
            IParticipantSearchApp participantSearchApp,
            CampaignIntegrationParticipantCreator participantCreator
        )
        {
            this.userRepository = userRepository;
            this.participantSearchApp = participantSearchApp;
            this.participantCreator = participantCreator;
        }

        public async Task<User> FindOrCreateUser(Guid campaignId, CampaignSettingsModel campaignSettings, ParticipantIntegrationData participantIntegrationData)
        {
            if (participantIntegrationData.SkipDocumentValidation)
            {
                LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Login {2} - Buscando pelo login",
                        campaignId, participantIntegrationData.Document, participantIntegrationData.Login);
                var user = await userRepository.GetByLogin(campaignId, participantIntegrationData.Login, false);
                if (user != null)
                {
                    if (!string.IsNullOrEmpty(user.GetDocument()) && participantIntegrationData.ShouldValidateDocument())
                    {
                        // TODO: efetuar migração do CPF/CNPJ caso não exista outro participante
                        participantIntegrationData.EnsureSameDocument(user.GetDocument());
                    }
                    return user;
                }
            }
            else
            {
                LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Login {2} - Buscando pelo CPF/CNPJ",
                        campaignId, participantIntegrationData.Document, participantIntegrationData.Login);
                var user = await participantSearchApp.FindParticipantByDocument(participantIntegrationData.Document, campaignId);
                if (user != null)
                    return user;
            }

            return await CreateUserFrom(campaignId, campaignSettings.Type, participantIntegrationData);
        }

        public async Task<User> CreateUserFrom(Guid campaignId, CampaignType campaignType, ParticipantIntegrationData participantIntegrationData)
        {
            User user;
            if (participantIntegrationData.SkipDocumentValidation)
            {
                LoggerFactory.GetLogger().Info("Cmp {0} - Login {1} - Criando user sem validar documento",
                        campaignId, participantIntegrationData.Login);
                user = User.CreateUser(campaignType);
                if (!string.IsNullOrEmpty(participantIntegrationData.Document))
                {
                    user.Type = PersonTypeHelper.FromDocument(participantIntegrationData.Document);
                    user.Document = participantIntegrationData.Document;
                    if (user.Type == PersonType.Fisica)
                    {
                        user.Cpf = participantIntegrationData.Document;
                    }
                    else
                    {
                        user.Cnpj = participantIntegrationData.Document;
                    }
                }
            }
            else
            {
                LoggerFactory.GetLogger().Info("Cmp {0} - Doc {1} - Criando user",
                        campaignId, participantIntegrationData.Document);
                await participantCreator.VerifyIfLoginAlreadyInUse(campaignId, participantIntegrationData.Document, participantIntegrationData.Login);
                user = User.CreateUser(campaignType, participantIntegrationData.Document);
            }

            if (user.Type == PersonType.Juridica)
            {
                user.Name = participantIntegrationData.Name;
                if (string.IsNullOrEmpty(participantIntegrationData.CompanyName))
                {
                    user.CompanyName = participantIntegrationData.Name;
                }
                else
                {
                    user.CompanyName = participantIntegrationData.CompanyName;
                }
                user.StateInscription = participantIntegrationData.StateInscription;
                user.StateInscriptionUf = participantIntegrationData.StateInscriptionUf;
                if (participantIntegrationData.StateInscriptionExempt.HasValue)
                {
                    user.StateInscriptionExempt = participantIntegrationData.StateInscriptionExempt.Value;
                    if (user.StateInscriptionExempt)
                    {
                        user.StateInscription = null;
                        user.StateInscriptionUf = null;
                    }
                }
            }
            else
            {
                user.Name = participantIntegrationData.Name;
                user.Rg = participantIntegrationData.Rg;
                user.Gender = participantIntegrationData.Gender;
                if (participantIntegrationData.BirthDate.HasValue)
                {
                    user.BirthDate = participantIntegrationData.BirthDate.Value;
                }
            }

            await this.userRepository.CreateUser(user, participantIntegrationData.SkipDocumentValidation);

            return user;
        }
    }
}
