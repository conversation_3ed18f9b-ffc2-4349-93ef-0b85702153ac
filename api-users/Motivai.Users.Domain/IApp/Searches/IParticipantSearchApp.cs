using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IApp.Searches
{
	public interface IParticipantSearchApp
	{
		Task<User> FindParticipantByDocument(string document);
		Task<User> FindParticipantByDocument(string document, Guid? campaignId);
		Task<List<ParticipantInfo>> SearchParticipantsIncampaign(Guid campaignId, ParticipantSearchField searchByField, string searchValue);
	}
}