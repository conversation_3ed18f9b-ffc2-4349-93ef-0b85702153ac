using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.Wallets.Devices;
using Motivai.Users.Domain.Models.Security;

namespace Motivai.Users.Domain.IApp.Users {
    public interface IUserDeviceManagerApp {
        Task<bool> AuthorizeDevice(Guid userId, DeviceAuthorizationRequest deviceAuthorizationRequest);

        Task<bool> GetById(Guid userId, string deviceId);

        Task<bool> GetActive(Guid userId, string deviceId);

        Task<bool> VerifyDeviceAuthorization(Guid userId, DeviceRequestInfo deviceRequestInfo);
        Task<bool> SendAuthorizationCode(Guid userId, SecurityCodeRequest securityCodeRequest);
        Task<bool> ValidateAuthorizationCode(Guid userId, SecurityCodeValidation codeValidation);
    }
}