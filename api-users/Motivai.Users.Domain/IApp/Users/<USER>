﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.Import;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IApp.Users
{
	public interface IUserApp
	{
		Task<User> GetUserByCpf(string cpf);
		Task<bool> IsUserActiveAtCampaign(Guid userId, Guid campaignId);
		Task<bool> IsParticipantActiveAtCampaign(Guid participantId, Guid campaignId);
		Task<Guid> GetParticipantIdIfActive(Guid userId, Guid campaignId);
		Task<UserParticipantCampaign> GetParticipantByUserAndCampaign(Guid userId, Guid campaignId);
		Task<List<UserCampaignsInfo>> SearchParticipantInBusinessType(UserBusinessType businessType, string document, string name);
		Task<ParticipantInfo> GetUserInfoByDocument(string document);
		Task<ParticipantInfo> GetParticipantInfo(Guid userId, Guid campaignId);
		Task<ParticipantInfo> GetParticipantBillingInfo(Guid userId, Guid campaignId);
		Task<dynamic> GetParticipantClientData(Guid userId, Guid campaignId);
		Task<ParticipantInfo> GetUserData(Guid userId, Guid campaignId);
		Task<dynamic> GetDocumentBy(Guid userId);
		Task<List<UserCampaignsInfo>> GetUsersByListOfIds(List<Guid> usersIds);

		Task<UserBasicInfo> GetUserBasicInfo(Guid campaignId, string document);
		Task<dynamic> SearchPersonByDocument(string document, Guid campaignId = default(Guid));
		Task<AccountRepresentative> GetAccountRepresentative(Guid userId, Guid campaignId);
		Task<bool> ExistUserWithDocument(Guid userId, string document);
		Task<bool> SaveUserAppCard(Guid campaignId, Guid userId, Card card);
		Task<List<Card>> GetAppCards(Guid campaignId, Guid userId);
		Task<List<Card>> GetActiveAppCards(Guid campaignId, Guid userId);
		Task<bool> ResetAppCards(Guid campaignId, Guid userId);
		Task<List<UserCampaignsInfo>> GetUserChildrenParticipants(Guid userId, Guid campaignId);
	}
}