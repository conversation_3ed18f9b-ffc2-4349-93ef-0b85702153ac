using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Participants;

namespace Motivai.Users.Domain.IApp.Users {
	public interface IUserParentFinder {
		Task<List<UserParentDetails>> GetUsersParentsByIds(Guid campaignId, List<Guid> usersIds);
		Task<bool> VerifyIfUsersHasSameParentHierarchy(Guid campaignId, Guid firstUser, Guid secondUser);
		Task<List<UserBasicInfo>> GetUsersWithSameParentHierarchy(Guid userId, Guid campaignId);
		Task<UserBasicInfo> GetParentByUserId(Guid userId, Guid campaignId);
	}
}