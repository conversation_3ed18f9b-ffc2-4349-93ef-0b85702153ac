using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IApp.Users {
    public interface IUserExternalClientApp {
        Task<decimal> GetBalanceByUserId(Guid campaignId, string document);
        Task<bool> CreateParticipant(Guid campaignId, UserExternalModel userExternalModel);
        Task<bool> UpdateParticipant(Guid campaignId, string document, UserExternalModel userExternalModel);
        Task<bool> BlockParticipant(Guid campaignId, string document);
    }
}