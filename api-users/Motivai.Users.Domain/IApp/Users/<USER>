using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IApp.Users {
	public interface IUserMetadataApp {
		Task<UserMetadataValue> GetUsersMetadataValue(Guid campaignId, Guid userId);
		Task<string> GetUserMetadataFieldValue(Guid userId, Guid campaignId, string fieldKey);
		Task<bool> SaveUserMetadata(Guid userId, Guid campaignId, Guid participantId, Dictionary<string, string> userMetadata, string origin = null);
	}
}