using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IApp.Authenticator
{
	public interface IAccessControlRegisterApp
	{
		Task RegisterAccessLog(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo);
		Task RegisterAuthenticationLog(CampaignAuthenticationLog authenticationLog);
		Task RegisterAccessAndNotificate(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo);
		Task RegisterAccessAndNotificate(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo,
			CampaignSettingsModel campaignSettings);
    }
}