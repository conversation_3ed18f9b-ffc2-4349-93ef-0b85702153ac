using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.AccountOperators.Authentication;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Entities.Integrations.CampaignsGroup;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Models.Security;

namespace Motivai.Users.Domain.IApp.Ath
{
	///<summary>
	/// Os métodos de autenticação deverão ser migrados para classes separadas e os
	/// seus clientes deverão usar ICampaignAuthenticator.
	///</summary>
	public interface IAuthenticatorApp
	{
		Task<bool> RegisterAccessLog(LogRegisterAction action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null);
		Task<bool> ExistLoginInCampaign(Guid campaignId, string login);
		Task<UserParticipantModel> AuthenticateParticipantByPassword(Guid campaignId, ParticipantLoginModel login);
		Task<UserParticipantModel> AuthenticateParticipantByIntegration(Guid campaignId, ParticipantIntegrationData login);
		Task<Guid> AuthenticateByPlatformSso(Guid campaignId, ParticipantIntegrationData login);
		Task<UserParticipantModel> FinalizeAuthenticatedUserUsingSsoToken(Guid campaignId, LoginSsoEndingRequest loginSso);
		Task<UserParticipantModel> AuthenticateParticipantForCallcenter(Guid campaignId, CallcenterLogin login);
		Task<Guid> AuthenticateParticipantForCallcenterSso(Guid campaignId, CallcenterLogin login);
		Task<UserParticipantModel> AuthenticateCampaignSiteIntegration(Guid campaignId, CampaignSiteLogin login);
		Task<UserParticipantModel> AuthenticateForMobile(Guid campaignId, ParticipantLoginModel login);
		Task<UserParticipantModel> AuthenticateOperatorInAccount(Guid campaignId, OperatorAuthenticationModel operatorAuthentication);
		Task<CampaignGroupSsoResult> AuthenticateAtTargetGroupCampaign(Guid campaignId, CampaignGroupSsoRequest ssoRequest);
        Task<UserParticipantModel> RefreshLoggedParticipant(Guid campaignId, Guid userId);
		Task<bool> SendAuthenticationCode(Guid userId, SimpleSecurityCodeRequest securityCodeRequest);
		Task<bool> ValidateAuthenticationCode(Guid userId, SimpleSecurityCodeValidation codeValidation);
		Task<UserParticipantModel> AuthenticateOperatorBySSO(Guid campaignId, AccountOperatorSsoRequest operatorSso);
	}
}
