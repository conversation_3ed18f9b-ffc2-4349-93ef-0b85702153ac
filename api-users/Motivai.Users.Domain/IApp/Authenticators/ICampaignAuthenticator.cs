using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IApp.Authenticators
{
	///<summary>
	/// Classe de autenticação de participante na campanha, utiliza o tipo de login configurado na campanha.
	///</summary>
	public interface ICampaignAuthenticator
	{
		Task<UserParticipantModel> AuthenticateParticipantByCampaignLoginType(Guid campaignId, ParticipantLoginModel login);
	}
}