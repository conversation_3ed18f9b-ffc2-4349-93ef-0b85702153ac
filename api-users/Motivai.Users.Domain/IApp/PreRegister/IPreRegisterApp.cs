using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IApp.PreRegister {
    public interface IPreRegisterApp {
        Task<bool> Create(PreRegisteredUser user);
        Task<List<PreRegisteredUser>> Find(Guid campaignId, string document, bool? integrated, bool? integrationError, int? skip, int? limit);
        Task<PreRegisteredUser> FindById(Guid id);
        Task<bool> Approve(Guid id, PreRegisteredUser user);
        Task<bool> Refuse(Guid id, PreRegisteredUser user);
        Task<dynamic> ValidateDocument(Guid campaignId, string document);
        Task<bool> ValidateUserData(Guid campaignId, PreRegisteredUser userData);
        Task<bool> Reintegrate(Guid id);
        Task IntegrateByDocument(Guid campaignId, string document);

    }
}