using System.Collections.Generic;
using System.Threading.Tasks;
using System;

using Motivai.Users.Domain.Entities.CreditCards;

namespace Motivai.Users.Domain.IApp.CreditCards
{
    public interface IUserCreditCardApp
    {
        Task<Guid> Create(Guid userId, UserCreditCard userCreditCard);
        Task<bool> UpdateBilling(Guid userId, Guid userCreditCardId, UserCreditCardBilling userCreditCardBilling);
        Task<UserCreditCard> Get(Guid userId, Guid userCreditCardId);
        Task<UserCreditCard> Get(Guid userId, String cardId);
        Task<List<UserCreditCard>> Get(Guid userId);
        Task<Boolean> Delete(Guid userId, Guid userCreditCardId);
    }
}