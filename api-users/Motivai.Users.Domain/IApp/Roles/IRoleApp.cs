using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IApp.Roles
{
    public interface IRoleApp
    {
        Task<List<Role>> GetRoles(string name, int? skip, int? limit);
        Task<Role> GetRole(Guid roleId);
        Task<bool> Save(Role role);
        Task<bool> Delete(Guid roleId);
        Task<Role> GetRoleByToken(string token);
    }
}