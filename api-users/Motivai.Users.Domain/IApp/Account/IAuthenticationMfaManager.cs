using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.Users.Domain.Models.Security;

namespace Motivai.Users.Domain.IApp.Account
{
    public interface IAuthenticationMfaManager
    {
        Task<bool> UpdateAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa);
        Task<bool> SendAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa);
        Task<bool> ValidateAuthenticationMfaToken(Guid campaignId, Guid userId, ValidateMfaToken userToken);
        Task<bool> SendAuthenticationMfaTokenToValidate(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa);
        Task<ParticipantAuthenticationMfaSettings> FindAuthenticationMfaSettings(Guid campaignId, Guid userId);
        Task<ParticipantAuthenticationMfaSettings> FindAuthenticationMfaSettingsToValidate(Guid campaignId, Guid userId);
    }
}