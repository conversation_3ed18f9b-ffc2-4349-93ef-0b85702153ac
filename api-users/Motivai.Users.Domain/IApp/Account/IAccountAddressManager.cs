using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.MyAccount.Registration;

namespace Motivai.Users.Domain.IApp.MyAccount {
    public interface IAccountAddressManager {
        Task<List<Address>> GetAllAddresses(Guid userId, Guid campaignId, bool? main = false);
        Task<List<Address>> GetActiveAddresses(Guid userId, Guid campaignId, bool? main = false);
        Task<List<ResumedAddress>> GetResumedAddresses(Guid userId, Guid campaignId);
        Task<Address> GetMainAddressOrFirst(Guid userId, Guid campaignId);
        Task<Address> GetAddressById(Guid userId, Guid campaignId, Guid addressId);
        Task<string> GetAddressCep(Guid userId, Guid campaignId, Guid addressId);
        Task<Address> SaveAddress(Guid userId, Guid campaignId, AddressUpdate address);
        Task<bool> UpdateAddress(Guid userId, Guid campaignId, Guid addressId, AddressUpdate address);
        Task<bool> DeleteAdress(Guid userId, Guid campaignId, Guid adressId);
    }
}