using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.Registration;

namespace Motivai.Users.Domain.IApp.MyAccount {
    public interface IParticipantRegistrationManager {
        Task<bool> VerifyIfNeedsCompleteRegistration(Guid userId, Guid campaignId);
        Task<ParticipantRegistrationData> GetRegistrionDataToComplete(Guid userId, Guid campaignId);
        Task<bool> CompleteRegistration(Guid userId, Guid campaignId, ParticipantRegistrationData participantData);
    }
}