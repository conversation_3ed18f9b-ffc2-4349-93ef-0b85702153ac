using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.Wallets;

namespace Motivai.Users.Domain.IApp.Account {
    public interface IUserConfigurationManager {
        Task<bool> GetUserTransactionalConfiguration(Guid userId);
        Task<bool> ConfigureTransactionalPassword(Guid userId, TransactionalPassword transactionalPassword);
        Task<bool> ValidateTransactionalPassword(Guid userId, TransactionalPassword transactionalPassword);
    }
}