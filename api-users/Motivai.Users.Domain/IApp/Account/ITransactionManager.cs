using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.Transactions;

namespace Motivai.Users.Domain.IApp.Account {
    public interface ITransactionManager {
		Task<BlockedPointsSummary> GetBlockedPoints(Guid userId, Guid campaignId);
		Task<List<TransactionDetailsModel>> GetLastAccumulations(Guid userId, Guid campaignId);
		Task<List<TransactionDetailsModel>> GetLastRedeems(Guid userId, Guid campaignId);
		Task<ExpiringPointsSummary> GetExpiringPoints(Guid userId, Guid campaignId);
		Task<BalanceResumeModel> LoadSummary(Guid userId, Guid campaignId);
		Task<ExtractModel> GetTransactions(Guid userId, Guid campaignId,
			TransactionType? transactionType = null, TransactionOrigin? transactionOrigin = null,
			DateTime? startDate = null, DateTime? endDate = null,
			int? skip = null, int? limit = null);
        Task<ExtractModel> GetExtractWithBlockedTransactions(Guid userId, Guid campaignId,
			TransactionType? transactionType = null, TransactionOrigin? transactionOrigin = null,
			DateTime? startDate = null, DateTime? endDate = null,
			int? skip = null, int? limit = null);
        Task<ExtractModel> GetBlockedTransactionsExtract(Guid userId, Guid campaignId,
			TransactionType? transactionType = null, TransactionOrigin? transactionOrigin = null,
			DateTime? startDate = null, DateTime? endDate = null,
			int? skip = null, int? limit = null);
    }
}