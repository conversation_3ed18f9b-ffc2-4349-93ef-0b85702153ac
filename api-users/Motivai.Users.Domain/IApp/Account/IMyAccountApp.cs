﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.Account.Registration;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.Users.Domain.Models.PrivacyPolicy;
using Motivai.Users.Domain.Entities.Users;

namespace Motivai.Users.Domain.IApp.MyAccounts
{
    public interface IMyAccountApp
	{
		Task<ParticipantInfo> GetParticipantInfo(Guid participantId);
		Task<string> GetParticipantDocument(Guid participantId);
		Task<bool> UpdatePassword(Guid userId, Guid campaignId, PasswordModel model);
		Task<ParticipantDataModel> GetRegistrationData(Guid userId, Guid campaignId);
		Task<bool> UpdateRegistrationData(Guid userId, Guid campaignId, ParticipantDataModel participantData);
		Task<bool> UpdateContactInfo(Guid userId, Guid campaignId, UserParticipantDataModel participantData);
		Task<ParticipantContact> GetPrincipalContact(Guid userId, Guid campaignId);
		Task<List<ResumedOrder>> GetOrdersWithProducts(Guid userId, string status, DateTime? startDate, DateTime? endDate);
		Task<List<ResumedOrder>> GetOrders(Guid userId, Guid campaignId, string status, DateTime? initialDate, DateTime? finalDate);
		Task<bool> RegisterRegulationAcceptance(Guid userId, Guid campaignId, string regulationId, string version);
		Task<bool> UpdateParent(Guid userId, Guid campaignId, UserBasicInfo parent);
		Task<bool> BlockParticipant(Guid userId, Guid campaignId, UserBlockingRequest blockingRequest);
		Task<bool> HasAcceptedAnyTerm(Guid userId, Guid campaignId);
	}
}