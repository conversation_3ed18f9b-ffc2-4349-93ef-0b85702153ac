using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Products;

namespace Motivai.Users.Domain.IApp.Account {
    public interface IAccountBalanceReader {
        Task<decimal> GetBalance(Guid userId, Guid campaignId);
        Task<decimal> GetBalanceByRankings(Guid userId, Guid campaignId, List<Guid> rankings);
        Task<decimal> GetReservedBalanceFor(Guid userId, Guid campaignId, Product product);
    }
}