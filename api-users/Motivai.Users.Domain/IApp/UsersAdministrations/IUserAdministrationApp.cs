﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.UsersAdministrators;
using Motivai.Users.Domain.Models.UserAdministration;

namespace Motivai.Users.Domain.IApp.UsersAdministrations
{
    public interface IUserAdministrationApp
    {
        Task<UserAdministration> Get(Guid userId);
        Task<Guid> AuthenticateAdministratorWithMfa(UserAdministrationAuthenticationRequest authenticationRequest);
        Task<UserAdministrationAuthenticationResponse> AuthenticateAdministratorWithoutMfa(UserAdministrationAuthenticationRequest authenticationRequest);
        Task<UserAdministrationAuthenticationResponse> ValidateAuthenticatedMfa(UserAdministrationAuthenticationValidateRequest authenticationRequest);
        Task<string> GetUserName(Guid userId);
        Task<List<UserAdministration>> GetUsers(string name, string email, string login, int? skip, int? limit);
        Task<string> CreateUser(UserAdministrationModel userModel);
        Task<bool> UpdateUser(Guid userId, UserAdministrationModel userModel);
        Task<bool> DeleteUser(Guid userId);
        Task<string> ResetPassword(Guid userId, UserAdministrationAction actionLog);
        Task<bool> ChangePassword(Guid userId, UserAdministrationPasswordModel passwordModel);
        Task<Guid> CreateAndGetId(UserAdministrationModel userModel);
    }
}
