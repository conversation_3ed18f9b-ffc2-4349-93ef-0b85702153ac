using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Users.Domain.Entities.B2b;

namespace Motivai.Users.Domain.IApp.B2b {
    public interface IShopKeeperApp {
        Task<Address> GetAddressByUser(Guid userId, Guid buId);
        Task<ParticipantInfo> GetOrCreateParticipantInfo(Guid userId, Guid campaignId);
        Task<List<ShopKeeperInfo>> SearchShopKeepers(string term, Guid buId);
        Task<List<Guid>> FindShopkeeperCampaigns(Guid shopkeeperId, Guid buId);
        Task<Shopkeeper> Create(Shopkeeper shopkeeper, Guid buId);
        Task<bool> Update(Guid shopkeeperId, Shopkeeper shopkeeper, Guid buId);
        Task<List<Shopkeeper>> Find(string document, string name, int skip, int limit, Guid buId);
        Task<Shopkeeper> FindById(Guid id, Guid buId);
    }
}