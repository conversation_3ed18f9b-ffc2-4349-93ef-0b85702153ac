using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities;

namespace Motivai.Users.Domain.IApp.B2b {
    public interface IUsersB2bPortalApp {
        Task<UserB2bPortal> Create(Guid buId, UserB2bPortal user);
        Task<bool> Update(UserB2bPortal user);
        Task<List<UserB2bPortal>> Find(Guid buId, string document, int skip, int limit);
        Task<UserB2bPortal> FindById(Guid id);
        Task<dynamic> Authenticate(string login, string password);
        Task<UserB2bPortal> ResetPassword(Guid userId);
        Task<bool> UpdatePassword(Guid userId, dynamic obj);
    }
}