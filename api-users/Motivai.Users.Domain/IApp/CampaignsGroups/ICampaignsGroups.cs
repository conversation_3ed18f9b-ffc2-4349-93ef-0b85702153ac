using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.CampaignsGroups;

namespace Motivai.Users.Domain.IApp.CampaignsGroups
{
	public interface ICampaignsGroupsApp
	{
		Task UpdateCampaignsInCoalitionIfNeeded(EventType eventType, Guid originId, Guid campaignId,
			Guid userId, Guid participantId, Guid? accountOperatorId = default, OperationOrigin? creationOrigin = default);
		Task UpdateAccountOperatorCampaignsInCoalitionIfNeeded(EventType eventType, Guid originId, Guid campaignId,
			Guid accountOperatorId = default, OperationOrigin? creationOrigin = default);
	}
}
