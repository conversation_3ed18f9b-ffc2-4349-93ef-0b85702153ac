using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.Users;

namespace Motivai.Users.Domain.IApp.ParticipantManagementApp
{
	public interface IParticipantManagementApp
	{
        Task<ParticipantManagementModel> GetParticipantRegistration(Guid userId, Guid campaignId);
		Task<bool> UpdateParticipantRegistration(Guid userId, Guid campaignId, ParticipantManagementModel participantDetails);
		Task<bool> UnblockingParticipant(Guid userId, Guid campaignId, UnblockingParticipantDetails unblockingRequest);

	}
}
