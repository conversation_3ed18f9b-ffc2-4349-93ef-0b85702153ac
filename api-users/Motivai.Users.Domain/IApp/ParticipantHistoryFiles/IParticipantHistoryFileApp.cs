using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.ParticipantHistoryFiles;

namespace Motivai.Users.Domain.IApp.ParticipantHistoryFiles {
    public interface IParticipantHistoryFileApp {
        Task<bool> Save(Guid UserId, Guid CampaignId, ParticipantHistoryFile participantHistoryFile);
        Task<List<ParticipantHistoryFile>> GetByParticipant(Guid UserId, Guid CampaignId, bool active = false);

         Task<bool> Unactive(Guid userId, Guid campaignId, Guid id);
    }
}