using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.Users;

namespace Motivai.Users.Domain.IApp.UsersManagement
{
	public interface IUserManagementApp
	{
		Task<List<UserCampaignsInfo>> SearchParticipantByDocument(Guid campaignId, string document, string login);
		Task<bool> UpdateRegistrationDataRanking(Guid userId, Guid campaignId, ParticipantDataModel participantData);
		Task<bool> ResetParticipantFirstAccess(Guid userId, Guid campaignId, OperationUser operationUser);
	}
}