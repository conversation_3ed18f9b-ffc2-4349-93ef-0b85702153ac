﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.Users.Domain.Models.Import;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Models.PrivacyPolicy;

namespace Motivai.Users.Domain.IApp.UsersParticipantCampaigns {
    public interface IUserParticipatCampaignApp {
        Task<List<Guid>> GetRankingChildrenById(Guid campaignId, Guid rankingId);
        Task<dynamic> SearchParticipant(Guid? campaginId, string document, string login);
        Task<bool> RememberPassword(Guid campaignId, PasswordRecoveryRequest model);
        Task<UserParticipantCampaign> GetUserParticipantById(Guid id);
        Task<bool> UpdateBalanceAndPointsByMechanics(Guid userParticipantId, UserTotalPoints userTotalPoints);
        Task<List<ParticipantInfo>> SearchParticipantInCampaign(Guid campaignId, string document = null, string cpf = null, string cnpj = null, string name = null);
        Task<string> SendVerificationCodeBySms(Guid userId, Guid campaignId);
        Task<bool> ResetPasswordWithSms(Guid userId, Guid campaignId);
        Task<dynamic> ImportUserIfDoesntExist(Guid campaignId, ParticipantIntegrationData participantIntegrationData);
        Task<List<string>> GetDistinctEmployee(Guid campaignId, string propertieEmployee);
        Task<List<ParticipantInfo>> SearchParticipantInCampaign(Guid campaignId);
        Task<dynamic> QueryUserInfoByDocument(Guid campaignId, string document);
        Task<bool> SearchDocumentInCampaign(Guid campaignId, string document);
        Task<bool> UpdateUserContactById(Guid campaignId, Guid userId, UsersCallcenterAction callcenterActionRegister);
    }
}