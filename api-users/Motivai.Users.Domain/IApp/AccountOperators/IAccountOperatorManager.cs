using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.AccountOperators.Actions;
using Motivai.Users.Domain.Models.AccountOperators;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.Domain.IApp.AccountOperators {
    public interface IAccountOperatorManager {
        Task<List<ResumedAccountOperator>> GetAccountOperators(Guid userId, Guid campaignId);
        Task<ResumedAccountOperator> QueryAccountOperatorByDocument(Guid userId, Guid campaignId, string document);
        Task<Guid> CreateOrUpdateAccountOperator(Guid userId, Guid campaignId, ResumedAccountOperator accountOperator);
        Task<bool> CreateAccountOperator(Guid userId, Guid campaignId, ResumedAccountOperator accountOperator);
        Task<bool> BlockAccountOperator(Guid userId, Guid campaignId, AccountOperatorAccessChange operatorBlocking);
        Task<bool> ActiveAccountOperator(Guid userId, Guid campaignId, AccountOperatorAccessChange operatorActive);
        Task<bool> ResetAccountOperatorPassword(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId, OperatorUser operatorUser);
        Task<List<UserCampaignsInfo>> GetOperatorAccessibleAccounts(Guid accountOperatorId, Guid accountOperatorLoginId);
        Task<ResumedAccountOperator> GetOperatorBy(Guid userId, Guid campaignId, string document);
        Task<bool> UpdateAccountOperatorPassword(Guid accountOperatorId, Guid accountOperatorLoginId, PasswordModel passwordUpdate);
        Task<OperatorRole?> GetAccountOperatorRoleInAccount(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId);
        Task<bool> UpdateAccountOperatorEmail(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId, UpdateDataAccountOperator updateDataAccountOperator);
        Task<bool> UpdateAccountOperatorRole(Guid userId, Guid accountOperatorId, Guid accountOperatorLoginId, UpdateDataAccountOperator updateDataAccountOperator);
        Task<List<OperatorAcessibleAccounts>> GetOperatorAcessibleAccounts(string document, Guid campaignId);
    }
}
