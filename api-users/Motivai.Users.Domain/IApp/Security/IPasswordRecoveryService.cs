using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Models.PasswordRecovery;

namespace Motivai.Users.Domain.IApp.Security
{
	public interface IPasswordRecoveryService
	{
		Task<ParticipantRecoveryContact> GetParticipantContactToPasswordRecovery(Guid campaignId, PasswordRecoveryRequest passwordRecovery);
		Task<string> SendPasswordRecoveryToken(Guid campaignId, RecoveryTokenIssue tokenIssue);
		Task<bool> UpdatePassword(Guid campaignId, RecoveryPasswordUpdate passwordUpdate);
		Task<bool> ForceParticipantPasswordReset(Guid campaignId, string document, string password);

	}
}