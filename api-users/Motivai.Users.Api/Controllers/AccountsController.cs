using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Users;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.Users.Domain.IApp.MyAccounts;
using Motivai.Users.Domain.IApp.Account;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using System.Collections.Generic;
using Motivai.Users.Domain.Models.PrivacyPolicy;
using Motivai.Users.Domain.Services.Security;
using Motivai.Users.Domain.Models.Account.Registration;

namespace Motivai.Users.Api.Controllers
{
    ///<summary>
    /// Controller para centralizar as ações na conta do participante.
    /// Idealmente será migrado no futuro para um projeto Java.
    ///</summary>
    [Route("/users/{userId}/campaigns/{campaignId}")]
	public class AccountsController
	{
		private readonly IUserApp _userApp;
		private readonly IMyAccountApp myAccountApp;
		private readonly IAccountBalanceReader accountBalanceReader;
		private readonly IUserMetadataApp userMetadataApp;
		private readonly IUserParentFinder userParentFinder;
		private readonly ParticipantPrivacySettingsManager privacySettingsManager;

		public AccountsController(IUserApp userApp, IMyAccountApp myAccountApp,
				IAccountBalanceReader accountBalanceReader, IUserMetadataApp userMetadataApp,
				IUserParentFinder userParentFinder, ParticipantPrivacySettingsManager privacySettingsManager)
		{
			_userApp = userApp;
			this.myAccountApp = myAccountApp;
			this.accountBalanceReader = accountBalanceReader;
			this.userMetadataApp = userMetadataApp;
			this.userParentFinder = userParentFinder;
			this.privacySettingsManager = privacySettingsManager;
		}

		[HttpGet("participant/id")]
		public async Task<ApiReturn<Guid>> GetParticipantIdIfActive(Guid userId, Guid campaignId)
		{
			return await ApiReturn<Guid>.Execute(_userApp.GetParticipantIdIfActive(userId, campaignId));
		}

		[HttpGet("status")]
		public async Task<ApiReturn<bool>> VerifyUserActiveAtCampaign(Guid userId, Guid campaignId)
		{
			return await ApiReturn<bool>.Execute(_userApp.IsUserActiveAtCampaign(userId, campaignId));
		}

		[HttpPut("participant/block")]
		public async Task<ApiReturn<bool>> BlockParticipant(Guid userId, Guid campaignId,
				[FromBody] UserBlockingRequest blockingRequest)
		{
			return await ApiReturn.Execute(myAccountApp.BlockParticipant(userId, campaignId, blockingRequest));
		}

		[HttpGet("reservedbalance")]
		public async Task<ApiReturn<decimal>> GetReservedBalanceFor(Guid userId, Guid campaignId,
				[FromQuery] Product product)
		{
			return await ApiReturn.Execute(accountBalanceReader.GetReservedBalanceFor(userId, campaignId, product));
		}

		[HttpGet("info")]
		public async Task<ApiReturn<ParticipantInfo>> GetUserData(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantInfo>.Execute(_userApp.GetUserData(userId, campaignId));
		}

		[HttpGet("contact")]
		public async Task<ApiReturn<ParticipantContact>> GetPrincipalContact(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantContact>.Execute(myAccountApp.GetPrincipalContact(userId, campaignId));
		}

		[HttpGet("participant")]
		public async Task<ApiReturn<UserParticipantCampaign>> GetParticipantById(Guid userId, Guid campaignId)
		{
			return await ApiReturn<UserParticipantCampaign>.Execute(_userApp.GetParticipantByUserAndCampaign(userId, campaignId));
		}

		[HttpGet("participant/info")]
		public async Task<ApiReturn<ParticipantInfo>> GetParticipantInfo(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantInfo>.Execute(_userApp.GetParticipantInfo(userId, campaignId));
		}

		[HttpGet("participant/billinginfo")]
		public async Task<ApiReturn<ParticipantInfo>> GetParticipantBillingInfo(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantInfo>.Execute(_userApp.GetParticipantBillingInfo(userId, campaignId));
		}

		[HttpGet("clientdata")]
		public async Task<ApiReturn<dynamic>> GetParticipantClientData(Guid userId, Guid campaignId)
		{
			return await ApiReturn<dynamic>.Execute(_userApp.GetParticipantClientData(userId, campaignId));
		}

		[HttpGet("metadatavalues")]
		[HttpGet("metadata")]
		public async Task<ApiReturn<UserMetadataValue>> GetUsersMetadataValue(Guid userId, Guid campaignId)
		{
			return await ApiReturn<UserMetadataValue>.Execute(userMetadataApp.GetUsersMetadataValue(campaignId, userId));
		}

		[HttpGet("metadata/{fieldKey}")]
		public async Task<ApiReturn<string>> GetUserMetadataFieldValue(Guid userId, Guid campaignId, string fieldKey)
		{
			return await ApiReturn.Execute(userMetadataApp.GetUserMetadataFieldValue(userId, campaignId, fieldKey));
		}

		[HttpGet("parentshierarchy/all")]
		public async Task<ApiReturn<List<UserBasicInfo>>> GetUsersWithSameParentHierarchy(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(userParentFinder.GetUsersWithSameParentHierarchy(userId, campaignId));
		}

		[HttpGet("parent")]
		public async Task<ApiReturn<UserBasicInfo>> GetParentByUserId(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(userParentFinder.GetParentByUserId(userId, campaignId));
		}

		[HttpPut("parent")]
		public async Task<ApiReturn<bool>> UpdateParent(Guid userId, Guid campaignId, [FromBody] UserBasicInfo parent)
		{
			return await ApiReturn.Execute(myAccountApp.UpdateParent(userId, campaignId, parent));
		}

		[HttpPut("participant/privacypolicy/acceptance")]
		public async Task<ApiReturn<bool>> UpdatePrivacyPolicyAcceptance(Guid userId, Guid campaignId, [FromBody] PrivacyPolicyChangeModel privacyPolicyChange)
		{
			return await ApiReturn.Execute(privacySettingsManager.RegisterPrivacySettingsChange(userId, campaignId, privacyPolicyChange));
		}
	}
}