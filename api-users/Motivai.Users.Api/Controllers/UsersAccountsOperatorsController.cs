using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.IApp.AccountOperators;
using Motivai.Users.Domain.Models.AccountOperators;

namespace Motivai.Users.Api.Controllers
{
	[Produces("application/json")]
	[Route("users/{userId}/campaigns/{campaignId}/account/operators")]
	public class UsersAccountsOperatorsController : Controller
	{
		private readonly IAccountOperatorManager accountOperatorManager;

		public UsersAccountsOperatorsController(IAccountOperatorManager accountOperatorManager)
		{
			this.accountOperatorManager = accountOperatorManager;
		}

		[HttpGet]
		public async Task<ApiReturn<List<ResumedAccountOperator>>> GetAccountOperators(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(accountOperatorManager.GetAccountOperators(userId, campaignId));
		}

		[HttpGet("{accountOperatorId}/logins/{accountOperatorLoginId}/role")]
		public async Task<ApiReturn<OperatorRole?>> GetAccountOperatorRoleInAccount(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId)
		{
			return await ApiReturn.Execute(accountOperatorManager.GetAccountOperatorRoleInAccount(userId, campaignId, accountOperatorId, accountOperatorLoginId));
		}

		[HttpGet("search")]
		public async Task<ApiReturn<ResumedAccountOperator>> QueryAccountOperatorByDocument(Guid userId, Guid campaignId, [FromQuery] string document = null)
		{
			return await ApiReturn.Execute(accountOperatorManager.QueryAccountOperatorByDocument(userId, campaignId, document));
		}

		[HttpPost]
		public async Task<ApiReturn<bool>> CreateAccountOperator(Guid userId, Guid campaignId, [FromBody] ResumedAccountOperator accountOperator)
		{
			return await ApiReturn.Execute(accountOperatorManager.CreateAccountOperator(userId, campaignId, accountOperator));
		}

		[HttpPost("{operatorId}/logins/{loginId}/password/reset")]
		public async Task<ApiReturn<bool>> ResetAccountOperatorPassword(Guid userId, Guid campaignId,
			Guid operatorId, Guid loginId, [FromBody] OperatorUser operatorUser)
		{
			return await ApiReturn.Execute(accountOperatorManager.ResetAccountOperatorPassword(userId, campaignId, operatorId, loginId, operatorUser));
		}

		[HttpPost("block")]
		public async Task<ApiReturn<bool>> BlockAccountOperator(Guid userId, Guid campaignId, [FromBody] AccountOperatorAccessChange operatorBlocking)
		{
			return await ApiReturn.Execute(accountOperatorManager.BlockAccountOperator(userId, campaignId, operatorBlocking));
		}

		[HttpPost("active")]
		public async Task<ApiReturn<bool>> ActiveAccountOperator(Guid userId, Guid campaignId, [FromBody] AccountOperatorAccessChange operatorActive)
		{
			return await ApiReturn.Execute(accountOperatorManager.ActiveAccountOperator(userId, campaignId, operatorActive));
		}

		[HttpPut("{operatorId}/logins/{loginId}/email")]
		public async Task<ApiReturn<bool>> UpdateAccountOperatorEmail(Guid userId, Guid campaignId,
			Guid operatorId, Guid loginId, [FromBody] UpdateDataAccountOperator updateDataAccountOperator)
		{
			return await ApiReturn.Execute(accountOperatorManager.UpdateAccountOperatorEmail(userId, campaignId, operatorId, loginId, updateDataAccountOperator));
		}

		[HttpPut("{operatorId}/logins/{loginId}/role")]
		public async Task<ApiReturn<bool>> UpdateAccountOperatorRole(Guid userId, Guid campaignId, Guid operatorId, Guid loginId, [FromBody] UpdateDataAccountOperator updateDataAccountOperator)
		{
			return await ApiReturn.Execute(accountOperatorManager.UpdateAccountOperatorRole(userId, operatorId, loginId, updateDataAccountOperator));
		}
	}
}
