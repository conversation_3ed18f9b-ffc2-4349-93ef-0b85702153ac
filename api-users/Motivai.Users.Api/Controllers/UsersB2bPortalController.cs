using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.B2b;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{
	[Route("usersb2bportal")]
	public class UsersB2bPortalController : BaseApiController
	{
		private readonly IUsersB2bPortalApp _usersB2bPortal;
		public UsersB2bPortalController(IUsersB2bPortalApp usersB2bPortal)
		{
			_usersB2bPortal = usersB2bPortal;
		}

		[HttpPost("authenticate")]
		public async Task<ApiReturn<dynamic>> Authenticate([FromBody] dynamic users)
		{
			return await ApiReturn<dynamic>.Execute(_usersB2bPortal.Authenticate(users.login.ToString(), users.password.ToString()));
		}

		[HttpGet("{userId}/campaigns")]
		public async Task<ApiReturn<List<B2bUserCampaign>>> GetLinkedCampaigns(Guid userId)
		{
			UserB2bPortal user = await _usersB2bPortal.FindById(userId);
			return ApiReturn<List<B2bUserCampaign>>.FromValue(user.Campaigns);
		}

		[HttpGet("{userId}/factories")]
		public async Task<ApiReturn<List<B2bFactories>>> GetLinkedFactories(Guid userId)
		{
			UserB2bPortal user = await _usersB2bPortal.FindById(userId);
			return ApiReturn<List<B2bFactories>>.FromValue(user.Factories);
		}

		[HttpPost]
		public async Task<ApiReturn<UserB2bPortal>> Create([FromBody] UserB2bPortal user)
		{
			return await ApiReturn<UserB2bPortal>.Execute(_usersB2bPortal.Create(BuId, user));
		}

		[HttpGet("{userId}")]
		public async Task<ApiReturn<UserB2bPortal>> FindById(Guid userId)
		{
			return await ApiReturn<UserB2bPortal>.Execute(_usersB2bPortal.FindById(userId));
		}

		[HttpGet]
		public async Task<ApiReturn<List<UserB2bPortal>>> Find(
			[FromQuery] string document, [FromQuery] int skip = 0,
			[FromQuery] int limit = 100)
		{
			return await ApiReturn<List<UserB2bPortal>>.Execute(_usersB2bPortal.Find(BuId, document, skip, limit));
		}

		[HttpPut("{userId}/password/reset")]
		public async Task<ApiReturn<UserB2bPortal>> ResetPassword(Guid userId)
		{
			return await ApiReturn<UserB2bPortal>.Execute(_usersB2bPortal.ResetPassword(userId));
		}

		[HttpPut("{userId}/password")]
		public async Task<ApiReturn<bool>> UpdatePassword(Guid userId, [FromBody] dynamic password)
		{
			return await ApiReturn<bool>.Execute(_usersB2bPortal.UpdatePassword(userId, password));
		}

		[HttpPut("{userId}")]
		public async Task<ApiReturn<bool>> Update(Guid userId, [FromBody] UserB2bPortal user)
		{
			user.Id = userId;
			return await ApiReturn<bool>.Execute(_usersB2bPortal.Update(user));
		}
	}
}