using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.MyAccounts;
using Motivai.Users.Domain.IApp.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.FirstAccess;
using Motivai.Users.Domain.Models.Transactions;
using Motivai.Users.Domain.IApp.UsersManagement;
using Motivai.Users.Domain.Models.MyAccount.Registration;
using Motivai.Users.Domain.IApp.MyAccount;
using Motivai.Users.App.Account;
using Motivai.Users.Domain.IApp.Account;
using Motivai.SharedKernel.Domain.Entities.References.Users.FirstAccess;
using Motivai.Users.Domain.Models.PrivacyPolicy;
using Motivai.Users.Domain.Entities.Users;

namespace Motivai.Users.Api.Controllers
{
	///<summary>
	/// Controller com as ações na conta do participante.
	/// Os paths aqui estão no modelo antigo onde usam ParticipantId.
	/// Aos poucos serão migrados para o modelo UserId + CampaignId
	///</summary>
	[Produces("application/json")]
	[Route("participants")]
	public class ParticipantsController : Controller
	{
		private readonly IUserParticipatCampaignApp _participationCampaignApp;
		private readonly IMyAccountApp _myAccountApp;
		private readonly IAccountAddressManager accountAddressManager;
		private readonly IUserManagementApp userManagementApp;
		private readonly FirstAccessCompleter firstAccessCompleter;
		private readonly IAccountBalanceReader accountBalanceReader;
		private readonly ITransactionManager transactionManager;

		public ParticipantsController(IUserParticipatCampaignApp participationCampaignApp, IMyAccountApp myAccountApp,
				IAccountAddressManager accountAddressManager, IUserManagementApp userManagementApp,
				FirstAccessCompleter firstAccessCompleter, IAccountBalanceReader accountBalanceReader,
				ITransactionManager transactionManager)
		{
			_participationCampaignApp = participationCampaignApp;
			_myAccountApp = myAccountApp;
			this.accountAddressManager = accountAddressManager;
			this.userManagementApp = userManagementApp;
			this.firstAccessCompleter = firstAccessCompleter;
			this.accountBalanceReader = accountBalanceReader;
			this.transactionManager = transactionManager;
		}

		[HttpGet]
		public async Task<ApiReturn<dynamic>> SearchParticipant([FromQuery] Guid? campaignId = null,
			[FromQuery] string document = null, [FromQuery] string login = null)
		{
			return await ApiReturn<dynamic>.Execute(_participationCampaignApp.SearchParticipant(campaignId, document, login));
		}

		[HttpGet("{participantId}")]
		public async Task<ApiReturn<UserParticipantCampaign>> Get(Guid participantId)
		{
			return await ApiReturn<UserParticipantCampaign>.Execute(_participationCampaignApp.GetUserParticipantById(participantId));
		}

		[HttpGet("{participantId}/info")]
		public async Task<ApiReturn<ParticipantInfo>> GetParticipantInfo(Guid participantId)
		{
			return await ApiReturn<ParticipantInfo>.Execute(_myAccountApp.GetParticipantInfo(participantId));
		}

		[HttpGet("{participantId}/document")]
		public async Task<ApiReturn<string>> GetParticipantDocument(Guid participantId)
		{
			return await ApiReturn<string>.Execute(_myAccountApp.GetParticipantDocument(participantId));
		}

		[HttpGet("{userId}/{campaignId}/balance")]
		public async Task<ApiReturn<decimal>> GetBalance(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(accountBalanceReader.GetBalance(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/balance/rankings")]
		public async Task<ApiReturn<decimal>> GetBalanceByRankings(Guid userId, Guid campaignId, [FromQuery] string rankings)
		{
			List<Guid> lowestRankings = null;
			if (!string.IsNullOrEmpty(rankings))
			{
				lowestRankings = rankings.Split(',').Select(r => Guid.Parse(r)).ToList();
			}
			return await ApiReturn.Execute(accountBalanceReader.GetBalanceByRankings(userId, campaignId, lowestRankings));
		}

		[HttpPost("{participantId}/balance")]
		public async Task<ApiReturn<bool>> UpdateBalance(Guid participantId, [FromBody] UserTotalPoints userTotalPoints)
		{
			return await ApiReturn.Execute(_participationCampaignApp.UpdateBalanceAndPointsByMechanics(participantId, userTotalPoints));
		}

		[HttpPost("{userId}/campaigns/{campaignId}/verificationcode/sms")]
		public async Task<ApiReturn<string>> SendVerificationCodeSms(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(_participationCampaignApp.SendVerificationCodeBySms(userId, campaignId));
		}

		[HttpPut("{userId}/campaigns/{campaignId}/password/sms")]
		public async Task<ApiReturn<bool>> ResetPasswordWithSms(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(_participationCampaignApp.ResetPasswordWithSms(userId, campaignId));
		}

		// Primeiro Acesso
		[HttpPost("{userId}/{campaignId}/firstaccess")]
		public async Task<ApiReturn<FirstAccessResult>> RegisterFirstAcess(Guid userId, Guid campaignId, [FromBody] FirstAccessDataModel firstAccessModel)
		{
			return await ApiReturn.Execute(firstAccessCompleter.RegisterFirstAccess(userId, campaignId, firstAccessModel));
		}

		[HttpPost("{userId}/{campaignId}/partial/firstaccess")]
		public async Task<ApiReturn<FirstAccessResult>> RegisterParcialFirstAcess(Guid userId, Guid campaignId, [FromBody] FirstAccessAcceptancesModel firstAccessModel)
		{
			return await ApiReturn.Execute(firstAccessCompleter.RegisterPartialFirstAccess(userId, campaignId, firstAccessModel));
		}

		// Minha Conta
		[HttpPut("{userId}/{campaignId}/password")]
		public async Task<ApiReturn<bool>> UpdatePassword(Guid userId, Guid campaignId, [FromBody] PasswordModel model)
		{
			return await ApiReturn<bool>.Execute(_myAccountApp.UpdatePassword(userId, campaignId, model));
		}

		[HttpGet("{userId}/{campaignId}/data")]
		public async Task<ApiReturn<ParticipantDataModel>> GetParticipantData(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantDataModel>.Execute(_myAccountApp.GetRegistrationData(userId, campaignId));
		}

		[HttpPut("{userId}/{campaignId}/data")]
		public async Task<ApiReturn<bool>> UpdateParticipantData(Guid userId, Guid campaignId, [FromBody] ParticipantDataModel participantData)
		{
			return await ApiReturn<bool>.Execute(_myAccountApp.UpdateRegistrationData(userId, campaignId, participantData));
		}

		[HttpPut("{userId}/{campaignId}/data/ranking")]
		public async Task<ApiReturn<bool>> UpdateParticipantDataRanking(Guid userId, Guid campaignId, [FromBody] ParticipantDataModel participantData)
		{
			return await ApiReturn<bool>.Execute(userManagementApp.UpdateRegistrationDataRanking(userId, campaignId, participantData));
		}

		[HttpGet("{userId}/{campaignId}/addresses/all")]
		public async Task<ApiReturn<List<Address>>> GetAllAddresses(Guid userId, Guid campaignId, [FromQuery] bool? main = false)
		{
			return await ApiReturn<List<Address>>.Execute(accountAddressManager.GetAllAddresses(userId, campaignId, main));
		}

		[HttpGet("{userId}/{campaignId}/addresses")]
		public async Task<ApiReturn<List<Address>>> GetAddresses(Guid userId, Guid campaignId, [FromQuery] bool? main = false)
		{
			return await ApiReturn<List<Address>>.Execute(accountAddressManager.GetActiveAddresses(userId, campaignId, main));
		}

		[HttpGet("{userId}/{campaignId}/addresses/resumed")]
		public async Task<ApiReturn<List<ResumedAddress>>> GetResumedAddresses(Guid userId, Guid campaignId)
		{
			return await ApiReturn<List<ResumedAddress>>.Execute(accountAddressManager.GetResumedAddresses(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/addresses/main")]
		public async Task<ApiReturn<Address>> GetMainAddressOrFirst(Guid userId, Guid campaignId)
		{
			return await ApiReturn<Address>.Execute(accountAddressManager.GetMainAddressOrFirst(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/addresses/{addressId}")]
		public async Task<ApiReturn<Address>> GetAddressById(Guid userId, Guid campaignId, Guid addressId)
		{
			return await ApiReturn<Address>.Execute(accountAddressManager.GetAddressById(userId, campaignId, addressId));
		}

		[HttpGet("{userId}/{campaignId}/addresses/{addressId}/cep")]
		public async Task<ApiReturn<string>> GetAddressCepById(Guid userId, Guid campaignId, Guid addressId)
		{
			return await ApiReturn<string>.Execute(accountAddressManager.GetAddressCep(userId, campaignId, addressId));
		}

		[HttpPost("{userId}/{campaignId}/addresses")]
		public async Task<ApiReturn<Address>> CreateAddress(Guid userId, Guid campaignId, [FromBody] AddressUpdate address)
		{
			return await ApiReturn<Address>.Execute(accountAddressManager.SaveAddress(userId, campaignId, address));
		}

		[HttpPut("{userId}/{campaignId}/addresses/{addressId}")]
		public async Task<ApiReturn<bool>> UpdateAddress(Guid userId, Guid campaignId, Guid addressId, [FromBody] AddressUpdate address)
		{
			return await ApiReturn<bool>.Execute(accountAddressManager.UpdateAddress(userId, campaignId, addressId, address));
		}

		[HttpDelete("{userId}/{campaignId}/addresses/{addressId}")]
		public async Task<ApiReturn<bool>> RemoveAddress(Guid userId, Guid campaignId, Guid addressId)
		{
			return await ApiReturn<bool>.Execute(accountAddressManager.DeleteAdress(userId, campaignId, addressId));
		}

		///<summary>
		/// Depreceado: utilizar da classe UsersController
		///</summary>
		[HttpGet("{userId}/{campaignId}/contact")]
		public async Task<ApiReturn<ParticipantContact>> GetPrincipalContact(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantContact>.Execute(_myAccountApp.GetPrincipalContact(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/summary")]
		public async Task<ApiReturn<BalanceResumeModel>> LoadAccountSummary(Guid userId, Guid campaignId)
		{
			return await ApiReturn<BalanceResumeModel>.Execute(transactionManager.LoadSummary(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/lastaccumlations")]
		public async Task<ApiReturn<List<TransactionDetailsModel>>> GetLastAccumulations(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(transactionManager.GetLastAccumulations(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/lastredeems")]
		public async Task<ApiReturn<List<TransactionDetailsModel>>> GetLastRedeems(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(transactionManager.GetLastRedeems(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/expiringpoints")]
		public async Task<ApiReturn<ExpiringPointsSummary>> GetExpiringPoints(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ExpiringPointsSummary>.Execute(transactionManager.GetExpiringPoints(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/blockedpoints")]
		public async Task<ApiReturn<BlockedPointsSummary>> GetBlockedPoints(Guid userId, Guid campaignId)
		{
			return await ApiReturn<BlockedPointsSummary>.Execute(transactionManager.GetBlockedPoints(userId, campaignId));
		}

		[HttpGet("{userId}/{campaignId}/transactions")]
		public async Task<ApiReturn<ExtractModel>> GetTransactions(Guid userId, Guid campaignId,
				[FromQuery] TransactionType? transactionType = default(TransactionType?),
				[FromQuery] TransactionOrigin? transactionOrigin = default(TransactionOrigin?),
				[FromQuery] DateTime? startDate = default(DateTime?), [FromQuery] DateTime? endDate = default(DateTime?),
				[FromQuery] int? skip = default(int?), [FromQuery] int? limit = default(int?))
		{
			return await ApiReturn.Execute(transactionManager.GetTransactions(userId, campaignId, transactionType, transactionOrigin,
				startDate, endDate, skip, limit));
		}

		[HttpGet("{userId}/{campaignId}/transactions/with-blocked-transactions")]
		public async Task<ApiReturn<ExtractModel>> GetExtractWithBlockedTransactions(Guid userId, Guid campaignId,
				[FromQuery] TransactionType? transactionType = default(TransactionType?),
				[FromQuery] TransactionOrigin? transactionOrigin = default(TransactionOrigin?),
				[FromQuery] DateTime? startDate = default(DateTime?), [FromQuery] DateTime? endDate = default(DateTime?),
				[FromQuery] int? skip = default(int?), [FromQuery] int? limit = default(int?))
		{
			return await ApiReturn.Execute(transactionManager.GetExtractWithBlockedTransactions(userId, campaignId, transactionType, transactionOrigin,
				startDate, endDate, skip, limit));
		}

		[HttpGet("{userId}/{campaignId}/transactions/only-blocked-transactions")]
		public async Task<ApiReturn<ExtractModel>> GetBlockedTransactionsExtract(Guid userId, Guid campaignId,
				[FromQuery] TransactionType? transactionType = default(TransactionType?),
				[FromQuery] TransactionOrigin? transactionOrigin = default(TransactionOrigin?),
				[FromQuery] DateTime? startDate = default(DateTime?), [FromQuery] DateTime? endDate = default(DateTime?),
				[FromQuery] int? skip = default(int?), [FromQuery] int? limit = default(int?))
		{
			return await ApiReturn.Execute(transactionManager.GetBlockedTransactionsExtract(userId, campaignId, transactionType, transactionOrigin,
				startDate, endDate, skip, limit));
		}

		[HttpGet("{userId}/{campaignId}/orders")]
		public async Task<ApiReturn<List<ResumedOrder>>> GetOrders(Guid userId, Guid campaignId, [FromQuery] string status,
				[FromQuery] DateTime? initialDate, [FromQuery] DateTime? finalDate)
		{
			return await ApiReturn<List<ResumedOrder>>.Execute(_myAccountApp.GetOrders(userId, campaignId, status, initialDate, finalDate));
		}

		[HttpGet("{userId}/marketplace/orders/resumed")]
		public async Task<ApiReturn<List<ResumedOrder>>> GetMarketplaceOrders(Guid userId, [FromQuery] string status,
				[FromQuery] DateTime? initialDate, [FromQuery] DateTime? finalDate)
		{
			return await ApiReturn.Execute(_myAccountApp.GetOrdersWithProducts(userId, status, initialDate, finalDate));
		}

		[HttpPost("{userId}/campaigns/{campaignId}/regulations/{regulationId}/versions/{version}")]
		public async Task<ApiReturn<bool>> RegisterRegulationAcceptance(Guid userId, Guid campaignId, string regulationId, string version)
		{
			return await ApiReturn<bool>.Execute(_myAccountApp.RegisterRegulationAcceptance(userId, campaignId, regulationId, version));
		}

		[HttpGet("campaigns/{campaignId}/positions")]
		public async Task<ApiReturn<List<string>>> GetPositions(Guid campaignId)
		{
			return await ApiReturn<List<string>>.Execute(_participationCampaignApp.GetDistinctEmployee(campaignId, "Position"));
		}

		[HttpGet("campaigns/{campaignId}/directions")]
		public async Task<ApiReturn<List<string>>> GetDirections(Guid campaignId)
		{
			return await ApiReturn<List<string>>.Execute(_participationCampaignApp.GetDistinctEmployee(campaignId, "Direction"));
		}

		[HttpGet("campaigns/{campaignId}/departments")]
		public async Task<ApiReturn<List<string>>> GetDepartments(Guid campaignId)
		{
			// LoggerHelper.GetLogger().Info("{date} - Users - Participants Controller - {methodName}", DateTime.UtcNow, nameof(GetDepartments));
			return await ApiReturn<List<string>>.Execute(_participationCampaignApp.GetDistinctEmployee(campaignId, "Department"));
		}

		[HttpGet("{userId}/campaigns/{campaignId}/regulations/accepted")]
		public async Task<ApiReturn<bool>> ParticpantHasAcceptedAnyTerm(Guid userId, Guid campaignId) {
			return await ApiReturn.Execute(_myAccountApp.HasAcceptedAnyTerm(userId, campaignId));
		}
		}
}