using Microsoft.AspNetCore.Mvc;

using System.Collections.Generic;
using System.Threading.Tasks;
using System;

using Motivai.Users.Domain.Entities.CreditCards;
using Motivai.Users.Domain.IApp.CreditCards;

using Motivai.SharedKernel.Domain.Model;

namespace Motivai.Users.Api.Controllers
{
	[Produces("application/json")]
	[Route("users/{userId}/creditcards")]
	public class UserCreditCardController : Controller
	{
		private readonly IUserCreditCardApp userCreditCardApp;

		public UserCreditCardController(IUserCreditCardApp userCreditCardApp)
		{
			this.userCreditCardApp = userCreditCardApp;
		}

		[HttpPost()]
		public async Task<ApiReturn<Guid>> Create(Guid userId, [FromBody] UserCreditCard userCreditCard)
		{
			return await ApiReturn<Guid>.Execute(userCreditCardApp.Create(userId, userCreditCard));
		}

		[HttpPut("{userCreditCardId}/billing")]
		public async Task<ApiReturn<bool>> UpdateBilling(Guid userId, Guid userCreditCardId, [FromBody] UserCreditCardBilling userCreditCardBilling)
		{
			return await ApiReturn<bool>.Execute(userCreditCardApp.UpdateBilling(userId, userCreditCardId, userCreditCardBilling));
		}

		[HttpGet("{userCreditCardId}")]
		public async Task<ApiReturn<UserCreditCard>> Get(Guid userId, Guid userCreditCardId)
		{
			return await ApiReturn<UserCreditCard>.Execute(userCreditCardApp.Get(userId, userCreditCardId));
		}

		[HttpGet("gatewayinfo/{cardId}")]
		public async Task<ApiReturn<UserCreditCard>> Get(Guid userId, string cardId)
		{
			return await ApiReturn<UserCreditCard>.Execute(userCreditCardApp.Get(userId, cardId));
		}

		[HttpGet]
		public async Task<ApiReturn<List<UserCreditCard>>> Get(Guid userId)
		{
			return await ApiReturn<List<UserCreditCard>>.Execute(userCreditCardApp.Get(userId));
		}


		[HttpDelete("{userCreditCardId}")]
		public async Task<ApiReturn<bool>> Delete(Guid userId, Guid userCreditCardId)
		{
			return await ApiReturn<bool>.Execute(userCreditCardApp.Delete(userId, userCreditCardId));
		}
	}
}