using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.Security;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.IApp.UsersParticipantCampaigns;

namespace Motivai.Users.Api.Controllers
{
	[Route("campaigns/{campaignId}")]
	public class ParticipantsPasswordRecoveryController
	{
		private readonly IPasswordRecoveryService passwordRecovery;
		private readonly IUserParticipatCampaignApp participationCampaignApp;

		public ParticipantsPasswordRecoveryController(IPasswordRecoveryService passwordRecovery, IUserParticipatCampaignApp participationCampaignApp)
		{
			this.passwordRecovery = passwordRecovery;
			this.participationCampaignApp = participationCampaignApp;
		}

		[HttpPut("participants/{document}/password")]
		public async Task<ApiReturn<bool>> ResetPassword(Guid campaignId, string document, [FromBody] dynamic payload) {
			string password = payload.password;
			return await ApiReturn.Execute(passwordRecovery.ForceParticipantPasswordReset(campaignId, document, password));
		}

		[HttpPost("participants/passwordrecovery/search")]
		public async Task<ApiReturn<ParticipantRecoveryContact>> SearchParticipantContact(Guid campaignId, [FromBody] PasswordRecoveryRequest passwordRecoveryRequest)
		{
			return await ApiReturn.Execute(passwordRecovery.GetParticipantContactToPasswordRecovery(campaignId, passwordRecoveryRequest));
		}

		[HttpPost("participants/passwordrecovery/token")]
		public async Task<ApiReturn<string>> SendPasswordRecoveryToken(Guid campaignId, [FromBody] RecoveryTokenIssue tokenIssue)
		{
			return await ApiReturn<string>.Execute(passwordRecovery.SendPasswordRecoveryToken(campaignId, tokenIssue));
		}

		[HttpPut("participants/passwordrecovery/password")]
		public async Task<ApiReturn<bool>> UpdatePassword(Guid campaignId, [FromBody] RecoveryPasswordUpdate passwordModel)
		{
			return await ApiReturn<bool>.Execute(passwordRecovery.UpdatePassword(campaignId, passwordModel));
		}

		[HttpPut("participants/resetpassword")]
		public async Task<ApiReturn<bool>> ResetPassword(Guid campaignId, [FromBody] PasswordRecoveryRequest model)
		{
			return await ApiReturn<bool>.Execute(participationCampaignApp.RememberPassword(campaignId, model));
		}
	}
}