using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IApp.MyAccounts;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.Security;
using Motivai.Users.Domain.Entities.Wallets.Devices;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.IApp.MyAccount;
using Motivai.Users.Domain.Models.MyAccount.Registration;

namespace Motivai.Users.Api.Controllers
{
	[Produces("application/json")]
	[Route("users/{userId}")]
	public class UserAccountController
	{
		private readonly IMyAccountApp accountApp;
		private readonly IUserConfigurationManager configurationManager;
		private readonly IUserDeviceManagerApp userDeviceManagerApp;
		private readonly IAccountAddressManager addressManager;
		private readonly ITransactionManager transactionManager;

		public UserAccountController(IUserConfigurationManager configurationManager, IMyAccountApp accountApp, IUserDeviceManagerApp userDeviceManagerApp,
				IAccountAddressManager addressManager,  ITransactionManager transactionManager)
		{
			this.configurationManager = configurationManager;
			this.accountApp = accountApp;
			this.userDeviceManagerApp = userDeviceManagerApp;
			this.addressManager = addressManager;
			this.transactionManager = transactionManager;
		}

		#region Dispositivos

		[HttpPost("configurations/devices")]
		public async Task<ApiReturn<bool>> AuthorizeDevice(Guid userId, [FromBody] DeviceAuthorizationRequest deviceAuthorizationRequest)
		{
			return await ApiReturn.Execute(userDeviceManagerApp.AuthorizeDevice(userId, deviceAuthorizationRequest));
		}

		[HttpPost("configurations/devices/verify")]
		public async Task<ApiReturn<bool>> VerifyDeviceAuthorization(Guid userId, [FromBody] DeviceRequestInfo deviceRequestInfo)
		{
			return await ApiReturn.Execute(userDeviceManagerApp.VerifyDeviceAuthorization(userId, deviceRequestInfo));
		}

		[HttpGet("configurations/devices/{id}")]
		public async Task<ApiReturn<bool>> GetDeviceById(Guid userId, Guid campaignId, string id)
		{
			return await ApiReturn.Execute(userDeviceManagerApp.GetById(userId, id));
		}

		[HttpGet("configurations/devices/{id}/active")]
		public async Task<ApiReturn<bool>> GetActiveDevice(Guid userId, Guid campaignId, string id)
		{
			return await ApiReturn.Execute(userDeviceManagerApp.GetActive(userId, id));
		}

		[HttpPost("configurations/devices/authorization")]
		public async Task<ApiReturn<bool>> SendAuthorizationCode(Guid userId, [FromBody] SecurityCodeRequest securityCodeRequest)
		{
			return await ApiReturn.Execute(userDeviceManagerApp.SendAuthorizationCode(userId, securityCodeRequest));
		}

		[HttpPut("configurations/devices/authorization")]
		public async Task<ApiReturn<bool>> ValidateAuthorizationCode(Guid userId, [FromBody] SecurityCodeValidation codeValidation)
		{
			return await ApiReturn.Execute(userDeviceManagerApp.ValidateAuthorizationCode(userId, codeValidation));
		}

		#endregion

		#region Senha Transacional
		[HttpGet("configurations/transactionalpassword")]
		public async Task<ApiReturn<bool>> GetUserTransactionalConfiguration(Guid userId)
		{
			return await ApiReturn.Execute(configurationManager.GetUserTransactionalConfiguration(userId));
		}

		[HttpPost("configurations/transactionalpassword")]
		public async Task<ApiReturn<bool>> ConfigureTransactionalPassword(Guid userId, [FromBody] TransactionalPassword transactionalPassword)
		{
			return await ApiReturn.Execute(configurationManager.ConfigureTransactionalPassword(userId, transactionalPassword));
		}

		[HttpPost("configurations/transactionalpassword/validate")]
		public async Task<ApiReturn<bool>> ValidateTransactionalPassword(Guid userId, [FromBody] TransactionalPassword transactionalPassword)
		{
			return await ApiReturn.Execute(configurationManager.ValidateTransactionalPassword(userId, transactionalPassword));
		}
		#endregion

		#region Minha Conta

		[HttpPost("campaigns/{campaignId}/addresses")]
		public async Task<ApiReturn<Address>> CreateAddress(Guid userId, Guid campaignId, [FromBody] AddressUpdate address)
		{
			return await ApiReturn.Execute(addressManager.SaveAddress(userId, campaignId, address));
		}

		[HttpPut("campaigns/{campaignId}/addresses/{addressId}")]
		public async Task<ApiReturn<bool>> UpdateAddress(Guid userId, Guid campaignId, Guid addressId, [FromBody] AddressUpdate address)
		{
			return await ApiReturn<bool>.Execute(addressManager.UpdateAddress(userId, campaignId, addressId, address));
		}

		[HttpDelete("campaigns/{campaignId}/addresses/{addressId}")]
		public async Task<ApiReturn<bool>> RemoveAddress(Guid userId, Guid campaignId, Guid addressId)
		{
			return await ApiReturn<bool>.Execute(addressManager.DeleteAdress(userId, campaignId, addressId));
		}

		[HttpGet("campaigns/{campaignId}/addresses/main")]
		public async Task<ApiReturn<Address>> GetMainAddressOrFirst(Guid userId, Guid campaignId)
		{
			return await ApiReturn<Address>.Execute(addressManager.GetMainAddressOrFirst(userId, campaignId));
		}

		[HttpGet("campaigns/{campaignId}/addresses/{addressId}")]
		public async Task<ApiReturn<Address>> GetAddressById(Guid userId, Guid campaignId, Guid addressId)
		{
			return await ApiReturn<Address>.Execute(addressManager.GetAddressById(userId, campaignId, addressId));
		}

		[HttpGet("campaigns/{campaignId}/addresses")]
		public async Task<ApiReturn<List<Address>>> GetAddresses(Guid userId, Guid campaignId, [FromQuery] bool? main = false)
		{
			return await ApiReturn<List<Address>>.Execute(addressManager.GetActiveAddresses(userId, campaignId, main));
		}

		[HttpGet("campaigns/{campaignId}/addresses/{addressId}/cep")]
		public async Task<ApiReturn<string>> GetAddressCepById(Guid userId, Guid campaignId, Guid addressId)
		{
			return await ApiReturn<string>.Execute(addressManager.GetAddressCep(userId, campaignId, addressId));
		}

		[HttpPut("campaigns/{campaignId}/data")]
		public async Task<ApiReturn<bool>> UpdateParticipantData(Guid userId, Guid campaignId, [FromBody] ParticipantDataModel participantData)
		{
			return await ApiReturn<bool>.Execute(accountApp.UpdateRegistrationData(userId, campaignId, participantData));
		}

		[HttpPut("campaigns/{campaignId}/contact")]
		public async Task<ApiReturn<bool>> UpdateContactInfo(Guid userId, Guid campaignId, [FromBody] UserParticipantDataModel participantData)  {
			return  await ApiReturn<bool>.Execute(accountApp.UpdateContactInfo(userId, campaignId, participantData));
		}

		[HttpGet("campaigns/{campaignId}/data")]
		public async Task<ApiReturn<ParticipantDataModel>> GetParticipantData(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantDataModel>.Execute(accountApp.GetRegistrationData(userId, campaignId));
		}

		[HttpGet("campaigns/{campaignId}/transactions")]
		public async Task<ApiReturn<ExtractModel>> GetTransactions(Guid userId, Guid campaignId,
				[FromQuery] TransactionType? transactionType = default(TransactionType?),
				[FromQuery] TransactionOrigin? transactionOrigin = default(TransactionOrigin?),
				[FromQuery] DateTime? startDate = default(DateTime?), [FromQuery] DateTime? endDate = default(DateTime?),
				[FromQuery] int? skip = default(int?), [FromQuery] int? limit = default(int?))
		{
			return await ApiReturn.Execute(transactionManager.GetTransactions(userId, campaignId,
				transactionType, transactionOrigin, startDate, endDate, skip, limit));
		}
		#endregion
	}
}