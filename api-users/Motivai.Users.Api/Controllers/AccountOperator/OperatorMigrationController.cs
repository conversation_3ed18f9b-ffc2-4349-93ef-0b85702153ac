using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities.OperatorMigration;
using Motivai.Users.Domain.IApp.OperatorMigration;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers.AccountOperator
{
    [Produces("application/json")]
    [Route("/users/operators/migration")]
    public class OperatorMigrationController : Controller
    {
        private readonly IOperatorMigrationApp operatorMigrationService;
        public OperatorMigrationController(IOperatorMigrationApp operatorMigrationService)
        {
            this.operatorMigrationService = operatorMigrationService;
        }

        [HttpPut()]
        public async Task<ApiReturn<bool>> MigrateAccountOperatorAuthenticationMethod([FromBody] OperatorMigrationModel operatorMigration)
        {
            return await ApiReturn<bool>.Execute(this.operatorMigrationService.MigrateAccountOperatorAuthenticationMethod(operatorMigration));
        }
    }
}