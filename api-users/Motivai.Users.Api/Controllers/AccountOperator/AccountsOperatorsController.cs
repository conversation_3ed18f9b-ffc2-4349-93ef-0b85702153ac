using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.AccountOperators;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers.AccountOperator
{
	[Route("users/operators/{accountOperatorId}/logins/{accountOperatorLoginId}")]
	public class AccountsOperatorsController : Controller
	{
		private readonly IAccountOperatorManager accountOperatorManager;

		public AccountsOperatorsController(IAccountOperatorManager accountOperatorManager)
		{
			this.accountOperatorManager = accountOperatorManager;
		}

		// exportar
		[HttpGet("accessibleaccounts")]
		public async Task<ApiReturn<List<UserCampaignsInfo>>> GetOperatorAccessibleAccounts(Guid accountOperatorId, Guid accountOperatorLoginId)
		{
			return await ApiReturn.Execute(accountOperatorManager.GetOperatorAccessibleAccounts(accountOperatorId, accountOperatorLoginId));
		}

		[HttpPut("password")]
		public async Task<ApiReturn<bool>> UpdateAccountOperatorPassword(Guid accountOperatorId, Guid accountOperatorLoginId, [FromBody] PasswordModel passwordUpdate)
		{
			return await ApiReturn.Execute(accountOperatorManager.UpdateAccountOperatorPassword(accountOperatorId, accountOperatorLoginId, passwordUpdate));
		}

		//criar path novo que recebe o cpf do operador
		// accessibleaccounts - spe
	}
}