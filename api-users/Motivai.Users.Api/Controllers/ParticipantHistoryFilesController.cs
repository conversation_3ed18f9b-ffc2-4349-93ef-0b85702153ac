using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.ParticipantHistoryFiles;
using Motivai.Users.Domain.Entities.ParticipantHistoryFiles;

namespace Motivai.Users.Api.Controllers
{
	[Produces("application/json")]
	[Route("participants")]
	public class ParticipantHistoryFilesController
	{
		private readonly IParticipantHistoryFileApp participantHistoryFileApp;

		public ParticipantHistoryFilesController(IParticipantHistoryFileApp participantHistoryFileApp)
		{
			this.participantHistoryFileApp = participantHistoryFileApp;
		}

		[HttpGet("{userId}/campaigns/{campaignId}/historyfiles")]
		public async Task<ApiReturn<List<ParticipantHistoryFile>>> Get(Guid userId, Guid campaignId, [FromQuery] bool active = false)
		{
			return await ApiReturn<List<ParticipantHistoryFile>>.Execute(participantHistoryFileApp.GetByParticipant(userId, campaignId, active));
		}

		[HttpPost("{userId}/campaigns/{campaignId}/historyfiles")]
		public async Task<ApiReturn<bool>> Get(Guid userId, Guid campaignId, [FromBody] ParticipantHistoryFile participantHistoryFile)
		{
			return await ApiReturn<bool>.Execute(participantHistoryFileApp.Save(userId, campaignId, participantHistoryFile));
		}

		[HttpPut("{userId}/campaigns/{campaignId}/historyfiles/{id}")]
		public async Task<ApiReturn<bool>> Unactive(Guid userId, Guid campaignId, Guid id)
		{
			return await ApiReturn<bool>.Execute(participantHistoryFileApp.Unactive(userId, campaignId, id));
		}

	}
}