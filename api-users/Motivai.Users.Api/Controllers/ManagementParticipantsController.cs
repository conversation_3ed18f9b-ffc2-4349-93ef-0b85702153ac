using System;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.IApp.ParticipantManagementApp;

namespace Motivai.Users.Api.Controllers
{
	[Produces("application/json")]
	[Route("users/{userId}/campaigns/{campaignId}/management")]
	public class ManagementParticipantsController : Controller
	{
		private readonly IParticipantManagementApp participantManagementApp;

		public ManagementParticipantsController(IParticipantManagementApp participantManagementApp)
		{
			this.participantManagementApp = participantManagementApp;
		}

		[HttpGet("registration")]
		public async Task<ApiReturn<ParticipantManagementModel>> GetParticipantRegistration(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantManagementModel>.Execute(participantManagementApp.GetParticipantRegistration(userId, campaignId));
		}

		[HttpPut("registration")]
		public async Task<ApiReturn<bool>> UpdateParticipantRegistration(Guid userId, Guid campaignId, [FromBody] ParticipantManagementModel participantDetails)
		{
			return await ApiReturn<bool>.Execute(participantManagementApp.UpdateParticipantRegistration(userId, campaignId, participantDetails));
		}

		[HttpPut("unblock")]
		public async Task<ApiReturn<bool>> UpdateParticipantUnblocking(Guid userId, Guid campaignId, [FromBody] UnblockingParticipantDetails unblockingRequest)
		{
			return await ApiReturn<bool>.Execute(participantManagementApp.UnblockingParticipant(userId, campaignId, unblockingRequest));
		}

	}
}
