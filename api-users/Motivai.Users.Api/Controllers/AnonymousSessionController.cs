using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.AnonymousSession;
using Motivai.Users.Domain.Models.AnonymousSession;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{
	[Route("accounts/anonymoussessions")]
	public class AnonymousSessionController
	{
		private readonly IAnonymousSessionApp anonymousSessionApp;

		public AnonymousSessionController(IAnonymousSessionApp anonymousSessionApp)
		{
			this.anonymousSessionApp = anonymousSessionApp;
		}

		[HttpPost("cookiesaccepted")]
		public async Task<ApiReturn> CookieAccepted([FromBody] AnonymousSessionAcceptanceInfo sessionIfo)
		{
			return await ApiReturn.Execute(this.anonymousSessionApp.CookieAccepted(sessionIfo));
		}
	}
}