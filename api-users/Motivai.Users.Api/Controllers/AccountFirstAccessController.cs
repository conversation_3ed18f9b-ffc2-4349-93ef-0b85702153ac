using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users.FirstAccess;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.App.Account;
using Motivai.Users.Domain.Models.FirstAccess;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{
	[Route("/users/{userId}/campaigns/{campaignId}")]
	public class AccountFirstAccessController : Controller
	{
		private readonly FirstAccessCompleter firstAccessCompleter;

		public AccountFirstAccessController(FirstAccessCompleter firstAccessCompleter)
		{
			this.firstAccessCompleter = firstAccessCompleter;
		}

		[HttpPost("firstaccess")]
		public async Task<ApiReturn<FirstAccessResult>> RegisterFirstAcess(Guid userId, Guid campaignId,
			[FromBody] FirstAccessDataModel firstAccessModel)
		{
			return await ApiReturn.Execute(firstAccessCompleter.RegisterFirstAccess(userId, campaignId, firstAccessModel));
		}
	}
}