using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.UsersAdministrators;
using Motivai.Users.Domain.IApp.UsersAdministrations;
using Motivai.Users.Domain.Models.UserAdministration;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers.Admin
{
    [Route("administrators")]
    public class UserAdministrationController : Controller
    {
        private readonly IUserAdministrationApp _userAdministrationApp;

        public UserAdministrationController(IUserAdministrationApp userAdministrationApp)
        {
            _userAdministrationApp = userAdministrationApp;
        }

        [HttpPost("authenticate")]
        public async Task<ApiReturn<Guid>> Authenticate([FromBody] UserAdministrationAuthenticationRequest authenticationRequest)
        {
            return await ApiReturn.Execute(_userAdministrationApp.AuthenticateAdministratorWithMfa(authenticationRequest));
        }

        [HttpPost("authentication/withoutmfa/validate")]
        public async Task<ApiReturn<UserAdministrationAuthenticationResponse>> ValidateAuthenticateWithoutMFA([FromBody] UserAdministrationAuthenticationRequest authenticationRequest)
        {
            return await ApiReturn.Execute(_userAdministrationApp.AuthenticateAdministratorWithoutMfa(authenticationRequest));
        }

        [HttpPost("authentication/mfa/validate")]
        public async Task<ApiReturn<UserAdministrationAuthenticationResponse>> ValidateAuthenticate([FromBody] UserAdministrationAuthenticationValidateRequest authenticationRequest)
        {
            return await ApiReturn.Execute(_userAdministrationApp.ValidateAuthenticatedMfa(authenticationRequest));
        }

        [HttpGet]
        public async Task<ApiReturn<List<UserAdministration>>> GetUsers(
            [FromQuery] string name,
            [FromQuery] string email,
            [FromQuery] string login,
            [FromQuery] int? skip,
            [FromQuery] int? limit)
        {

            return await ApiReturn<List<UserAdministration>>.Execute(_userAdministrationApp.GetUsers(name, email, login, skip, limit));
        }

        [HttpGet("{id}")]
        public async Task<ApiReturn<UserAdministration>> GetUser(Guid id)
        {
            return await ApiReturn<UserAdministration>.Execute(_userAdministrationApp.Get(id));
        }

        [HttpGet("{userId}/name")]
        public async Task<ApiReturn<string>> GetUserName(Guid userId)
        {
            return await ApiReturn<string>.Execute(_userAdministrationApp.GetUserName(userId));
        }

        [HttpPost]
        public async Task<ApiReturn<string>> Create([FromBody] UserAdministrationModel user)
        {
            return await ApiReturn<string>.Execute(_userAdministrationApp.CreateUser(user));
        }

        [HttpPost("id")]
        public async Task<ApiReturn<Guid>> CreateAndGetId([FromBody] UserAdministrationModel user)
        {
            return await ApiReturn<Guid>.Execute(_userAdministrationApp.CreateAndGetId(user));
        }

        [HttpPut("{userId}")]
        public async Task<ApiReturn<bool>> Update(Guid userId, [FromBody] UserAdministrationModel user)
        {
            return await ApiReturn<bool>.Execute(_userAdministrationApp.UpdateUser(userId, user));
        }

        [HttpPut("{userId}/password")]
        public async Task<ApiReturn<bool>> ChangePassword(Guid userId, [FromBody] UserAdministrationPasswordModel passwordModel)
        {
            return await ApiReturn<bool>.Execute(_userAdministrationApp.ChangePassword(userId, passwordModel));
        }

        [HttpPost("{userId}/password/reset")]
        public async Task<ApiReturn<string>> ResetPassword(Guid userId, [FromBody] UserAdministrationAction actionLog)
        {
            return await ApiReturn<string>.Execute(_userAdministrationApp.ResetPassword(userId, actionLog));
        }
    }
}
