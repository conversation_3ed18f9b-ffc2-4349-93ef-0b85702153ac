using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.UsersAdministrations;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers.Admin
{
    [Route("/administrators/actions/logs")]
    public class UserAdministrationActionsLogsController : Controller
    {
        private readonly IUserAdministrationActionLogApp actionLogApp;

        public UserAdministrationActionsLogsController(IUserAdministrationActionLogApp actionLogApp)
        {
            this.actionLogApp = actionLogApp;
        }

        [HttpPost]
        public async Task<ApiReturn<bool>> RegisterLog([FromBody] UserActionOperationLog userActionOperationLog)
        {
            return await ApiReturn.Execute(actionLogApp.RegisterLog(userActionOperationLog));
        }
    }
}