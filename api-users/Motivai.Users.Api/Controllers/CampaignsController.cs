using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IApp.UsersManagement;
using Motivai.Users.Domain.Models.Import;
using Motivai.Users.Domain.IApp.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.Users.Domain.IApp.Searches;
using Motivai.Users.Domain.Entities.Integrations;

namespace Motivai.Users.Api.Controllers
{
	///<summary>
	/// Controller para operações gerais (cadastro e pesquisa) de participantes de uma campanha.
	///</summary>
	[Route("campaigns/{campaignId}")]
	public class CampaignsController
	{
		private readonly IUserParticipatCampaignApp _participationCampaignApp;
		private readonly IUserManagementApp userManagementApp;
		private readonly IUserExternalClientApp userExternalClientApp;
		private readonly IUserApp _userApp;
		private readonly IParticipantSearchApp participantSearch;

		public CampaignsController(IUserParticipatCampaignApp participationCampaignApp, IUserManagementApp userManagementApp,
			IUserExternalClientApp userExternalClientApp, IUserApp userApp, IParticipantSearchApp participantSearch)
		{
			_participationCampaignApp = participationCampaignApp;
			this.userManagementApp = userManagementApp;
			this.userExternalClientApp = userExternalClientApp;
			_userApp = userApp;
			this.participantSearch = participantSearch;
		}

		[HttpGet("participantsrankings/{rankingId}")]
		public async Task<ApiReturn<List<Guid>>> GetRankingChildrenById(Guid campaignId, Guid rankingId)
		{
			return await ApiReturn<List<Guid>>.Execute(_participationCampaignApp.GetRankingChildrenById(campaignId, rankingId));
		}

		[HttpGet("participants/all")]
		public async Task<ApiReturn<List<ParticipantInfo>>> FindParticipantsByCampaign(Guid campaignId)
		{
			return await ApiReturn<List<ParticipantInfo>>.Execute(_participationCampaignApp.SearchParticipantInCampaign(campaignId));
		}

		[HttpGet("participants/infos")]
		public async Task<ApiReturn<List<ParticipantInfo>>> SearchParticipantsIncampaign(Guid campaignId,
			[FromQuery] ParticipantSearchField searchByField = ParticipantSearchField.DOCUMENT,
			[FromQuery] string document = null, [FromQuery] string searchValue = null)
		{
			return await ApiReturn.Execute(participantSearch.SearchParticipantsIncampaign(campaignId, searchByField, document ?? searchValue));
		}

		/// <summary>
		/// Depreceado.
		/// Utilizar: /campaigns/{campaignId}/participants/infos
		/// </summary>
		[HttpGet("participants")]
		public async Task<ApiReturn<List<ParticipantInfo>>> SearchParticipantInCampaignOld(Guid campaignId, [FromQuery] string document = null, [FromQuery] string cpf = null, [FromQuery] string cnpj = null, [FromQuery] string name = null)
		{
			return await ApiReturn<List<ParticipantInfo>>.Execute(_participationCampaignApp.SearchParticipantInCampaign(campaignId, document, cpf, cnpj, name));
		}

		/// <summary>
		/// Depreceado.
		/// Utilizar: /campaigns/{campaignId}/participants/infos
		/// </summary>
		[HttpGet("participants/search")]
		public async Task<ApiReturn<List<UserCampaignsInfo>>> SearchParticipantInCampaignOld(Guid campaignId, [FromQuery] string document = null, [FromQuery] string login = null)
		{
			return await ApiReturn.Execute(userManagementApp.SearchParticipantByDocument(campaignId, document, login));
		}

		/// <summary>
		/// Depreceado.
		/// Utilizar: /campaigns/{campaignId}/participants/infos
		/// </summary>
		[HttpGet("participants/{document}/info")]
		public async Task<ApiReturn<dynamic>> QueryUserInfoByDocument(Guid campaignId, string document)
		{
			return await ApiReturn<dynamic>.Execute(_participationCampaignApp.QueryUserInfoByDocument(campaignId, document));
		}

		[HttpPost("participants/import")]
		public async Task<ApiReturn<dynamic>> Import(Guid campaignId, [FromBody] ParticipantIntegrationData participantIntegrationData)
		{
			return await ApiReturn<dynamic>.Execute(_participationCampaignApp.ImportUserIfDoesntExist(campaignId, participantIntegrationData));
		}

		[HttpPost("participants/{document}/block")]
		public async Task<ApiReturn<bool>> BlockUserExternal(Guid campaignId, string document)
		{
			return await ApiReturn<bool>.Execute(userExternalClientApp.BlockParticipant(campaignId, document));
		}

		[HttpPut("participants/{document}/registrationdata")]
		public async Task<ApiReturn<bool>> UpdateUserExternal(Guid campaignId, string document, [FromBody] UserExternalModel userExternalModel)
		{
			return await ApiReturn<bool>.Execute(userExternalClientApp.UpdateParticipant(campaignId, document, userExternalModel));
		}

		[HttpPost("participants")]
		public async Task<ApiReturn<bool>> SaveUserExternal(Guid campaignId, [FromBody] UserExternalModel userExternalModel)
		{
			return await ApiReturn<bool>.Execute(userExternalClientApp.CreateParticipant(campaignId, userExternalModel));
		}

		[HttpGet("participants/{document}/balance")]
		public async Task<ApiReturn<decimal>> GetBalanceByUserId(Guid campaignId, string document)
		{
			return await ApiReturn<decimal>.Execute(userExternalClientApp.GetBalanceByUserId(campaignId, document));
		}
		// Informacoes basicas do participant elsys
		/// <summary>
		/// Depreceado.
		/// Utilizar: /campaigns/{campaignId}/participants/infos
		/// </summary>
		[HttpGet("participants/{document}/informations")]
		public async Task<ApiReturn<UserBasicInfo>> GetUserBasicInfo(Guid campaignId, string document)
		{
			return await ApiReturn<UserBasicInfo>.Execute(_userApp.GetUserBasicInfo(campaignId, document));
		}

		[HttpGet("participants/document/{document}/exists")]
		public async Task<ApiReturn<bool>> SearchDocumentInCampaign(Guid campaignId, string document)
		{
			return await ApiReturn<bool>.Execute(_participationCampaignApp.SearchDocumentInCampaign(campaignId, document));
		}

	}
}