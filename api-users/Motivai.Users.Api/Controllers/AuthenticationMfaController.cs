using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.Models.Security;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{
    ///<summary>
    /// Controller para centralizar as ações na conta do participante.
    /// Idealmente será migrado no futuro para um projeto Java.
    ///</summary>
    [Route("/users/{userId}/campaigns/{campaignId}")]
    public class AuthenticationMfaController
    {
        private readonly IAuthenticationMfaManager authenticationMfaManager;

        public AuthenticationMfaController(IAuthenticationMfaManager authenticationMfaManager)
        {
            this.authenticationMfaManager = authenticationMfaManager;
        }

        [HttpGet("mfa/data")]
        public async Task<ApiReturn<ParticipantAuthenticationMfaSettings>> FindAuthenticationMfaSettings(Guid userId, Guid campaignId)
        {
            return await ApiReturn.Execute(authenticationMfaManager.FindAuthenticationMfaSettings(campaignId, userId));
        }

        [HttpGet("mfa/validate/data")]
        public async Task<ApiReturn<ParticipantAuthenticationMfaSettings>> FindAuthenticationMfaSettingsToVaidate(Guid userId, Guid campaignId)
        {
            return await ApiReturn.Execute(authenticationMfaManager.FindAuthenticationMfaSettingsToValidate(campaignId, userId));
        }

        [HttpPut("mfa/data")]
        public async Task<ApiReturn<bool>> UpdateAuthenticationMfaToken(Guid userId, Guid campaignId, [FromBody] ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            return await ApiReturn.Execute(authenticationMfaManager.UpdateAuthenticationMfaToken(campaignId, userId, userAuthenticationMfa));
        }

        [HttpPost("mfa/send")]
        public async Task<ApiReturn<bool>> SendToken(Guid userId, Guid campaignId, [FromBody] ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            return await ApiReturn.Execute(authenticationMfaManager.SendAuthenticationMfaToken(campaignId, userId, userAuthenticationMfa));
        }

        [HttpPost("mfa/validate/send")]
        public async Task<ApiReturn<bool>> SendTokenToValidate(Guid userId, Guid campaignId, [FromBody] ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            return await ApiReturn.Execute(authenticationMfaManager.SendAuthenticationMfaTokenToValidate(campaignId, userId, userAuthenticationMfa));
        }

        [HttpPost("mfa/validate")]
        public async Task<ApiReturn<bool>> ValidateToken(Guid userId, Guid campaignId, [FromBody] ValidateMfaToken userToken)
        {
            return await ApiReturn.Execute(authenticationMfaManager.ValidateAuthenticationMfaToken(campaignId, userId, userToken));
        }

    }
}