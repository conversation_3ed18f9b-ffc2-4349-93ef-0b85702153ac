using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Entities.Integrations.CampaignsGroup;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IApp.Ath;
using Motivai.Users.Domain.IApp.Authenticators;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Microsoft.AspNetCore.Mvc;
using Motivai.Users.Domain.Models.Security;
using Motivai.Users.Domain.Entities.AccountOperators.Authentication;

namespace Motivai.Users.Api.Controllers
{
	[Route("campaigns/{campaignId}")]
	public class AuthenticatorController
	{
		private readonly ICampaignAuthenticator campaignAuthenticator;
		private readonly IAuthenticatorApp _authenticatorApp;

        public AuthenticatorController(ICampaignAuthenticator campaignAuthenticator, IAuthenticatorApp authenticatorApp)
		{
			this.campaignAuthenticator = campaignAuthenticator;
			_authenticatorApp = authenticatorApp;
        }

		[HttpPost("channels/mobile/authentication")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateForMobile(Guid campaignId, [FromBody] ParticipantLoginModel login)
		{
			return await ApiReturn<UserParticipantModel>.Execute(_authenticatorApp.AuthenticateForMobile(campaignId, login));
		}

		[HttpPost("participants/{userId}/authenticate/sso/log")]
		public async Task<ApiReturn<bool>> RegisterLoginWithSso(Guid campaignId, Guid userId,
			[FromBody] LocationInfo locationInfo, [FromQuery] string document = null)
		{
			return await ApiReturn.Execute(_authenticatorApp.RegisterAccessLog(LogRegisterAction.LoginWithSSO, userId, campaignId, document, locationInfo));
		}

		[HttpGet("participants/{login}/status")]
		public async Task<ApiReturn<bool>> ExistInCampaign(Guid campaignId, string login)
		{
			return await ApiReturn<bool>.Execute(_authenticatorApp.ExistLoginInCampaign(campaignId, login));
		}

		[HttpPost("participants/authentication")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateParticipantByCampaignLoginType(Guid campaignId, [FromBody] ParticipantLoginModel login)
		{
			return await ApiReturn.Execute(campaignAuthenticator.AuthenticateParticipantByCampaignLoginType(campaignId, login));
		}

		///<summary>
		/// Será depreciado em favor do método `AuthenticateParticipantByCampaignLoginType`.
		///</summary>
		[HttpPost("participants/authenticate")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateParticipantByPassword(Guid campaignId, [FromBody] ParticipantLoginModel login)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateParticipantByPassword(campaignId, login));
		}

		[HttpPost("participants/operators/authentication")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateOperatorInAccount(Guid campaignId, [FromBody] OperatorAuthenticationModel operatorAuthentication)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateOperatorInAccount(campaignId, operatorAuthentication));
		}

		[HttpPost("participants/integration/site/authenticate")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateForCampaignSite(Guid campaignId, [FromBody] CampaignSiteLogin login)
		{
			return await ApiReturn<UserParticipantModel>.Execute(_authenticatorApp.AuthenticateCampaignSiteIntegration(campaignId, login));
		}

		[HttpPut("participants/{userId}/refresh-session")]
		public async Task<ApiReturn<UserParticipantModel>> RefreshLoggedParticipant(Guid campaignId, Guid userId)
		{
			return await ApiReturn<UserParticipantModel>.Execute(_authenticatorApp.RefreshLoggedParticipant(campaignId, userId));
		}

		#region SSO

		///<summary>
		/// Utilizado no catálogo para já criar a sessão junto do SSO.
		///</summary>
		[HttpPost("participants/integration/authenticate")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateIntegration(Guid campaignId, [FromBody] ParticipantIntegrationData login)
		{
			return await ApiReturn<UserParticipantModel>.Execute(_authenticatorApp.AuthenticateParticipantByIntegration(campaignId, login));
		}

		[HttpPost("participants/sso/authentication")]
		public async Task<ApiReturn<Guid>> AuthenticateByPlatformSso(Guid campaignId, [FromBody] ParticipantIntegrationData login)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateByPlatformSso(campaignId, login));
		}

		[HttpPut("participants/sso")]
		public async Task<ApiReturn<UserParticipantModel>> FinalizeAuthenticatedUserUsingSsoToken(Guid campaignId, [FromBody] LoginSsoEndingRequest loginSso)
		{
			return await ApiReturn.Execute(_authenticatorApp.FinalizeAuthenticatedUserUsingSsoToken(campaignId, loginSso));
		}

		#endregion

		#region SSO de Coalizão

		[HttpPost("participants/campaignsgroup/sso/authentication")]
		public async Task<ApiReturn<CampaignGroupSsoResult>> AuthenticateAtTargetGroupCampaign(Guid campaignId,
				[FromBody] CampaignGroupSsoRequest ssoRequest)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateAtTargetGroupCampaign(campaignId, ssoRequest));
		}

		#endregion

		#region SSO VR Acesso

		[HttpPost("participants/accountoperators/sso/authentication")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateOperatorBySSOIntregration(Guid campaignId, [FromBody] AccountOperatorSsoRequest operatorSso)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateOperatorBySSO(campaignId, operatorSso));
		}

		#endregion

		#region Callcenter

		[HttpPost("participants/integration/authenticatecallcenter")]
		public async Task<ApiReturn<UserParticipantModel>> AuthenticateCallcenterIntegration(Guid campaignId, [FromBody] CallcenterLogin login)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateParticipantForCallcenter(campaignId, login));
		}

		[HttpPost("participants/integration/authenticatecallcenter/sso")]
		public async Task<ApiReturn<Guid>> AuthenticateCallcenterIntegrationSso(Guid campaignId, [FromBody] CallcenterLogin login)
		{
			return await ApiReturn.Execute(_authenticatorApp.AuthenticateParticipantForCallcenterSso(campaignId, login));
		}

		#endregion

		#region Para autenticação em duas etapas
		[HttpPost("users/{userId}/authentication/code")]
		public async Task<ApiReturn<bool>> SendAuthenticationCode(Guid userId, [FromBody] SimpleSecurityCodeRequest securityCodeRequest)
		{
			return await ApiReturn.Execute(_authenticatorApp.SendAuthenticationCode(userId, securityCodeRequest));
		}

		[HttpPut("users/{userId}/authentication/code")]
		public async Task<ApiReturn<bool>> ValidateAuthenticationCode(Guid userId, [FromBody] SimpleSecurityCodeValidation codeValidation)
		{
			return await ApiReturn.Execute(_authenticatorApp.ValidateAuthenticationCode(userId, codeValidation));
		}
		#endregion
	}
}
