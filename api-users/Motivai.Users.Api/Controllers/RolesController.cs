using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Roles;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{
	[Route("roles")]
	[Produces("application/json")]
	public class RolesController : Controller
	{
		private readonly IRoleApp _roleApp;

		public RolesController(IRoleApp roleApp)
		{
			_roleApp = roleApp;
		}

		[HttpGet]
		public async Task<ApiReturn<List<Role>>> GetRoles([FromQuery] string name, [FromQuery] int? skip, [FromQuery] int? limit)
		{
			return await ApiReturn<List<Role>>.Execute(_roleApp.GetRoles(name, skip, limit));
		}

		[HttpGet("{roleId}")]
		public async Task<ApiReturn<Role>> GetRole(Guid roleId)
		{
			return await ApiReturn<Role>.Execute(_roleApp.GetRole(roleId));
		}

		[HttpPost]
		public async Task<ApiReturn<bool>> CreateRole([FromBody] Role role)
		{
			return await ApiReturn<bool>.Execute(_roleApp.Save(role));
		}

		[HttpPut("{roleId}")]
		public async Task<ApiReturn<bool>> UpdateRole(Guid roleId, [FromBody] Role role)
		{
			if (roleId == Guid.Empty)
			{
				return ApiReturn<bool>.FromError("ID do perfil inválido.");
			}

			if (role != null)
			{
				role.Id = roleId;
			}

			return await ApiReturn<bool>.Execute(_roleApp.Save(role));
		}


		[HttpDelete("{roleId}")]
		public async Task<ApiReturn<bool>> DeleteRole(Guid roleId)
		{
			if (roleId == Guid.Empty)
			{
				return ApiReturn<bool>.FromError("ID do perfil inválido.");
			}

			return await ApiReturn<bool>.Execute(_roleApp.Delete(roleId));
		}

		[HttpGet("token/{token}")]
		public async Task<ApiReturn<Role>> GetRoleByToken(string token)
		{
			return await ApiReturn<Role>.Execute(_roleApp.GetRoleByToken(token));
		}
	}
}