using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.UsersParticipantCampaigns;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{

	[Route("users/{userId}/campaigns/{campaignId}/callcenter")]
	public class CampaignCallcenterController : Controller
	{
		private readonly IUserParticipatCampaignApp participationCampaignApp;
		public CampaignCallcenterController(IUserParticipatCampaignApp participationCampaignApp)
		{
			this.participationCampaignApp = participationCampaignApp;
		}

		[HttpPut("contact")]
		public async Task<ApiReturn<bool>> UpdateUserContactById(Guid campaignId, Guid userId, [FromBody] UsersCallcenterAction callcenterActionRegister)
		{
			return await ApiReturn.Execute(participationCampaignApp.UpdateUserContactById(campaignId, userId, callcenterActionRegister));
		}
	}
}