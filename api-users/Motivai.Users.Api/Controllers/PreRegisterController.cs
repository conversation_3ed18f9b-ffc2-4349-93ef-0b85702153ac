using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.PreRegister;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{
	[Route("/preregister")]
	public class PreRegisterController
	{
		private readonly IPreRegisterApp _preRegisterApp;
		public PreRegisterController(IPreRegisterApp preRegisterApp)
		{
			_preRegisterApp = preRegisterApp;
		}

		[HttpPost]
		public async Task<ApiReturn<bool>> Create([FromBody] PreRegisteredUser preUser)
		{
			return await ApiReturn<bool>.Execute(_preRegisterApp.Create(preUser));
		}

		[HttpPost("campaigns/{campaignId}/validate")]
		public async Task<ApiReturn<bool>> ValidateUserData(Guid campaignId, [FromBody] PreRegisteredUser userData)
		{
			return await ApiReturn<bool>.Execute(_preRegisterApp.ValidateUserData(campaignId, userData));
		}

		[HttpGet("campaigns/{campaignId}")]
		public async Task<ApiReturn<List<PreRegisteredUser>>> Find(Guid campaignId, [FromQuery] string document, [FromQuery] bool? integrated, [FromQuery] bool? integrationError, [FromQuery] int? skip, [FromQuery] int? limit)
		{
			return await ApiReturn<List<PreRegisteredUser>>.Execute(_preRegisterApp.Find(campaignId, document, integrated, integrationError, skip, limit));
		}

		[HttpGet("{id}")]
		public async Task<ApiReturn<PreRegisteredUser>> FindById(Guid id)
		{
			return await ApiReturn<PreRegisteredUser>.Execute(_preRegisterApp.FindById(id));
		}

		[HttpPut("{id}/approve")]
		public async Task<ApiReturn<bool>> Approve(Guid id, [FromBody] PreRegisteredUser preUser)
		{
			return await ApiReturn<bool>.Execute(_preRegisterApp.Approve(id, preUser));
		}

		[HttpPut("{id}/refuse")]
		public async Task<ApiReturn<bool>> Refuse(Guid id, [FromBody] PreRegisteredUser preUser)
		{
			return await ApiReturn<bool>.Execute(_preRegisterApp.Refuse(id, preUser));
		}

		[HttpGet("campaigns/{campaignId}/users/{document}/exist")]
		public async Task<ApiReturn<dynamic>> ValidateDocument(Guid campaignId, string document)
		{
			return await ApiReturn<dynamic>.Execute(_preRegisterApp.ValidateDocument(campaignId, document));
		}

		[HttpPut("{id}/integration/retry")]
		public async Task<ApiReturn<bool>> Reintegrate(Guid id)
		{
			return await ApiReturn<bool>.Execute(_preRegisterApp.Reintegrate(id));
		}

		[HttpPut("campaigns/{campaignId}/users/{document}/integrate")]
		public async Task<ApiReturn> IntegrateByDocument(Guid campaignId, string document)
		{
			return await ApiReturn.Execute(_preRegisterApp.IntegrateByDocument(campaignId, document));
		}
	}
}