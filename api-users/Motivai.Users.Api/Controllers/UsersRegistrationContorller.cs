using Microsoft.AspNetCore.Mvc;
using Motivai.Users.Domain.IApp.MyAccount;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Model;
using System;
using Motivai.Users.Domain.Models.Registration;
using System.Collections.Generic;
using Motivai.Users.Domain.IApp.Users;

namespace Motivai.Users.Api.Controllers
{
	[Route("users")]
	[Produces("application/json")]
	public class UsersRegistrationContorller : Controller
	{
		private readonly IParticipantRegistrationManager registrationManager;
		private readonly IUserMetadataApp userMetadataApp;

		public UsersRegistrationContorller(IParticipantRegistrationManager registrationManager, IUserMetadataApp userMetadataApp)
		{
			this.registrationManager = registrationManager;
			this.userMetadataApp = userMetadataApp;
		}

		[HttpGet("{userId}/campaigns/{campaignId}/registration/verify")]
		public async Task<ApiReturn<bool>> VerifyIfNeedsCompleteRegistration(Guid userId, Guid campaignId)
		{
			return await ApiReturn<bool>.Execute(registrationManager.VerifyIfNeedsCompleteRegistration(userId, campaignId));
		}

		[HttpGet("{userId}/campaigns/{campaignId}/registration")]
		public async Task<ApiReturn<ParticipantRegistrationData>> GetRegistrionDataToComplete(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantRegistrationData>.Execute(registrationManager.GetRegistrionDataToComplete(userId, campaignId));
		}

		[HttpPut("{userId}/campaigns/{campaignId}/registration")]
		public async Task<ApiReturn<bool>> CompleteRegistration(Guid userId, Guid campaignId, [FromBody] ParticipantRegistrationData participantData)
		{
			return await ApiReturn<bool>.Execute(registrationManager.CompleteRegistration(userId, campaignId, participantData));
		}

		[HttpPost("{userId}/campaigns/{campaignId}/participants/{participantId}/metadata")]
		public async Task<ApiReturn<bool>> SaveUserMetadata(Guid userId, Guid campaignId, Guid participantId, [FromBody] Dictionary<string, string> userMetadata)
		{
			return await ApiReturn<bool>.Execute(userMetadataApp.SaveUserMetadata(userId, campaignId, participantId, userMetadata, "RegistrationUpdate"));
		}
	}
}