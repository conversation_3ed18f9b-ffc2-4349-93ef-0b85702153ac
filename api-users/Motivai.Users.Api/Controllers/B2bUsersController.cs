using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Model;
using Motivai.Users.Domain.Entities.B2b;
using Motivai.Users.Domain.IApp.B2b;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Users.Api.Controllers
{

	[Route("b2b/users")]
	[Produces("application/json")]
	public class B2bUsersController : BaseApiController
	{
		private readonly IShopKeeperApp _shopKeeperApp;

		public B2bUsersController(IShopKeeperApp shopKeeperApp)
		{
			_shopKeeperApp = shopKeeperApp;
		}

		[HttpPost]
		public async Task<ApiReturn<Shopkeeper>> <PERSON>reate([FromBody] Shopkeeper shopkeeper)
		{
			return await ApiReturn<Shopkeeper>.Execute(_shopKeeperApp.Create(shopkeeper, BuId));
		}

		[HttpPut("{shopkeeperId}")]
		public async Task<ApiReturn<bool>> Update(Guid shopkeeperId, [FromBody] Shopkeeper shopkeeper)
		{
			return await ApiReturn<bool>.Execute(_shopKeeperApp.Update(shopkeeperId, shopkeeper, BuId));
		}

		[HttpGet("search")]
		public async Task<ApiReturn<List<Shopkeeper>>> Search([FromQuery] string document, [FromQuery] string name, [FromQuery] int skip = 0, [FromQuery] int limit = 100)
		{
			return await ApiReturn<List<Shopkeeper>>.Execute(_shopKeeperApp.Find(document, name, skip, limit, BuId));
		}

		[HttpGet("{shopkeeperId}")]
		public async Task<ApiReturn<Shopkeeper>> FindById(Guid shopkeeperId)
		{
			return await ApiReturn<Shopkeeper>.Execute(_shopKeeperApp.FindById(shopkeeperId, BuId));
		}

		[HttpGet]
		public async Task<ApiReturn<List<ShopKeeperInfo>>> SearchShopKeepers([FromQuery] string term)
		{
			return await ApiReturn<List<ShopKeeperInfo>>.Execute(_shopKeeperApp.SearchShopKeepers(term, BuId));
		}

		[HttpGet("{shopkeeperId}/campaigns")]
		public async Task<ApiReturn<List<Guid>>> FindShopkeeperCampaigns(Guid shopkeeperId)
		{
			return await ApiReturn<List<Guid>>.Execute(_shopKeeperApp.FindShopkeeperCampaigns(shopkeeperId, BuId));
		}

		[HttpGet("{userId}/campaigns/{campaignId}")]
		public async Task<ApiReturn<ParticipantInfo>> GetOrCreateParticipantInfo(Guid userId, Guid campaignId)
		{
			return await ApiReturn<ParticipantInfo>.Execute(_shopKeeperApp.GetOrCreateParticipantInfo(userId, campaignId));
		}

		[HttpGet("{userId}/address/main")]
		public async Task<ApiReturn<Address>> GetAddressByUser(Guid userId)
		{
			return await ApiReturn<Address>.Execute(_shopKeeperApp.GetAddressByUser(userId, BuId));
		}
	}
}