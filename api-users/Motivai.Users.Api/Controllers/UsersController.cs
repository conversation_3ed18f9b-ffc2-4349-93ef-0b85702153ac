using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IApp.UsersManagement;
using Motivai.Users.Domain.Models.Import;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Services.Participants;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.IApp.AccountOperators;

namespace Motivai.Users.Api.Controllers
{
	///<summary>
	/// Controller para operações de pesquisa em usuários.
	///</summary>
	[Produces("application/json")]
	[Route("users")]
	public class UsersController : Controller
	{
		private readonly IUserApp _userApp;
		private readonly IUserFinder userFinder;
		private readonly IUserParentFinder userParentFinder;
		private readonly ParticipantMover participantMover;
		private readonly IUserManagementApp userManagementApp;
		private readonly IAccountOperatorManager accountOperatorManager;

		public UsersController(IUserApp userApp, IUserFinder userFinder,
				IUserParentFinder userParentFinder, ParticipantMover participantMover,
				IUserManagementApp userManagementApp, IAccountOperatorManager accountOperatorManager)
		{
			_userApp = userApp;
			this.userFinder = userFinder;
			this.userParentFinder = userParentFinder;
			this.participantMover = participantMover;
			this.userManagementApp = userManagementApp;
			this.accountOperatorManager = accountOperatorManager;
		}

		[HttpGet("document/{document}/info")]
		public async Task<ApiReturn<ParticipantInfo>> GetUserInfoByDocument(string document)
		{
			return await ApiReturn<ParticipantInfo>.Execute(_userApp.GetUserInfoByDocument(document));
		}

		[HttpGet("document/{document}/campaigns")]
		public async Task<ApiReturn<UserCampaigns>> FindUserCampaignsByDocument(string document, [FromQuery] bool onlyActive = true)
		{
			return await ApiReturn.Execute(userFinder.FindUserCampaignsByDocument(document, onlyActive));
		}

		[HttpGet("{id}/document")]
		public async Task<ApiReturn<dynamic>> GetDocumentBy(Guid id)
		{
			return await ApiReturn<dynamic>.Execute(_userApp.GetDocumentBy(id));
		}

		[HttpGet("document/{document}/search")]
		public async Task<ApiReturn<dynamic>> SearchPersonByDocument(string document, [FromQuery] Guid campaignId = default(Guid))
		{
			return await ApiReturn.Execute(_userApp.SearchPersonByDocument(document, campaignId));
		}

		[HttpGet("cpf/{cpf}")]
		public async Task<ApiReturn<User>> GetUsuarioPeloCpf(string cpf)
		{
			return await ApiReturn<User>.Execute(_userApp.GetUserByCpf(cpf));
		}

		[HttpGet("participants/{participantId}/campaigns/{campaignId}/status")]
		public async Task<ApiReturn<bool>> VerifyParticipantActiveAtCampaign(Guid participantId, Guid campaignId)
		{
			return await ApiReturn<bool>.Execute(_userApp.IsParticipantActiveAtCampaign(participantId, campaignId));
		}

		[HttpGet("{businessType}")]
		public async Task<ApiReturn<List<UserCampaignsInfo>>> SearchParticipantInBusinessType(UserBusinessType businessType, [FromQuery] string document = null, [FromQuery] string name = null)
		{
			return await ApiReturn<List<UserCampaignsInfo>>.Execute(_userApp.SearchParticipantInBusinessType(businessType, document, name));
		}

		[HttpPost("ids/info")]
		public async Task<ApiReturn<List<UserCampaignsInfo>>> GetUsersByListOfIds([FromBody] dynamic obj)
		{
			if (obj == null)
			{
				throw MotivaiException.ofValidation("Informe a lista de IDs para buscar");
			}

			if (obj.ids == null)
			{
				throw MotivaiException.ofValidation("Informe a lista de IDs para buscar");
			}

			IEnumerable<Guid> list = (obj.ids as IEnumerable<dynamic>).Select(x => new Guid(x.ToString()));
			return await ApiReturn<List<UserCampaignsInfo>>.Execute(_userApp.GetUsersByListOfIds(list.ToList()));
		}

		[HttpGet("{userId}/campaigns/{campaignId}/accountrepresentative")]
		public async Task<ApiReturn<AccountRepresentative>> GetAccountRepresentative(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(_userApp.GetAccountRepresentative(userId, campaignId));
		}

		[HttpGet("{userId}/document/{document}/exists")]
		public async Task<ApiReturn<bool>> ExistUserWithDocument(Guid userId, string document)
		{
			return await ApiReturn<bool>.Execute(_userApp.ExistUserWithDocument(userId, document));
		}

		[HttpPost("{userId}/campaigns/{campaignId}/move")]
		public async Task<ApiReturn<bool>> MoveParticipant(Guid userId, Guid campaignId, [FromBody] UserMoveRequest moveRequest)
		{
			return await ApiReturn.Execute(participantMover.MoveParticipant(campaignId, userId, moveRequest));
		}

		[HttpGet("campaigns/{campaignId}/parents")]
		public async Task<ApiReturn<List<UserParentDetails>>> GetUsersParentsByIds(Guid campaignId, [FromQuery] string usersIds)
		{
			return await ApiReturn.Execute(userParentFinder.GetUsersParentsByIds(campaignId, ParseIds(usersIds)));
		}

		[HttpGet("campaigns/{campaignId}/parentshierarchy/verify")]
		public async Task<ApiReturn<bool>> VerifyIfUsersHasSameParentHierarchy(Guid campaignId,
				[FromQuery] Guid firstUserId, [FromQuery] Guid secondUserId)
		{
			return await ApiReturn.Execute(userParentFinder.VerifyIfUsersHasSameParentHierarchy(campaignId, firstUserId, secondUserId));
		}

		[HttpPut("{userId}/campaigns/{campaignId}/firstaccess/reset")]
		public async Task<ApiReturn<bool>> ResetParticipantFirstAcess(Guid userId, Guid campaignId, [FromBody] dynamic payload)
		{
			return await ApiReturn.Execute(userManagementApp.ResetParticipantFirstAccess(userId, campaignId, payload.operationUser?.ToObject<OperationUser>()));
		}

		[HttpPost("{userId}/campaigns/{campaignId}/cards")]
		public async Task<ApiReturn<bool>> SaveAppCard(Guid userId, Guid campaignId, [FromBody] Card card)
		{
			return await ApiReturn.Execute(_userApp.SaveUserAppCard(campaignId, userId, card));
		}

		[HttpGet("{userId}/campaigns/{campaignId}/cards")]
		public async Task<ApiReturn<List<Card>>> GetAppCards(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(_userApp.GetAppCards(campaignId, userId));
		}

		[HttpGet("{userId}/campaigns/{campaignId}/cards/active")]
		public async Task<ApiReturn<List<Card>>> GetActiveAppCards(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(_userApp.GetActiveAppCards(campaignId, userId));
		}

		[HttpPut("{userId}/campaigns/{campaignId}/cards/reset")]
		public async Task<ApiReturn<bool>> ResetAppCards(Guid userId, Guid campaignId)
		{
			return await ApiReturn.Execute(_userApp.ResetAppCards(campaignId, userId));
		}

		[HttpGet("{userId}/campaigns/{campaignId}/children")]
		public async Task<ApiReturn<List<UserCampaignsInfo>>> GetUserChildrenParticipants(Guid userId, Guid campaignId) {
			return await ApiReturn.Execute(_userApp.GetUserChildrenParticipants(userId, campaignId));
		}

		[HttpGet("accountoperator/document/{document}/campaigns/{campaignId}/accessibleaccounts")]
		public async Task<ApiReturn<List<OperatorAcessibleAccounts>>> GetOperatorAcessibleAccounts(string document, Guid campaignId) {
			return await ApiReturn.Execute(accountOperatorManager.GetOperatorAcessibleAccounts(document, campaignId));
		}

		private static List<Guid> ParseIds(string usersIds)
		{
			if (string.IsNullOrEmpty(usersIds))
			{
				return null;
			}

			try
			{
				return usersIds.Split(',')
					.AsEnumerable()
					.Select(Guid.Parse)
					.ToList();
			}
			catch
			{
				return null;
			}
		}
	}
}