﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Motivai.SharedKernel.Domain.Repository;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Repository;
using Motivai.Users.App;
using Motivai.Users.App.Account;
using Motivai.Users.App.AccountOperators;
using Motivai.Users.App.AnonymousSession;
using Motivai.Users.App.Authenticators;
using Motivai.Users.App.B2b;
using Motivai.Users.App.CampaignsGroups;
using Motivai.Users.App.MyAccount;
using Motivai.Users.App.OperatorMigration;
using Motivai.Users.App.Searches;
using Motivai.Users.App.Security;
using Motivai.Users.App.Users;
using Motivai.Users.App.UsersAdministration;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IApp.AccountOperators;
using Motivai.Users.Domain.IApp.AnonymousSession;
using Motivai.Users.Domain.IApp.Ath;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IApp.Authenticators;
using Motivai.Users.Domain.IApp.B2b;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IApp.MyAccount;
using Motivai.Users.Domain.IApp.MyAccounts;
using Motivai.Users.Domain.IApp.OperatorMigration;
using Motivai.Users.Domain.IApp.ParticipantHistoryFiles;
using Motivai.Users.Domain.IApp.ParticipantManagementApp;
using Motivai.Users.Domain.IApp.PreRegister;
using Motivai.Users.Domain.IApp.Roles;
using Motivai.Users.Domain.IApp.Searches;
using Motivai.Users.Domain.IApp.Security;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IApp.UsersAdministrations;
using Motivai.Users.Domain.IApp.UsersManagement;
using Motivai.Users.Domain.IApp.UsersParticipantCampaigns;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.AnonymousSession;
using Motivai.Users.Domain.IRepository.B2b;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.CampaignsGroups;
using Motivai.Users.Domain.IRepository.ClientIntegrations;
using Motivai.Users.Domain.IRepository.Correios;
using Motivai.Users.Domain.IRepository.Credify;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.ExtraServices;
using Motivai.Users.Domain.IRepository.Orders;
using Motivai.Users.Domain.IRepository.ParticipantHistoryFiles;
using Motivai.Users.Domain.IRepository.PreRegister;
using Motivai.Users.Domain.IRepository.Roles;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.IRepository.Sms;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.IRepository.Users.Searches;
using Motivai.Users.Domain.IRepository.UsersAdministrations;
using Motivai.Users.Domain.IRepository.UsersAdministrations.AccessLog;
using Motivai.Users.Domain.IRepository.UsersAdministrations.ActionLog;
using Motivai.Users.Domain.IRepository.UsersManagement;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Services.Authentication;
using Motivai.Users.Domain.Services.Notifications;
using Motivai.Users.Domain.Services.Participants;
using Motivai.Users.Domain.Services.Participants.Creation;
using Motivai.Users.Domain.Services.Security;
using Motivai.Users.Domain.Services.Users.Creation;
using Motivai.Users.Repository.Repositories.AccessLog;
using Motivai.Users.Repository.Repositories.AnonymousSession;
using Motivai.Users.Repository.Repositories.B2b;
using Motivai.Users.Repository.Repositories.Campaigns;
using Motivai.Users.Repository.Repositories.CampaignsGroups;
using Motivai.Users.Repository.Repositories.ClientsIntegrations;
using Motivai.Users.Repository.Repositories.Correios;
using Motivai.Users.Repository.Repositories.Credify;
using Motivai.Users.Repository.Repositories.Email;
using Motivai.Users.Repository.Repositories.ExtraServices;
using Motivai.Users.Repository.Repositories.ParticipantHistoryFiles;
using Motivai.Users.Repository.Repositories.PreRegister;
using Motivai.Users.Repository.Repositories.Searches;
using Motivai.Users.Repository.Repositories.Security;
using Motivai.Users.Repository.Repositories.Sms;
using Motivai.Users.Repository.Repositories.UserParticipantCampaigns;
using Motivai.Users.Repository.Repositories.Users;
using Motivai.Users.Repository.Repositories.Users.AccountOperators;
using Motivai.Users.Repository.Repositories.UsersAdministration.AccessLog;
using Motivai.Users.Repository.Repositories.UsersAdministration.Action;
using Motivai.Users.Repository.Repositories.UsersManagement;
using Motivai.Users.Repository.Repositorys.Campaigns;
using Motivai.Users.Repository.Repositorys.Orders;
using Motivai.Users.Repository.Repositorys.Roles;
using Motivai.Users.Repository.Repositorys.Transactions;
using Motivai.Users.Repository.Repositorys.UserParticipantCampaigns;
using Motivai.Users.Repository.Repositorys.Users;
using Motivai.Users.Repository.Repositorys.UsersAdministration;

namespace Motivai.Users.Api
{
    public class Startup : ApiBaseStartup
    {
        public Startup(IWebHostEnvironment env) : base(env) { }

        public override void ConfigureIoC(IServiceCollection services)
        {
            // Crypt
            services.AddSingleton<ICryptography, Cryptography>();

            // Apps
            services.AddSingleton<IAuthenticatorApp, AuthenticatorApp>();
            services.AddSingleton<IUserApp, UserApp>();
            services.AddSingleton<IUserFinder, UserFinder>();
            services.AddSingleton<IUserParentFinder, UserParentFinder>();
            services.AddSingleton<IUserAdministrationApp, UserAdministrationApp>();
            services.AddSingleton<IUserAdministrationActionLogApp, UserAdministrationActionLogApp>();
            services.AddSingleton<IUserParticipatCampaignApp, UserParticipatCampaignApp>();
            services.AddSingleton<IMyAccountApp, MyAccountApp>();
            services.AddSingleton<IAccountBalanceReader, AccountBalanceReader>();
            services.AddSingleton<FirstAccessCompleter>();
            services.AddSingleton<IRoleApp, RoleApp>();
            services.AddSingleton<IPreRegisterApp, PreRegisterApp>();
            services.AddSingleton<IUsersB2bPortalApp, UsersB2bPortalApp>();

            services.AddSingleton<IParticipantRegistrationManager, ParticipantRegistrationManager>();
            services.AddSingleton<IOperatorMigrationApp, OperatorMigrationApp>();
            services.AddSingleton<ParticipantImporter, ParticipantImporter>();
            services.AddSingleton<ParticipantsGroupService>();
            services.AddSingleton<ParticipantMover>();
            services.AddSingleton<IParticipantSearchApp, ParticipantSearchApp>();

            services.AddSingleton<ICampaignAuthenticator, CampaignAuthenticator>();
            services.AddSingleton<CampaignIntegrationAuthenticator>();
            services.AddSingleton<CatalogAuthenticator>();
            services.AddSingleton<MobileAuthenticator>();
            services.AddSingleton<AccountOperatorAuthenticator>();
            services.AddSingleton<CampaignPlatformSsoAuthentication>();
            services.AddSingleton<CallcenterAuthenticator>();
            services.AddSingleton<CampaignGroupSsoAuthenticator>();

            services.AddSingleton<IUserManagementApp, UserManagementApp>();

            services.AddSingleton<IParticipantManagementApp, ParticipantManagementApp>();

            services.AddSingleton<IUsersAccountsOperatorsActionsHistoryApp, UsersAccountsOperatorsActionsHistoryApp>();

            services.AddSingleton<IAccountOperatorManager, AccountOperatorManager>();
            services.AddSingleton<IAccountAddressManager, AccountAddressManager>();
            services.AddSingleton<CampaignB2bParticipantCreator>();
            services.AddSingleton<CampaignIntegrationParticipantCreator>();
            services.AddSingleton<CampaignIntegrationUserCreator>();
            services.AddSingleton<CampaignParticipantConfigurator>();
            services.AddSingleton<ParticipantPrivacySettingsManager>();
            services.AddSingleton<ParticipantNotificator>();

            services.AddSingleton<IAnonymousSessionApp, AnonymousSessionApp>();

            // Repositories
            services.AddSingleton<IBlockListedIpRepository, BlockListedIpRepository>();

            services.AddSingleton<ICampaignRepository, CampaignRepository>();
            services.AddSingleton<ICampaignGoalsRepository, CampaignGoalsRepository>();
            services.AddSingleton<ICampaignTargetAudienceRepository, CampaignTargetAudienceRepository>();

            services.AddSingleton<ITransactionApiRepository, TransactionApiRepository>();
            services.AddSingleton<IUserParticipationCampaignRepository, UserParticipationCampaignRepository>();
            services.AddSingleton<IUserRepository, UserRepository>();
            services.AddSingleton<IUserAdministrationRepository, UserAdministrationRepository>();
            services.AddSingleton<IUserAdministrationActionLogRepository, UserAdministrationActionLogRepository>();
            services.AddSingleton<IUserAdministrationAccessLogRepository, UserAdministrationAccessLogRepository>();
            services.AddSingleton<IOrderRepository, OrderRepository>();
            services.AddSingleton<IRoleRepository, RoleRepository>();
            services.AddSingleton<IEmailRepository, EmailRepository>();
            services.AddSingleton<ISmsRepository, SmsRepository>();
            services.AddSingleton<IShopKeeperApp, ShopKeeperApp>();
            services.AddSingleton<IShopkeeperRepository, ShopkeeperRepository>();
            services.AddSingleton<IPreRegisterRepository, PreRegisterRepository>();
            services.AddSingleton<IAccessLogRepository, AccessLogRepository>();
            services.AddSingleton<IAuthenticationLogRepository, AuthenticationLogRepository>();
            services.AddSingleton<ICredifyRepository, CredifyRepository>();
            services.AddSingleton<IUsersB2bPortalRepository, UsersB2bPortalRepository>();
            services.AddSingleton<ICampaignTermsAcceptanceRepository, CampaignTermsAcceptanceRepository>();
            services.AddSingleton<ICardRepository, CardRepository>();
            services.AddSingleton<ICashbackRepository, CashbackRepository>();
            services.AddSingleton<IUsersAccountsOperatorsActionsHistoryRepository, UsersAccountsOperatorsActionsHistoryRepository>();
            services.AddSingleton<IAccountOperatorRepository, AccountOperatorRepository>();
            services.AddSingleton<IParticipantHistoryFileRepository, ParticipantHistoryFileRepository>();
            services.AddSingleton<IParticipantHistoryFileApp, ParticipantHistoryFileApp>();
            services.AddSingleton<IAccessControlRegisterApp, AccessControlRegisterApp>();
            services.AddSingleton<IPasswordRecoveryService, PasswordRecoveryService>();
            services.AddSingleton<IUsersCallcenterActionRegisterRepository, UsersCallcenterActionRegisterRepository>();

            services.AddSingleton<IUserManagementRepository, UserManagementRepository>();
            services.AddSingleton<IParticipantRegistrationLogRepository, ParticipantRegistrationLogRepository>();
            services.AddSingleton<AddressVerifier>();
            services.AddSingleton<IUserExternalClientApp, UserExternalClientApp>();
            services.AddSingleton<ICorreiosRepository, CorreiosRepository>();

            services.AddSingleton<SecurityCodeSender>();
            services.AddSingleton<IUserConfigurationManager, UserConfigurationManager>();

            services.AddSingleton<IUserConfigurationRepository, UserConfigurationRepository>();
            services.AddSingleton<IUserMetadataApp, UserMetadataApp>();
            services.AddSingleton<IUserMetadataValueRepository, UserMetadataValueRepository>();
            services.AddSingleton<IParticipantSearchRepository, ParticipantSearchRepository>();

            services.AddSingleton<IAnonymousSessionRepository, AnonymousSessionRepository>();

            // Integrações com clientes (serão movidas para outro projeto quando tiver mais uma integração)
            services.AddSingleton<IClientIntegrations, ClientIntegrations>();

            services.AddSingleton<IBusinessUnitCommomRepository, BusinessUnitCommomRepository>();
            services.AddSingleton<ICampaignExtraServicesRepository, CampaignExtraServicesRepository>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // DevicesManager
            services.AddSingleton<IUserDeviceManagerApp, UserDeviceManagerApp>();

            services.AddSingleton<ICampaignsGroupsApp, CampaignsGroupsApp>();
            services.AddSingleton<ICampaignsGroupsRepository, CampaignsGroupsRepository>();
            services.AddSingleton<ITransactionManager, TransactionManager>();
            services.AddSingleton<IAuthenticationMfaManager, AuthenticationMfaManager>();
        }
    }
}
