﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <PublishWithAspNetCoreTargetManifest>false</PublishWithAspNetCoreTargetManifest>
  </PropertyGroup>
  <ItemGroup>
    <Content Update="nlog.config" CopyToOutputDirectory="Always" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Motivai.Users.App\Motivai.Users.App.csproj" />
    <ProjectReference Include="..\Motivai.Users.Domain\Motivai.Users.Domain.csproj" />
    <ProjectReference Include="..\Motivai.Users.Repository\Motivai.Users.Repository.csproj" />
  </ItemGroup>
 </Project>
