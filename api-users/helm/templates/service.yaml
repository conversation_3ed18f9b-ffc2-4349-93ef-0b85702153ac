apiVersion: v1
kind: Service
metadata:
  name: {{ include "application.fullname" . }}
  namespace: {{ include "application.namespace" . }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "application.selectorLabels" . | nindent 4 }}
