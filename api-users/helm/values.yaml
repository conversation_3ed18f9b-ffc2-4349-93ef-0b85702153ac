# papel da aplicação (frontend, api, consumer, scheduler)
component: api
# quando o componente faz parte de um conjunto de outros componentes para realizar uma capacidade de negócio
partOf: ""

# imagem do container
image:
  repository: 677276094924.dkr.ecr.us-east-1.amazonaws.com/motivai/api-users
  pullPolicy: IfNotPresent
  tag: "" # default usa Chart.AppVersion

# labels para o pod
podLabels: {}

# qtde mínima de réplicas
replicaCount: 1
# tempo mínimo para aguardar antes de atualizar o status para running
minReadySeconds: 5

# variáveis de ambientes simples
env:
  - name: ASPNETCORE_ENVIRONMENT
    value: ""

# variáveis de ambientes a partir de outros objetos
envFrom:
  - configMapRef:
      name: apis-urls-dotnet-configmap
  - secretRef:
      name: mongodb-secret
  - secretRef:
      name: redis-secret

# recursos configurados para o container
resources:
  requests:
    cpu: 50m
    memory: 128Mi
  limits:
    cpu: 100m
    memory: 256Mi

# configuração de autoscale (https://kubernetes.io/docs/concepts/workloads/autoscaling/)
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# probes do container
livenessProbe:
  httpGet:
    path: /healthy/ping
    port: http
  initialDelaySeconds: 30
  periodSeconds: 60
  timeoutSeconds: 10
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /healthy/ping
    port: http
  initialDelaySeconds: 30
  periodSeconds: 15
  timeoutSeconds: 10
  successThreshold: 1
  failureThreshold: 3

# service (quando componente não expõe API altere para `{}`)
service:
  type: ClusterIP
  port: 80
  targetPort: 80

# ingress (apenas aplicações expostas para Internet)
ingress: {}
