using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users.FirstAccess;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.Entities.Terms;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.FirstAccess;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Services.Participants;
using Motivai.Users.Domain.Services.Security;
using Motivai.Users.Domain.Services.Validations;


namespace Motivai.Users.App.Account
{
	public class FirstAccessCompleter {
		private readonly ICampaignRepository campaignRepository;
		private readonly IUserRepository userRepository;
		private readonly ICampaignTermsAcceptanceRepository campaignTermsRepository;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
		private readonly ParticipantPrivacySettingsManager participantPrivacySettingsManager;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;
		private readonly ParticipantMover participantMover;
        private readonly IEmailRepository emailRepository;

		public FirstAccessCompleter(ICampaignRepository campaignRepository, IUserRepository userRepository,
				ICampaignTermsAcceptanceRepository campaignTermsRepository, ICampaignsGroupsApp campaignsGroupsApp,
				ParticipantPrivacySettingsManager participantPrivacySettingsManager,
				IParticipantRegistrationLogRepository participantRegistrationLogRepository,
				ParticipantMover participantMover, IEmailRepository emailRepository)
		{
			this.campaignRepository = campaignRepository;
			this.userRepository = userRepository;
			this.campaignTermsRepository = campaignTermsRepository;
			this.campaignsGroupsApp = campaignsGroupsApp;
			this.participantPrivacySettingsManager = participantPrivacySettingsManager;
			this.participantRegistrationLogRepository = participantRegistrationLogRepository;
			this.participantMover = participantMover;
			this.emailRepository = emailRepository;
		}

		public async Task<FirstAccessResult> RegisterPartialFirstAccess(Guid userId, Guid campaignId,
			FirstAccessAcceptancesModel userData)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("ID do usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("ID da campanha inválido.");

			var campaignSettings = await GetCampaignSettings(campaignId);

			var user = await FindAndValidateUser(userId, campaignId);
			var participant = FindAndValidateParticipant(user, campaignId);

			participant.UpdatePrivacySettings(userData.CampaignAcceptancesResult.PrivacyPolicy,
				userData.AcceptedCampaignCommunications, userData.AcceptedPartnerCommunications);

			await RegisterRegulationsAndPrivacyAcceptance(campaignSettings, user, participant,
					userData.CampaignAcceptancesResult, userData.LocationInfo);

			await RegisterFirstAccessAndSendToCoalition(user, participant, userData.KeepFirstAccess);

			AuthenticationMfa(userData.AuthenticationMfaSettings, participant);

			return FirstAccessResult.OfCompleted();
		}

		public async Task<FirstAccessResult> RegisterFirstAccess(Guid userId, Guid campaignId, FirstAccessDataModel userData)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("ID do usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("ID da campanha inválido.");
			if (userData == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

			userData.Validate();

			var campaignSettings = await GetCampaignSettings(campaignId);

			var user = await FindAndValidateUser(userId, campaignId);
			var participant = FindAndValidateParticipant(user, campaignId);

			if (campaignSettings.Parametrizations.EnableDocumentFieldAtFirstAccess)
			{
				await ValidateExistingParticipantWithDocument(campaignId, campaignSettings, userId, participant, userData);
			}

			var firstAccessSettings = await campaignRepository.GetFirstAccessSettings(campaignId);
			bool participantMigratedToAnotherUser = false;

			if (firstAccessSettings.EnablePersonalData)
            {
                // Atualiza os dados do cadastro
                if (userData.IsPessoaJuridica)
                {
                    if (campaignSettings.Parametrizations.EnableDocumentFieldAtFirstAccess)
                    {
                        LoggerFactory.GetLogger().Info("FirstAccess - Cmp {0} - Usr {1} - Doc {2} - Login {3} - Verificando se já existe outro usuário com documento",
                                campaignId, userId, userData.GetDocument(), participant.Login);

                        if (!string.IsNullOrEmpty(userData.Cnpj))
                        {
                            var preExistingUser = await this.userRepository.GetByDocument(userData.Cnpj);
                            if (preExistingUser != null && userId != preExistingUser.Id)
                            {
                                participantMigratedToAnotherUser = await MigrateCurrentParticipantToTargetUser(
                                    campaignId, userId, userData.GetDocument(), participant.Login, preExistingUser
                                );
                                user = await this.userRepository.GetUserAndParticipant(preExistingUser.Id, campaignId);
                                participant = user.GetParticipantByCampaign(campaignId);
                            }
                        }
                        user.Cnpj = userData.Cnpj;
                        user.Type = PersonType.Juridica;
                    }
                    if (firstAccessSettings.PersonalData.CompanyName.CanEdit)
                    {
                        user.CompanyName = userData.CompanyName;
                    }
                    user.StateInscriptionExempt = userData.StateInscriptionExempt;

                    if (campaignSettings.Parametrizations.EnableAccountRepresentative)
                    {
                        if (userData.AccountRepresentative == null && campaignSettings.Parametrizations.OriginTaxIdForInvoicing == OriginTaxIdForInvoicing.AccountDocument)
                        {
                            throw MotivaiException.ofValidation("O representante da conta deve ser preenchido.");
                        }
                        if (userData.AccountRepresentative != null)
                        {
                            userData.AccountRepresentative.Validate();
                            participant.AccountRepresentative = userData.AccountRepresentative;
                        }
                    }

                    if (user.StateInscriptionExempt)
                    {
                        user.StateInscription = null;
                        user.StateInscriptionUf = null;
                    }
                    else
                    {
                        user.StateInscription = userData.StateInscription;
                        user.StateInscriptionUf = userData.StateInscriptionUf;
                    }

                }
                else
                {
                    if (campaignSettings.Parametrizations.EnableDocumentFieldAtFirstAccess)
                    {
                        if (!string.IsNullOrEmpty(userData.Cpf))
                        {
                            LoggerFactory.GetLogger().Info("FirstAccess - Cmp {0} - Usr {1} - Doc {2} - Login {3} - Verificando se já existe outro usuário com documento",
                                    campaignId, userId, userData.GetDocument(), participant.Login);

                            var preExistingUser = await this.userRepository.GetByDocument(userData.Cpf);
                            if (preExistingUser != null && userId != preExistingUser.Id)
                            {
                                participantMigratedToAnotherUser = await MigrateCurrentParticipantToTargetUser(
                                    campaignId, userId, userData.GetDocument(), participant.Login, preExistingUser
                                );
                                user = await this.userRepository.GetUserAndParticipant(preExistingUser.Id, campaignId);
                                participant = user.GetParticipantByCampaign(campaignId);
                            }
                        }
                        user.Type = PersonType.Fisica;
                        user.Cpf = userData.Cpf;
                    }
                    user.Rg = userData.Rg;
                    user.Gender = userData.Gender;
                    user.MaritalStatus = userData.MaritalStatus;
                    if (userData.BirthDate != null)
                        user.BirthDate = userData.BirthDate.ToDate();
                }
                if (firstAccessSettings.PersonalData.Name.CanEdit)
                {
                    user.Name = userData.Name;
                }

                AuthenticationMfa(userData.AuthenticationMfaSettings, participant);
            }

            // Contatos
            if (firstAccessSettings.EnableEmails || firstAccessSettings.EnableTelephones)
			{
				participant.Contact = userData.Contact;
				if (participant.Contact != null)
				{
					participant.Contact.MainEmail = participant.GetMainEmail();
				}
			}

			user.GpInf = userData.AcceptedCampaignCommunications;
			user.GpPartnerInf = userData.AcceptedPartnerCommunications;

			participant.UpdatePrivacySettings(userData.CampaignAcceptancesResult?.PrivacyPolicy, userData.AcceptedCampaignCommunications,
				userData.AcceptedPartnerCommunications);

			// Enderecos
			if (participant.Addresses == null)
				participant.Addresses = new List<Address>(2);

			if (firstAccessSettings.EnableHomeAddress && userData.HomeAddress != null)
			{
				ValidateAndUpdateAddress(campaignSettings, firstAccessSettings, participant, userData.HomeAddress);
			}

			if (firstAccessSettings.EnableBusinessAddress && userData.CommercialAddress != null && !string.IsNullOrEmpty(userData.CommercialAddress.Cep))
			{
				ValidateAndUpdateAddress(campaignSettings, firstAccessSettings, participant, userData.CommercialAddress);
			}

			if (firstAccessSettings.EnablePassword)
			{
				var newPassword = new Senha(userData.Password, userData.PasswordConfirmation);
				if (participant.Password != null && newPassword.Descricao.Equals(participant.Password.Descricao))
				{
					throw MotivaiException.ofValidation("Senha digitada não pode ser igual a senha cadastrada.");
				}
				participant.Password = newPassword;
			}

			user.Validate();

			await userRepository.UpdateFirstAccessData(user, participant, firstAccessSettings);

			await RegisterRegulationsAndPrivacyAcceptance(campaignSettings, user, participant,
					userData.CampaignAcceptancesResult, userData.LocationInfo);

			await RegisterFirstAccessAndSendToCoalition(user, participant, userData.KeepFirstAccess);

			if (participantMigratedToAnotherUser)
			{
				return FirstAccessResult.OfCompletedWithMigration(user.GetDocument(), user.Id, participant.Id);
			}

			try
			{
				await this.emailRepository.SendFirstAccessNotification(campaignId, user.Id, user.Name, participant.GetMainEmail(), participant.GetMobilePhone());
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Ocorreu um erro ao disparar email de notificação do firstAccess");
			}

			return FirstAccessResult.OfCompleted();
		}

        private void AuthenticationMfa(ParticipantAuthenticationMfaSettings AuthenticationMfaSettings, UserParticipantCampaign participant)
        {
            if (AuthenticationMfaSettings != null && AuthenticationMfaSettings.AuthenticationMfaFormat != null)
            {
                participant.AuthenticationMfaSettings = AuthenticationMfaSettings;
            }
        }

        private async Task RegisterFirstAccessAndSendToCoalition(User user, UserParticipantCampaign participant,
			bool keepFirstAccess)
		{
			if (!keepFirstAccess)
			{
				// Desabilita o primeiro acesso
				participant.RegisterFirstAccess();
				await userRepository.UpdateFirstAccessFlag(user, participant);
			}
			await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.REGISTRATION_UPDATE, Guid.Empty,
				participant.CampaignId, user.Id, participant.Id, Guid.Empty);
		}

		private async Task<CampaignSettingsModel> GetCampaignSettings(Guid campaignId)
		{
			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			if (!campaignSettings.Parametrizations.EnableFirstAccess)
			{
				throw MotivaiException.ofValidation("Primeiro acesso está desabilitado nesta campanha.");
			}

			return campaignSettings;
		}

		private async Task<User> FindAndValidateUser(Guid userId, Guid campaignId)
		{
			var user = await userRepository.GetUserAndParticipant(userId, campaignId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");
			if (!user.UsersParticipantCampaign.Any())
				throw MotivaiException.ofValidation("Usuário não possuí nenhuma campanha ativa, entre em contato com o atendimento.");
			return user;
		}

		private UserParticipantCampaign FindAndValidateParticipant(User user, Guid campaignId)
		{
			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não relacionado com a campanha informada. Campanha: " + campaignId);
			if (!participant.FirstAccess)
			{
				throw MotivaiException.ofValidation("Participante já efetuou o primeiro acesso.");
			}
			return participant;
		}

		private async Task<bool> MigrateCurrentParticipantToTargetUser(Guid campaignId, Guid currentUserId,
			string userDocument, string userLogin, User targetUser)
		{
			LoggerFactory.GetLogger().Warn("FirstAccess - Cmp {0} - Usr {1} - Doc {2} - Login {3} - Migrando para usuário {4}",
					campaignId, currentUserId, userDocument, userLogin, targetUser.Id);

			var migrated = await this.participantMover.MoveParticipant(campaignId, currentUserId, new UserMoveRequest(targetUser.Id, userDocument));

			LoggerFactory.GetLogger().Warn("FirstAccess - Cmp {0} - Usr {1} - Participante migrado para usuário {2}",
					campaignId, currentUserId, targetUser.Id);
			return migrated;
		}

		private async Task ValidateExistingParticipantWithDocument(Guid campaignId, CampaignSettingsModel campaignSettings,
				Guid userId, UserParticipantCampaign participant, FirstAccessDataModel userData) {
			LoggerFactory.GetLogger().Warn("FirstAccess - Cmp {0} - Usr {1} - Doc {2} - Login {3} - Validando existência do CPF/CNPJ preenchido na campanha",
				campaignId, userId, userData.GetDocument(), participant.Login);

			User existingUser;
			if (userData.IsPessoaFisica) {
				existingUser = await userRepository.GetByCpf(userData.GetDocument());
			} else {
				existingUser = await userRepository.GetByCnpj(userData.GetDocument());
			}
			// Se encontrou um User que não seja o atual
			if (existingUser != null && existingUser.Id != userId) {
				LoggerFactory.GetLogger().Error("FirstAccess - Cmp {0} - Usr {1} - Doc {2} - Login {3} - Encontrado outro user com o mesmo CPF/CNPJ: {4}",
					campaignId, userId, userData.GetDocument(), participant.Login, existingUser.Id);

				var existingParticipant = existingUser.GetParticipantByCampaign(campaignId);
				if (existingParticipant != null) {
					LoggerFactory.GetLogger().Error("FirstAccess - Cmp {0} - Usr {1} - Doc {2} - Login {3} - CPF/CNPJ já registrado na campanha: Usr {4} - Login {5}",
						campaignId, userId, userData.GetDocument(), participant.Login, existingUser.Id, existingParticipant.Login);
					throw MotivaiException.ofValidation("Já existe outro participante na campanha com este CPF/CNPJ, por favor, entre em contato com o atendimento.");

				} else if (campaignSettings.Parametrizations.EnableParticipantMigrationAtFirstAccess == true) {
					LoggerFactory.GetLogger().Warn("FirstAccess - Cmp {0} - Usr {1} - Migração de participante habilitada na campanha",
						campaignId, userId);
				} else {
					throw MotivaiException.ofValidation("Já existe outro usuário com este CPF/CNPJ, por favor, entre em contato com o atendimento.");
				}
			}
		}

		private void ValidateAndUpdateAddress(CampaignSettingsModel campaignSettings, ParticipantData participantSettings,
				UserParticipantCampaign participant, Address address)
		{
			CampaignParticipantValidations.ValidateFirstAccessAddress(address, participantSettings, campaignSettings);
			Address existingAddress = null;

			if (address.Id != Guid.Empty && participant.Addresses.Any(a => a.Id == address.Id)) {
				existingAddress = participant.Addresses.FirstOrDefault(a => a.Id == address.Id);
			} else {
				existingAddress = participant.Addresses.FirstOrDefault(a => a.AddressName == address.AddressName);
			}

			if (existingAddress != null) {
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - FirstAccess - Address {2} - Alteração de Endereço - De '{3} - {4} - {5} - {6} - {7}' para '{8} - {9} - {10} - {11} - {12}'",
					participant.CampaignId, participant.UserId, existingAddress.Id,
					existingAddress.AddressName, existingAddress.Cep, existingAddress.Street, existingAddress.Number, existingAddress.City,
					address.AddressName, address.Cep, address.Street, address.Number, address.City);
				existingAddress.CopyFrom(address);

			} else {
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - FirstAccess - Address {2} - Cadastrando Endereço - '{3} - {4} - {5} - {6} - {7}'",
					participant.CampaignId, participant.UserId, address.Id, address.AddressName, address.Cep,
					address.Street, address.Number, address.City);
				participant.AddAddress(address, false);
			}
		}

		private async Task RegisterRegulationsAndPrivacyAcceptance(CampaignSettingsModel campaignSettings, User user,
			UserParticipantCampaign participant, CampaignAcceptancesResult campaignAcceptancesResult,
			LocationInfo locationInfo)
		{
			var log = ParticipantRegistrationLog.OfAction(RegistrationAction.FIRST_ACCESS, user.Id,
							participant.CampaignId, null, locationInfo);
			await participantRegistrationLogRepository.RegisterLog(log);

			await participantPrivacySettingsManager.RegisterPrivacySettingsChange(user.Id, participant.CampaignId,
				participant.PrivacySettings, locationInfo);

			bool wasRegistered = await RegisterAcceptanceTerms(participant.CampaignId, campaignSettings, user,
					campaignAcceptancesResult);
			if (!wasRegistered && campaignSettings.Parametrizations.AcceptRegulationRequired)
				throw MotivaiException.ofValidation("Não foi possível registrar o aceite do regulamento, por favor, tente novamente.");
		}

		private async Task<bool> RegisterAcceptanceTerms(Guid campaignId, CampaignSettingsModel campaignSettings,
			User user, CampaignAcceptancesResult campaignAcceptancesResult)
		{
			// Registra o aceite
			if (campaignSettings.Parametrizations.EnableCampaignWithMultiRegulations)
			{
				LoggerFactory.GetLogger().Info("FirstAccess - Cmp {0} - Usr {1} - Verificando os regulamentos aceitos - qtde {2}",
						campaignId, user.Id, campaignAcceptancesResult.RegulationAcceptance.AcceptanceTerms?.Count);
				if (campaignAcceptancesResult.RegulationAcceptance.AcceptanceTerms.IsNullOrEmpty())
				{
					throw MotivaiException.ofValidation("Nenhum regulamento aceito para registrar.");
				}

				foreach (var term in campaignAcceptancesResult.RegulationAcceptance.AcceptanceTerms)
				{
					if (term == null || string.IsNullOrEmpty(term.RegulationId) || !term.Accepted)
						continue;
					await campaignTermsRepository.RegisterAcceptance(TermsAcceptance.ofRegulation(user.Id, campaignId, term.RegulationId, term.Version));
				}
				return true;
			}
			LoggerFactory.GetLogger().Info("FirstAccess - Cmp {0} - Usr {1} - RegId {2} - Vers {3} - Registrando o regulamento aceito",
					campaignId, user.Id, campaignAcceptancesResult.RegulationAcceptance.RegulationId, campaignAcceptancesResult.RegulationAcceptance.Version);
			if (string.IsNullOrEmpty(campaignAcceptancesResult.RegulationAcceptance.RegulationId))
			{
				LoggerFactory.GetLogger().Error("FirstAccess - Cmp {0} - Usr {1} - RegId {2} - Vers {3} - ID do regulamento inválido",
						campaignId, user.Id, campaignAcceptancesResult.RegulationAcceptance.RegulationId, campaignAcceptancesResult.RegulationAcceptance.Version);
				return false;
			}

			await campaignTermsRepository.RegisterAcceptance(TermsAcceptance.ofRegulation(user.Id, campaignId,
					campaignAcceptancesResult.RegulationAcceptance.RegulationId, campaignAcceptancesResult.RegulationAcceptance.Version));
			return true;
		}
	}
}