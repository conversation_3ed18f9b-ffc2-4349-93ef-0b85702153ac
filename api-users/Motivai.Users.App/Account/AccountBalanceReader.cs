using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;

namespace Motivai.Users.App.Account {
	public class AccountBalanceReader : IAccountBalanceReader {
		private readonly IUserParticipationCampaignRepository participantRepository;
		private readonly ICampaignRepository campaignRepository;
		private readonly ITransactionApiRepository transactionRepository;

		public AccountBalanceReader(IUserParticipationCampaignRepository participantRepository,
			ICampaignRepository campaignRepository, ITransactionApiRepository transactionRepository) {
			this.participantRepository = participantRepository;
			this.campaignRepository = campaignRepository;
			this.transactionRepository = transactionRepository;
		}

		public async Task<decimal> GetBalance(Guid userId, Guid campaignId) {
			var participantId = await participantRepository.GetParticipantId(userId, campaignId);
			if (participantId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			var balance = await transactionRepository.GetBalance(campaignId, userId);
			await participantRepository.UpdateParticipantBalance(userId, participantId, balance);
			return balance;
		}

		public async Task<decimal> GetBalanceByRankings(Guid userId, Guid campaignId, List<Guid> lowestRankings) {
			var rankingId = await participantRepository.GetParticipantRanking(userId, campaignId);
			if (!rankingId.HasValue)
				return 0;
			var rankingsToUse = await campaignRepository.GetRankingsParents(campaignId, lowestRankings);
			return await transactionRepository.GetBalanceByRankings(campaignId, userId, rankingsToUse);
		}

		public async Task<decimal> GetReservedBalanceFor(Guid userId, Guid campaignId, Product product) {
			return await transactionRepository.GetReservedBalanceFor(userId, campaignId, product);
		}
	}
}