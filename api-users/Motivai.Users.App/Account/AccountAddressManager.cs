using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IApp.MyAccount;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.MyAccount.Registration;
using Motivai.Users.Domain.Services.Validations;

namespace Motivai.Users.App.MyAccount {
    public class AccountAddressManager : IAccountAddressManager {
        private readonly IUserRepository userRepository;
        private readonly ICampaignRepository campaignRepository;
        private readonly IUserParticipationCampaignRepository participationCampaignRepository;
        private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;
        private readonly ICampaignsGroupsApp campaignsGroupsApp;

        public AccountAddressManager(IUserRepository userRepository, ICampaignRepository campaignRepository,
            IUserParticipationCampaignRepository participationCampaignRepository,
            IParticipantRegistrationLogRepository participantRegistrationLogRepository,
            ICampaignsGroupsApp campaignsGroupsApp) {
            this.userRepository = userRepository;
            this.campaignRepository = campaignRepository;
            this.participationCampaignRepository = participationCampaignRepository;
            this.participantRegistrationLogRepository = participantRegistrationLogRepository;
            this.campaignsGroupsApp = campaignsGroupsApp;
        }

        private async Task RegisterLog(ParticipantRegistrationLog participantRegistrationLog) {
            try {
                await participantRegistrationLogRepository.RegisterLog(participantRegistrationLog);
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "RegistrationLog", "Erro ao registrar log de ação nos dados cadastrais");
            }
        }

        public async Task<Address> GetAddressById(Guid userId, Guid campaignId, Guid addressId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("ID da campanha inválido.");
            if (addressId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Endereço.");
            return await participationCampaignRepository.GetAddressById(userId, campaignId, addressId);
        }

        public async Task<string> GetAddressCep(Guid userId, Guid campaignId, Guid adressId) {
            if (adressId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Endereço");

            return await participationCampaignRepository.GetAddressCep(userId, campaignId, adressId);
        }

        public async Task<List<Address>> GetAllAddresses(Guid userId, Guid campaignId, bool? main = false) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("ID da campanha inválido.");

            return await participationCampaignRepository.GetAllAddresses(userId, campaignId, main);
        }

        public async Task<List<Address>> GetActiveAddresses(Guid userId, Guid campaignId, bool? main = false) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("ID da campanha inválido.");

            return await participationCampaignRepository.GetAddresses(userId, campaignId, main);
        }

        public async Task<List<ResumedAddress>> GetResumedAddresses(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("ID da campanha inválido.");

            return await participationCampaignRepository.GetResumedAddresses(userId, campaignId);
        }

        public async Task<Address> GetMainAddressOrFirst(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("ID da campanha inválido.");
            return await participationCampaignRepository.GetMainAddress(userId, campaignId);
        }

        public async Task<Address> SaveAddress(Guid userId, Guid campaignId, AddressUpdate addressUpdate) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            // Verificar se a campanha permite cadastrar/alterar o endereco
            var campaignSettings = await campaignRepository.GetSettings(campaignId);
            if (!campaignSettings.Parametrizations.AllowChangeShippingAddress)
                throw MotivaiException.of(ErrorType.Business, "Não é permitido cadastrar ou alterar os endereços de entrega.");

            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (addressUpdate == null)
                throw MotivaiException.ofValidation("Dados do endereço são obrigatórios.");

            var address = addressUpdate.ToEntity();

            CampaignParticipantValidations.ValidateAddress(address, campaignSettings);

            var user = await userRepository.Get(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado.");
            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não possuí participa da campanha informada.");
            address.UserId = userId;

            LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Cadastrando Endereço - '{2} - {3} - {4} - {5} - {6}'",
                campaignId, userId, address.AddressName, address.Cep, address.Street, address.Number, address.City);
            participant.AddAddress(address);

            var wasUpdate = await userRepository.SaveAddress(user.Id, participant.Id, participant.Addresses);

            // Registra a ação
            await RegisterLog(ParticipantRegistrationLog.OfAddressUpdate(RegistrationAction.NEW_ADDRESS,userId, campaignId, addressUpdate.AccountOperator, address.Id,
                addressUpdate.LocationInfo));

            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ADDRESS_UPDATE,
                    Guid.Empty, campaignId, user.Id, participant.Id, Guid.Empty
                );

                return address;
            }
            return null;
        }

        public async Task<bool> UpdateAddress(Guid userId, Guid campaignId, Guid addressId, AddressUpdate addressUpdate) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválido.");

            // Verificar se a campanha permite cadastrar/alterar o endereco
            var campaignSettings = await campaignRepository.GetSettings(campaignId);

            if (!campaignSettings.Parametrizations.AllowChangeShippingAddress)
                throw MotivaiException.of(ErrorType.Business, "Não é permitido cadastrar ou alterar os endereços de entrega.");

            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (addressId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do endereço inválido.");

            if (addressUpdate == null)
                throw MotivaiException.ofValidation("Dados do endereço são obrigatórios.");

            var user = await userRepository.Get(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado.");
            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não possuí participa da campanha informada.");

            var dbAddress = participant.Addresses.FirstOrDefault(a => a.Id == addressId);
            if (dbAddress == null)
                throw MotivaiException.ofValidation("Endereço não encontrado.");

            // Verificar se a campanha permite cadastrar/alterar o endereco
            if (dbAddress.CreatedByIntegration && !campaignSettings.Parametrizations.AllowChangeIntegrationShippingAddress)
                throw MotivaiException.of(ErrorType.Business, "Não é permitido alterar os endereços de entrega pré-cadastrados.");

            LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Address {2} - Alteração de Endereço - De '{3} - {4} - {5} - {6} - {7}' para '{8} - {9} - {10} - {11} - {12}'",
                campaignId, userId, dbAddress.Id, dbAddress.AddressName, dbAddress.Cep, dbAddress.Street, dbAddress.Number, dbAddress.City,
                addressUpdate.AddressName, addressUpdate.Cep, addressUpdate.Street, addressUpdate.Number, addressUpdate.City);

            dbAddress.CopyFrom(addressUpdate);

            CampaignParticipantValidations.ValidateAddress(dbAddress, campaignSettings);

            // Manter apenas um endereço principal
            if (addressUpdate.MainAddress) {
                participant.SetMainAddress(dbAddress);
            } else {
                participant.VerifyMainAddress();
            }

            var wasUpdate = await userRepository.UpdateAddress(user.Id, participant.Id, dbAddress);
            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ADDRESS_UPDATE,
                    dbAddress.Id, campaignId, user.Id, participant.Id, Guid.Empty
                );
            }
             // Registra a ação
            await RegisterLog(ParticipantRegistrationLog.OfAddressUpdate(RegistrationAction.UPDATE_ADDRESS, userId, campaignId, addressUpdate.AccountOperator, dbAddress.Id,
                addressUpdate.LocationInfo));

            return wasUpdate;
        }

        public async Task<bool> DeleteAdress(Guid userId, Guid campaignId, Guid addressId) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("ID da campanha inválido.");

            // Verificar se a campanha permite cadastrar/alterar o endereco
            var campaignSettings = await campaignRepository.GetSettings(campaignId);
            if (!campaignSettings.Parametrizations.AllowChangeShippingAddress)
                throw MotivaiException.of(ErrorType.Business, "Não é permitido cadastrar ou alterar os endereços de entrega.");

            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário inválido.");
            if (addressId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do endereço inválido.");

            var user = await userRepository.GetUserAndParticipant(userId, campaignId);
			var participant = user.GetParticipantByCampaign(campaignId);

            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado.");
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não possuí participa da campanha informada.");

            var dbAddress = participant.Addresses.FirstOrDefault(a => a.Id == addressId);
            if (dbAddress == null)
                throw MotivaiException.ofValidation("Endereço não encontrado.");

            dbAddress.Active = false;
			dbAddress.MainAddress = false;
			dbAddress.UpdateDate = DateTime.UtcNow;
			//user.Validate(); TODO: Verificar pois o admin nao manda o login

            LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Address {2} - Excluíndo Endereço", campaignId, userId, addressId);

            var wasUpdate = await participationCampaignRepository.DeleteAddress(userId, participant.Id, dbAddress);
            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ADDRESS_UPDATE,
                    addressId, campaignId, userId, participant.Id, Guid.Empty
                );
            }
            return wasUpdate;
        }
    }
}
