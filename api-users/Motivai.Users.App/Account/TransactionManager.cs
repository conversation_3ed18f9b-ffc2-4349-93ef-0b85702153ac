using System;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.Transactions;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;

namespace Motivai.Users.App.Account
{
    public class TransactionManager : ITransactionManager {
        private readonly ITransactionApiRepository _transactionRepository;
        private readonly IUserParticipationCampaignRepository _participationCampaignRepository;

        public TransactionManager(ITransactionApiRepository _transactionRepository, IUserParticipationCampaignRepository _participationCampaignRepository) {
            this._transactionRepository = _transactionRepository;
            this._participationCampaignRepository = _participationCampaignRepository;
        }

		public async Task<BlockedPointsSummary> GetBlockedPoints(Guid userId, Guid campaignId)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			return await _transactionRepository.GetPointsBlocked(campaignId, userId);
		}

		public async Task<List<TransactionDetailsModel>> GetLastAccumulations(Guid userId, Guid campaignId)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			return await _transactionRepository.GetLastAccumulations(campaignId, userId);
		}

		public async Task<List<TransactionDetailsModel>> GetLastRedeems(Guid userId, Guid campaignId)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			return await _transactionRepository.GetLastRedemptions(campaignId, userId);
		}

		public async Task<ExpiringPointsSummary> GetExpiringPoints(Guid userId, Guid campaignId)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			return await _transactionRepository.GetExpiringPoints(campaignId, userId);
		}

		public async Task<BalanceResumeModel> LoadSummary(Guid userId, Guid campaignId)
		{
			var participant = await GetParticipantAndValidateCampaign(userId, campaignId);

			var balance = await _transactionRepository.GetBalance(campaignId, userId);
			var nextPointsExpire = await _transactionRepository.GetFirstPointsToExpire(campaignId, userId);
			var nextPointsLocked = await _transactionRepository.GetFirstPointsToUnblock(campaignId, userId);

			return new BalanceResumeModel
			{
				LastAccess = participant.GetLastAccess(),
				Balance = balance,
				ExpiringPoints = nextPointsExpire,
				BlockedPoints = nextPointsLocked
			};
		}
		private async Task<UserParticipantCampaign> GetParticipantAndValidateCampaign(Guid userId, Guid campaignId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Deve ser informado a campanha.");

			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Deve ser informado o usuário.");

			var participant = await _participationCampaignRepository.Get(userId, campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			return participant;
		}

        public async Task<ExtractModel> GetTransactions(Guid userId, Guid campaignId,
				TransactionType? transactionType = null, TransactionOrigin? transactionOrigin = null,
				DateTime? startDate = null, DateTime? endDate = null,
				int? skip = null, int? limit = null)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			// var participantId = await _participationCampaignRepository.GetParticipantId(userId, campaignId);

			var extractApi = await _transactionRepository.GetExtract(campaignId, userId, null,
				transactionType, transactionOrigin, startDate, endDate, skip, limit);

			if (extractApi == null) return null;

			return new ExtractModel
			{
				TotalAmount = extractApi.TotalAmount,
				Transactions = extractApi.Transactions == null ? null :
					extractApi.Transactions.Select(CreateTransactionRegister)
					.OrderByDescending(t => t.ProcessingDate)
					.ToList()
			};
		}

		public async Task<ExtractModel> GetExtractWithBlockedTransactions(Guid userId, Guid campaignId,
				TransactionType? transactionType = null, TransactionOrigin? transactionOrigin = null,
				DateTime? startDate = null, DateTime? endDate = null,
				int? skip = null, int? limit = null)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");

			var extractApi = await _transactionRepository.GetExtractWithBlockedTransactions(campaignId, userId, null,
				transactionType, transactionOrigin, startDate, endDate, skip, limit);

			if (extractApi == null) return null;

			return new ExtractModel
			{
				TotalAmount = extractApi.TotalAmount,
				Transactions = extractApi.Transactions == null ? null :
					extractApi.Transactions.Select(CreateTransactionRegister)
					.OrderByDescending(t => t.ProcessingDate)
					.ToList()
			};
		}

		public async Task<ExtractModel> GetBlockedTransactionsExtract(Guid userId, Guid campaignId,
				TransactionType? transactionType = null, TransactionOrigin? transactionOrigin = null,
				DateTime? startDate = null, DateTime? endDate = null,
				int? skip = null, int? limit = null)
		{
			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");

			var extractApi = await _transactionRepository.GetBlockedTransactionsExtract(campaignId, userId, null,
				transactionType, transactionOrigin, startDate, endDate, skip, limit);

			if (extractApi == null) return null;

			return new ExtractModel
			{
				TotalAmount = extractApi.TotalAmount,
				Transactions = extractApi.Transactions == null ? null :
					extractApi.Transactions.Select(CreateTransactionRegister)
					.OrderByDescending(t => t.ProcessingDate)
					.ToList()
			};
		}


		private static ExtractRegister CreateTransactionRegister(TransactionDetailsModel transaction)
		{
			var register = new ExtractRegister
			{
				ProcessingDate = transaction.ProcessingDate,
				Amount = transaction.TotalPoints,
				Blocked = transaction.Blocked,
				ExtraData = transaction.ExtraData
			};

			switch (transaction.TransactionType)
			{
				case TransactionType.Credit:
					register.Description = string.Format("Crédito - {0}", transaction.Description);
					break;
				case TransactionType.Debt:
					if (transaction.Blocked == true)
						register.Description = transaction.Description;
					else if (string.IsNullOrEmpty(transaction.OrderNumber))
						register.Description = "Débito - " + transaction.Description;
					else
						register.Description = $"{transaction.Description} - Pedido {transaction.OrderNumber}";
					break;
				default:
					register.Description = transaction.Description;
					break;
			}
			return register;
		}
    }
}