using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Time;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.MyAccount;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.Models.Registration;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.App.MyAccount
{
	public class ParticipantRegistrationManager : IParticipantRegistrationManager
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly IUserRepository userRepository;
		private readonly IUserMetadataApp userMetadata;

		public ParticipantRegistrationManager(ICampaignRepository campaignRepository, IUserRepository userRepository, IUserMetadataApp userMetadata)
		{
			this.campaignRepository = campaignRepository;
			this.userRepository = userRepository;
			this.userMetadata = userMetadata;
		}

		public async Task<bool> VerifyIfNeedsCompleteRegistration(Guid userId, Guid campaignId)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			var participant = await userRepository.GetParticipantByCampaign(userId, campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			if (participant.FirstAccess)
				return true;

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			if (campaignSettings.Parametrizations.RegistrationReviewPeriodicity == Periodicity.Never)
			{
				return false;
			}

			if (participant.NextUpdateDate == null || !participant.NextUpdateDate.HasValue)
				return true;

			return participant.NextUpdateDate.Value <= DateTime.UtcNow;
		}

		public async Task<ParticipantRegistrationData> GetRegistrionDataToComplete(Guid userId, Guid campaignId)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			var firstAccessSettings = await campaignRepository.GetFirstAccessSettings(campaignId);

			var user = await userRepository.Get(userId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			if (firstAccessSettings.EnableMetadataAtFirstAccess == true)
			{
				var usersMetadata = await userMetadata.GetUsersMetadataValue(campaignId, userId);
				return ParticipantRegistrationData.Of(user, participant, usersMetadata);

			}

			return ParticipantRegistrationData.Of(user, participant);
		}

		public async Task<bool> CompleteRegistration(Guid userId, Guid campaignId, ParticipantRegistrationData participantData)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (participantData == null)
				throw MotivaiException.ofValidation("Preencha os dados corretamente para continuar.");
			participantData.Validate();

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			var user = await userRepository.Get(userId);
			UserParticipantCampaign participant = null;

			if (user == null)
			{
				user = User.CreateUser(campaignSettings.Type, participantData.Document);
			}
			else
			{
				participant = user.GetParticipantByCampaign(campaignId);
			}
			if (participant == null)
			{
				participant = UserParticipantCampaign.CreateForCampaign(campaignId, campaignSettings, user);
			}

			if (user.Type == PersonType.Juridica)
			{
				user.Name = participantData.TradeName;
				user.CompanyName = participantData.CompanyName;
				user.StateInscriptionExempt = participantData.StateInscriptionExempt;
				user.StateInscription = participantData.StateInscription;
				user.StateInscriptionUf = participantData.StateInscriptionUf;

				if (campaignSettings.Parametrizations.EnableAccountRepresentative)
				{
					if (campaignSettings.Parametrizations.OriginTaxIdForInvoicing == OriginTaxIdForInvoicing.AccountDocument && participantData.AccountRepresentative == null)
					{
						throw MotivaiException.ofValidation("O representante da conta deve ser preenchido.");
					}

					participantData.AccountRepresentative.Validate();
					participant.AccountRepresentative = participantData.AccountRepresentative;
				}
			}

			participant.Contact = participantData.Contact;
			if (participant.FirstAccess)
			{
				participant.RegisterFirstAccess();
			}
			UpdateAddress(participant, participantData);
			user.GpInf = participantData.GpInfo;
			user.GpPartnerInf = participantData.GpPartnerInfo;


			participant.UpdateNextReviewDate(campaignSettings.Parametrizations.RegistrationReviewPeriodicity);
			participant.UpdateLastAccess();

			await userRepository.Save(user);
			return true;
		}

		private void UpdateAddress(UserParticipantCampaign participant, ParticipantRegistrationData participantData)
		{
			if (participant.Addresses == null)
			{
				participant.Addresses = new List<Address>() {
					participantData.BusinessAddress
				};
			}
			else
			{
				var address = participant.Addresses.FirstOrDefault(a => a.AddressName == participantData.BusinessAddress.AddressName);
				if (address == null)
				{
					participant.AddAddress(participantData.BusinessAddress);
				}
				else
				{
					address.CopyFrom(participantData.BusinessAddress);
				}
			}
		}
	}
}