using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.Wallets;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.App.Account
{
    public class UserConfigurationManager : IUserConfigurationManager {
        private readonly IUserRepository userRepository;
        private readonly IUserConfigurationRepository configurationRepository;

        public UserConfigurationManager(IUserRepository userRepository, IUserConfigurationRepository configurationRepository) {
            this.userRepository = userRepository;
            this.configurationRepository = configurationRepository;
        }

        public async Task<bool> GetUserTransactionalConfiguration(Guid userId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (!await userRepository.IsActive(userId)) {
                throw MotivaiException.ofValidation("Usuário não encontrado.");
            }
            return await configurationRepository.HasTransactionalPasswordConfigured(userId);
        }

        public async Task<bool> ConfigureTransactionalPassword(Guid userId, TransactionalPassword transactionalPassword) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (transactionalPassword == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            transactionalPassword.Validate();

            if (await configurationRepository.HasTransactionalPasswordConfigured(userId)) {
                throw MotivaiException.ofValidation("Senha transacional já foi configurada.");
            }

            transactionalPassword.Password = Hashing.HashString(transactionalPassword.Password);
            return await configurationRepository.ConfigureTransactionalPassword(userId, transactionalPassword);
        }

        public async Task<bool> ValidateTransactionalPassword(Guid userId, TransactionalPassword transactionalPassword) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (transactionalPassword == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            transactionalPassword.Validate();
            var hashedPassword = await configurationRepository.GetTransactionalPassword(userId);
            return Hashing.ValidateHash(transactionalPassword.Password, hashedPassword);
        }
    }
}