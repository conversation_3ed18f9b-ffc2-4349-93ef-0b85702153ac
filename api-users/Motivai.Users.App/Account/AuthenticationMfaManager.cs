using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.IApp.Account;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.Models.Security;

namespace Motivai.Users.App.Account
{
    public class AuthenticationMfaManager : IAuthenticationMfaManager
    {
        private static readonly string PARTICIPANT_MFA_KEY_PREFIX = "usr-mfa-token";
        private static readonly int CODE_EXPIRATION = 300;

        private readonly IEmailRepository emailRepository;
        private readonly IUserRepository userRepository;
        private readonly ICache cache;

        public AuthenticationMfaManager(IEmailRepository emailRepository, IUserRepository userRepository, ICache cache)
        {
            this.emailRepository = emailRepository;
            this.userRepository = userRepository;
            this.cache = cache;
        }

        public async Task<ParticipantAuthenticationMfaSettings> FindAuthenticationMfaSettings(Guid campaignId, Guid userId)
        {
            var authMFaData = await userRepository.FindAuthenticationMfaSettings(campaignId, userId);
            if (authMFaData == null)
            {
                throw MotivaiException.ofValidation("Configurações de autenticação não encontradas");
            }

            return authMFaData;
        }

        public async Task<ParticipantAuthenticationMfaSettings> FindAuthenticationMfaSettingsToValidate(Guid campaignId, Guid userId)
        {
            var authMFaData = await userRepository.FindAuthenticationMfaSettings(campaignId, userId);
            if (authMFaData == null)
            {
                throw MotivaiException.ofValidation("Configurações de autenticação não encontradas");
            }

            if (!string.IsNullOrEmpty(authMFaData.MobilePhone))
            {
                authMFaData.MobilePhone = Obfuscator.ObfuscateMobilePhone(authMFaData.MobilePhone);
            }

            if (!string.IsNullOrEmpty(authMFaData.Email))
            {
                authMFaData.Email = Obfuscator.ObfuscateEmail(authMFaData.Email);
            }

            return authMFaData;
        }


        public Task<bool> UpdateAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            if (userAuthenticationMfa == null)
            {
                throw MotivaiException.ofValidation("Configurações de autenticação inválidas");
            }

            userAuthenticationMfa.Validate();

            return userRepository.UpdateAuthenticationMfaToken(campaignId, userId, userAuthenticationMfa);
        }

        public async Task<bool> SendAuthenticationMfaTokenToValidate(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            if (userAuthenticationMfa == null)
            {
                throw MotivaiException.ofValidation("Configurações de autenticação inválidas");
            }

            var authMFaData = await FindAuthenticationMfaSettings(campaignId, userId);

            return await SendAuthenticationMfaToken(campaignId, userId, authMFaData);
        }

        public async Task<bool> SendAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            var securityToken = GenerateVerificationCode();
            try
            {
                await cache.Set(PARTICIPANT_MFA_KEY_PREFIX + userId, securityToken, CODE_EXPIRATION);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Envio", $"Erro ao cachear código de segurança MFA do usuário {userId}", true);
                throw MotivaiException.of("Não foi possível gerar o código de segurança, por favor, tente novamente.", ex);
            }

            var participantInfo = await userRepository.GetUserData(userId, campaignId);
            if (participantInfo == null)
            {
                throw MotivaiException.ofValidation("Dados do participante não encontrado");
            }

            try
            {
                switch (userAuthenticationMfa.AuthenticationMfaFormat)
                {
                    case ParticipantAuthenticationMfaFormat.EMAIL_ONLY:
                        return await emailRepository.SendToken(campaignId, userId, participantInfo.Name, userAuthenticationMfa.Email, userAuthenticationMfa.MobilePhone, SecurityTokenSendMethod.EMAIL, securityToken);
                    case ParticipantAuthenticationMfaFormat.SMS_ONLY:
                        return await emailRepository.SendToken(campaignId, userId, participantInfo.Name, userAuthenticationMfa.Email, userAuthenticationMfa.MobilePhone, SecurityTokenSendMethod.SMS, securityToken);
                    default:
                        throw MotivaiException.ofValidation("Formato de autenticação inválido");
                }
            }
            catch (MotivaiException ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Validação", $"Erro durante validação do código de segurança para o usuário {userId}", true);
                throw ex;
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Envio", $"Erro ao enviar código de segurança para o usuário {userId}", true);
                throw MotivaiException.of("Não foi possível enviar o código de segurança, por favor, tente novamente.", ex);
            }
        }

        public async Task<bool> ValidateAuthenticationMfaToken(Guid campaignId, Guid userId, ValidateMfaToken userToken)
        {
            if (userToken == null || string.IsNullOrEmpty(userToken.Token))
            {
                throw MotivaiException.ofValidation("Código de segurança não preenchido");
            }
            string token = userToken.Token;

            try
            {
                var securityToken = await cache.Get<string>(PARTICIPANT_MFA_KEY_PREFIX + userId);
                if (securityToken == null || string.IsNullOrEmpty(securityToken))
                {
                    throw MotivaiException.ofValidation("Código de segurança expirado");
                }

                if (securityToken.Equals(token))
                {
                    if (userToken.Setup)
                    {
                        return await UpdateAuthenticationMfaToken(campaignId, userId, new ParticipantAuthenticationMfaSettings()
                        {
                            AuthenticationMfaFormat = userToken.AuthenticationMfaFormat,
                            Email = userToken.Email,
                            MobilePhone = userToken.MobilePhone,
                            NeedSetupAuthenticationMfa = false,
                            SetupAuthenticationMfaDate = DateTime.UtcNow
                        });
                    }

                    return true;
                }

                throw MotivaiException.ofValidation("Código de segurança inválido");
            }
            catch (MotivaiException ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Validação", $"Erro durante validação do código de segurança para o usuário {userId}", true);
                throw ex;
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Validação", $"Erro durante validação do código de segurança para o usuário {userId}", true);
                throw MotivaiException.of("Não foi possível validar o código de segurança, por favor, tente novamente.", ex);
            }
        }

        private string GenerateVerificationCode()
        {
            var random = new Random();
            var verificationCode = random.Next(100000, 999999).ToString();

            return verificationCode;
        }
    }
}