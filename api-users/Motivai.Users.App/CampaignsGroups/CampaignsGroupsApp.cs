using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IRepository.CampaignsGroups;

namespace Motivai.Users.App.CampaignsGroups
{
	public class CampaignsGroupsApp : ICampaignsGroupsApp
	{
		private readonly ICampaignsGroupsRepository campaignsGroupsRepository;

		public CampaignsGroupsApp(ICampaignsGroupsRepository campaignsGroupsRepository)
		{
			this.campaignsGroupsRepository = campaignsGroupsRepository;
		}

		public Task UpdateAccountOperatorCampaignsInCoalitionIfNeeded(EventType eventType, Guid originId, Guid campaignId,
			Guid accountOperatorId = default, OperationOrigin? creationOrigin = default)
		{
			return UpdateCampaignsInCoalitionIfNeeded(eventType, originId, campaignId,
				Guid.Empty, Guid.Empty, accountOperatorId, creationOrigin);
		}

		public async Task UpdateCampaignsInCoalitionIfNeeded(EventType eventType, Guid originId, Guid campaignId,
			Guid userId, Guid participantId, Guid? accountOperatorId = default, OperationOrigin? creationOrigin = default)
		{
			var isInColatition = await this.campaignsGroupsRepository.VerifyIfCampaignIsInCoalition(campaignId);
			if (isInColatition)
			{
				try
				{
					await this.UpdateParticipantDataInGroupCampaigns(eventType, originId, campaignId, userId, participantId, accountOperatorId, creationOrigin);
				}
				catch (Exception ex)
				{
					LoggerFactory.GetLogger().Error(
						"Event {0} - OriginId {1} - Cmp {2} - Usr {3} - Error ao enviar para sincronização no grupo: {4}",
						eventType, originId, campaignId, userId, ex.Message
					);
					await ExceptionLogger.LogException(ex, "CampaignsGroup - Envio para fila de atualização", "Erro ao enviar para fila de atualização");
				}
			}
		}

		private async Task UpdateParticipantDataInGroupCampaigns(EventType eventType, Guid originId, Guid campaignId,
			Guid userId, Guid participantId, Guid? accountOperatorId, OperationOrigin? creationOrigin)
		{
			var updateRequest = CampaignsGroupsUpdateRequest.Of(eventType, originId, campaignId, userId, participantId, accountOperatorId, creationOrigin);
			LoggerFactory.GetLogger().Info(
				"TraceId {0} - Event {1} - OriginId {2} - Cmp {3} - Usr {4} - Enviando participante para ser replicado nas campanhas em coalizão",
				updateRequest.TraceId, eventType, originId, campaignId, userId
			);
			bool wasUpdated = await this.campaignsGroupsRepository.SendParticipantToCampaignsGroupSync(updateRequest);
			if (!wasUpdated)
			{
				throw MotivaiException.ofValidation("Não foi possível enviar o participante para sincronização no grupo de campanhas.");
			}
		}
	}
}
