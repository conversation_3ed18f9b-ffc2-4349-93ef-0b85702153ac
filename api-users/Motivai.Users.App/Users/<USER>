using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.App.Users {
	public class UserFinder : IUserFinder {
		private readonly IUserRepository userRepository;
		private readonly ICampaignRepository campaignRepository;

		public UserFinder(IUserRepository userRepository, ICampaignRepository campaignRepository) {
			this.userRepository = userRepository;
			this.campaignRepository = campaignRepository;
		}

		public async Task<UserCampaigns> FindUserCampaignsByDocument(string document, bool onlyActive = true) {
			var personType = PersonTypeHelper.FromDocument(document);

			var userCampaigns = await userRepository.GetUserCampaignsByDocument(personType, document, onlyActive);

			// Se estiver inativo não retorna nada
			if (userCampaigns == null || (onlyActive && !userCampaigns.Active))
				return null;
			if (userCampaigns.Campaigns.IsNullOrEmpty())
				return userCampaigns;

			if (onlyActive) {
				userCampaigns.Campaigns.RemoveAll(c => !c.Active);
			}

			await LoadCampaignsNames(userCampaigns);
			return userCampaigns;
		}

		private async Task LoadCampaignsNames(UserCampaigns userCampaigns) {
			var campaigns = await campaignRepository.GetCampaignsByIds(userCampaigns.Campaigns.Select(c => c.Id).ToList());
			if (campaigns.IsNullOrEmpty()) return;

			foreach (var userCampaign in userCampaigns.Campaigns) {
				var campaign = campaigns.FirstOrDefault(c => c.Id == userCampaign.Id);
				if (campaign == null) {
					continue;
				}
				userCampaign.Name = campaign.Name;
				if (!campaign.Active) {
					userCampaign.Active = userCampaign.Active;
				}
			}
		}
	}
}