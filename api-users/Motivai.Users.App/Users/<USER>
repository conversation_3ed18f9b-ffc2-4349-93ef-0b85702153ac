using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.Participants;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;

namespace Motivai.Users.App.Users {
	public class UserParentFinder : IUserParentFinder {
		private readonly IUserRepository userRepository;
		private readonly IUserParticipationCampaignRepository participantRepository;

		public UserParentFinder(IUserRepository userRepository, IUserParticipationCampaignRepository participantRepository) {
			this.userRepository = userRepository;
			this.participantRepository = participantRepository;
		}

		public async Task<List<UserParentDetails>> GetUsersParentsByIds(Guid campaignId, List<Guid> usersIds) {
			if (usersIds.IsNullOrEmpty()) {
				return null;
			}
			if (campaignId == Guid.Empty) {
				throw MotivaiException.ofValidation("Campanha inválida");
			}
			var parents = await participantRepository.GetUsersParentsByIds(campaignId, usersIds);
			if (parents.IsNullOrEmpty())
				return null;
			parents.RemoveAll(p => p == null);
			return parents;
		}

		public async Task<bool> VerifyIfUsersHasSameParentHierarchy(Guid campaignId, Guid firstUserId, Guid secondUserId) {
			var parents = await GetUsersParentsByIds(campaignId, new List<Guid>(2) { firstUserId, secondUserId });
			if (parents.IsNullOrEmpty())
				return false;
			var firstParent = parents.FirstOrDefault(p => p.UserId == firstUserId);
			var secondParent = parents.FirstOrDefault(p => p.UserId == secondUserId);
			// se os dois não tem pai
			if (firstParent == null && secondParent == null) {
				return false;
			}
			await FillParentIfNeeded(campaignId, firstParent);
			await FillParentIfNeeded(campaignId, secondParent);

			if (firstParent == null) {
				return secondParent.IsUserParent(firstUserId);
			} else if (secondParent == null) {
				return firstParent.IsUserParent(secondUserId);
			}
			return firstParent.HasSameParenthierarchy(secondParent);
		}

		private async Task FillParentIfNeeded(Guid campaignId, UserParentDetails userParent) {
			if (userParent == null || userParent.HasParent() || !userParent.HasParentDocument()) {
				return;
			}
			var foundParent = await userRepository.GetUserBasicInfoByDocument(campaignId, userParent.GetParentDocument());
			if (foundParent == null)
				return;
			userParent.SetParent(foundParent);
			await participantRepository.UpdateParticipantParent(campaignId, userParent);
		}

		public async Task<List<UserBasicInfo>> GetUsersWithSameParentHierarchy(Guid userId, Guid campaignId) {
			var userParent = await participantRepository.GetUserParent(userId, campaignId);
			if (userParent == null) {
				return null;
			}
			await FillParentIfNeeded(campaignId, userParent);

			var parentUserId = userParent.HasParent() ? userParent.ParentUserId.Value : userId;
			var foundUsers = await participantRepository.FindUsersWithSameParent(campaignId, parentUserId);
			if (foundUsers.IsNullOrEmpty()) {
				return null;
			}
			foundUsers.RemoveAll(u => u.UserId == userId);
			return foundUsers;
		}

		public async Task<UserBasicInfo> GetParentByUserId(Guid userId, Guid campaignId)
		{
			return await this.participantRepository.GetParentUser(userId, campaignId);
		}
	}
}