using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.Configurations.Security;
using Motivai.Users.Domain.Entities.Wallets.Devices;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.Security;
using Motivai.Users.Domain.Services.Security;

namespace Motivai.Users.App {
    public class UserDeviceManagerApp : IUserDeviceManagerApp {
        private readonly IUserConfigurationRepository userConfiguratioRepository;
        private readonly SecurityCodeSender securityCodeSender;

        public UserDeviceManagerApp(IUserConfigurationRepository userConfiguratioRepository, SecurityCodeSender securityCodeSender) {
            this.userConfiguratioRepository = userConfiguratioRepository;
            this.securityCodeSender = securityCodeSender;
        }

        public async Task<bool> GetActive(Guid userId, string deviceId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");

            var device = await this.userConfiguratioRepository.GetActiveDevice(userId, deviceId);
            return device == null ? false : true;
        }

        public async Task<bool> GetById(Guid userId, string deviceId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");

            if (deviceId == null)
                throw MotivaiException.ofValidation("Dispositivo inválido.");

            var device = await this.userConfiguratioRepository.GetDeviceById(userId, deviceId);
            return device == null ? false : true;
        }

        public async Task<bool> AuthorizeDevice(Guid userId, DeviceAuthorizationRequest deviceAuthorizationRequest) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");

            if (deviceAuthorizationRequest.DeviceRequestInfo == null)
                throw MotivaiException.ofValidation("Dispositivo inválido.");

            // Se já estiver registrado então segue OK
            if (await userConfiguratioRepository.VerifyAuthorization(userId, deviceAuthorizationRequest.DeviceRequestInfo)) {
                return true;
            }

            var registeredDevice = RegisteredDevice.FromAuthorizationRequest(deviceAuthorizationRequest);
            registeredDevice.Authorizate();

            return await this.userConfiguratioRepository.CreateRegisteredDevice(userId, registeredDevice);
        }

        public async Task<bool> VerifyDeviceAuthorization(Guid userId, DeviceRequestInfo deviceRequestInfo) {
            if (deviceRequestInfo == null) {
                throw MotivaiException.ofValidation("Dispositivo inválido.");
            }
            return await this.userConfiguratioRepository.VerifyAuthorization(userId, deviceRequestInfo);
        }

        public async Task<bool> SendAuthorizationCode(Guid userId, SecurityCodeRequest securityCodeRequest) {
            if (securityCodeRequest == null)
                throw MotivaiException.ofValidation("Método de envio do código de segurança inválido.");

            var securityCode = securityCodeSender.GenerateCode();

            var generatedCode = GeneratedSecurityCode.Of(securityCodeRequest.CampaignId, securityCodeRequest.DeviceRequestInfo.Device.DeviceId, securityCode);
            await userConfiguratioRepository.SaveSecurityCode(userId, generatedCode);

            await securityCodeSender.SendSecurityCodeByMethod(userId, securityCodeRequest.CampaignId, securityCodeRequest.SendMethod, securityCode);
            return true;
        }

        public async Task<bool> ValidateAuthorizationCode(Guid userId, SecurityCodeValidation codeValidation) {
            if (codeValidation == null || string.IsNullOrEmpty(codeValidation.Code))
                throw MotivaiException.ofValidation("Código de segurança inválido.");

            var generatedCode = await userConfiguratioRepository.GetSecurityCodeForValidation(userId);
            if (generatedCode == null || string.IsNullOrEmpty(generatedCode.HashedSecurityCode))
                throw MotivaiException.ofValidation("Código de segurança inválido.");
            generatedCode.ValidateSecurityCodeAndSubject(codeValidation.Code, codeValidation.DeviceRequestInfo.Device.DeviceId);
            var registered = await AuthorizeDevice(userId, new DeviceAuthorizationRequest() {
                TokenRequestDate = generatedCode.CreateDate,
                TokenConfirmationDate = DateTime.UtcNow,
                DeviceRequestInfo = codeValidation.DeviceRequestInfo
            });
            if (registered) {
                await userConfiguratioRepository.InvalidateSecurityCode(userId);
            }
            return registered;
        }
    }
}