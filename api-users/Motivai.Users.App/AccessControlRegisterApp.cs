using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.App
{
	public class AccessControlRegisterApp : IAccessControlRegisterApp
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly IAccessLogRepository accessLogRepository;
		private readonly IAuthenticationLogRepository authenticationLogRepository;
		private readonly IEmailRepository emailRepository;
		private readonly IUserRepository userRepository;

		public AccessControlRegisterApp(ICampaignRepository campaignRepository, IAccessLogRepository accessLogRepository,
				IAuthenticationLogRepository authenticationLogRepository, IEmailRepository emailRepository, IUserRepository userRepository)
		{
			this.campaignRepository = campaignRepository;
			this.accessLogRepository = accessLogRepository;
			this.authenticationLogRepository = authenticationLogRepository;
			this.emailRepository = emailRepository;
			this.userRepository = userRepository;
		}

		public async Task RegisterAccessLog(LogRegisterAction action, UserParticipantModel participantSession, ConnectionInfo connectionInfo)
		{
			try
			{
				await accessLogRepository.RegisterLogin(action, participantSession, connectionInfo);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Login - Access Log", $"Erro ao registrar acesso", true);
			}
		}

		public async Task RegisterAuthenticationLog(CampaignAuthenticationLog authenticationLog)
		{
			try
			{
				await authenticationLogRepository.RegisterAuthenticationLog(authenticationLog);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Login - Auhentication Log", $"Erro ao registrar log de autenticação", true);
			}
		}

		private async Task RegisterAuthenticationLog(AuthenticationActionLog actionLog, UserParticipantModel participantSession,
			ConnectionInfo connectionInfo)
		{
			try
			{
				var authenticationLog = CampaignAuthenticationLog.OfSuccessfulAuthentication(actionLog, participantSession, connectionInfo);
				await authenticationLogRepository.RegisterAuthenticationLog(authenticationLog);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Login - Access Log", $"Erro ao registrar acesso", true);
			}
		}

		public async Task RegisterAccessAndNotificate(LogRegisterAction action, UserParticipantModel participantSession,
			ConnectionInfo connectionInfo)
		{
			var campaignSettings = await campaignRepository.GetSettings(participantSession.CampaignId);
			await RegisterAccessAndNotificate(action, participantSession, connectionInfo, campaignSettings);
		}

		public async Task RegisterAccessAndNotificate(LogRegisterAction action, UserParticipantModel participantSession,
			ConnectionInfo connectionInfo, CampaignSettingsModel campaignSettings)
		{
			LoggerFactory.GetLogger().Info("LoginNotification - Usr {0} - Cmp {1} - CPF {2} - Email {3} - Operator {4} - Registrando acesso",
				participantSession.UserId, participantSession.CampaignId, participantSession.Document, participantSession.Email, participantSession.AccountOperatorId);

			await RegisterAccessLog(action, participantSession, connectionInfo);
			await RegisterAuthenticationLog(action.MapActionToAuthenticationActionLog(), participantSession, connectionInfo);

			// UserId pode ser Empty quando for login de operador sem ter selecionado uma conta
			if (participantSession.UserId != Guid.Empty)
			{
				await userRepository.UpdateLastAccess(participantSession.UserId, participantSession.CampaignId, participantSession.Timezone, participantSession.Balance);
			}

			if (campaignSettings.Parametrizations.AllowLoginNotification)
			{
				await SendNotification(AccountLoginNotification.FromParticipantSession(participantSession, connectionInfo));
			}
		}

		// TODO: rastrear quem utilizava esse método
		public async Task RegisterAccessLogAndNotificate(Guid campaignId, User user, UserParticipantCampaign participant,
			LogRegisterAction action, CampaignSettingsModel campaignSettings, LocationInfo locationInfo = null)
		{
			await RegisterAccessLog(action, user.Id, campaignId, user.Cpf, locationInfo);
			await RegisterAccessActionLog(action.MapActionToAuthenticationActionLog(), campaignId, user, locationInfo);
			participant.UpdateLastAccess();

			await userRepository.UpdateAccess(participant);

			LoggerFactory.GetLogger().Info("LoginNotification - Usr {0} - Cmp {1} - CPF {2} - Email {3} - Enviando notificação de login",
				user.Id, campaignId, user.GetDocument(), participant.GetMainEmail());

			if (campaignSettings.Parametrizations.AllowLoginNotification)
			{
				await SendNotification(AccountLoginNotification.FromParticipant(campaignId, user, participant, locationInfo));
			}
		}

        private async Task RegisterAccessLog(LogRegisterAction action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null)
		{
			try
			{
				await accessLogRepository.RegisterLogin(action, userId, campaignId, document, locationInfo);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Login - Access Log", $"Erro ao registrar acesso", true);
			}
		}

		private async Task RegisterAccessActionLog(AuthenticationActionLog actionLog, Guid campaignId, User user, LocationInfo locationInfo = null)
		{
			try
			{
				await authenticationLogRepository.RegisterLogin(actionLog, user.Id, campaignId, user.GetDocument(), locationInfo);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Login - Access Log", $"Erro ao registrar acesso", true);
			}
		}

		private async Task SendNotification(AccountLoginNotification notification)
		{
			try
			{
				await emailRepository.SendLoginNotification(notification);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Login - Notification", "Erro ao enviar e-mail de notificação");
				LoggerFactory.GetLogger().Error("LoginNotification - Usr {0} - Cmp {1} - Email {2} - Erro no email: {3}",
					notification.UserId, notification.CampaignId, notification.Email, ex.Message);
			}
		}
    }
}