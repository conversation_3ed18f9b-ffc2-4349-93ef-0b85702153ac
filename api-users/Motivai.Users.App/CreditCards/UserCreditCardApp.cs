using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System;

using Motivai.Users.Domain.Entities.CreditCards;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IApp.CreditCards;

using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.App.CreditCards
{
    public class UserCreditCardApp : IUserCreditCardApp
    {
        private readonly IUserRepository userRepository;

        public UserCreditCardApp(IUserRepository userRepository)
        {
            this.userRepository = userRepository;
        }

        public async Task<Guid> Create(Guid userId, UserCreditCard userCreditCard)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.");

            if (userCreditCard == null)
                throw MotivaiException.ofValidation("Deve ser informado os Dados do Cartão de Crédito do Usuário.");

            userCreditCard.Validate();

            var user = await this.userRepository.Get(userId);

            if (user == null)
                throw MotivaiException.ofValidation(String.Format("Usuário não encontrado pelo ID informado: {0}", userId));

            if (user.Wallet == null)
                throw MotivaiException.ofValidation(String.Format("Não existe carteira configurada para o Usuário informado. ID do Usuário: {0}", userId));

            if (user.Wallet.CreditCards == null || !user.Wallet.CreditCards.Any())
                user.Wallet.CreditCards = new List<UserCreditCard>();

            user.Wallet.CreditCards.Add(userCreditCard);

            await this.userRepository.Save(user);
            return userCreditCard.Id;
        }

        public async Task<bool> UpdateBilling(Guid userId, Guid userCreditCardId, UserCreditCardBilling userCreditCardBilling)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.");

            if (userCreditCardId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do UserCreditCard.");

            if (userCreditCardBilling == null)
                throw MotivaiException.ofValidation("Deve ser informado os Dados de Billing Cartão de Crédito do Usuário.");

            userCreditCardBilling.Validate();

            return await this.userRepository.UpdateCreditCardBilling(userId, userCreditCardId, userCreditCardBilling);
        }

        public async Task<UserCreditCard> Get(Guid userId, Guid userCreditCardId)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.");

            if (userCreditCardId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do UserCreditCard.");

            var user = await this.userRepository.Get(userId);

            if (user == null)
                throw MotivaiException.ofValidation(String.Format("Usuário não encontrado pelo ID informado: {0}", userId));

            if (user.Wallet == null)
                throw MotivaiException.ofValidation(String.Format("Não existe carteira configurada para o Usuário informado. ID do Usuário: {0}", userId));

            if (user.Wallet.CreditCards == null || !user.Wallet.CreditCards.Any())
                throw MotivaiException.ofValidation(String.Format("Não existe vínculos de Cartões de Crédito para o ID do Usuário informado: {0}", userId));

            if (user.Wallet.CreditCards == null || !user.Wallet.CreditCards.Any())
                return null;

            return user.Wallet.CreditCards.Where(u => u.Id == userCreditCardId).FirstOrDefault();
        }

        public async Task<List<UserCreditCard>> Get(Guid userId)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.");

            var user = await this.userRepository.Get(userId);

            if (user == null)
                throw MotivaiException.ofValidation(String.Format("Usuário não encontrado pelo ID informado: {0}", userId));

            if (user.Wallet == null)
                throw MotivaiException.ofValidation(String.Format("Não existe carteira configurada para o Usuário informado. ID do Usuário: {0}", userId));

            return user.Wallet.CreditCards != null && user.Wallet.CreditCards.Any() ? user.Wallet.CreditCards.Where(c => c.Active).ToList() : null;
        }

        public async Task<bool> Delete(Guid userId, Guid userCreditCardId)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.");

            if (userCreditCardId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do UserCreditCard.");

            var user = await this.userRepository.Get(userId);

            if (user == null)
                throw MotivaiException.ofValidation(String.Format("Usuário não encontrado pelo ID informado: {0}", userId));

            if (user.Wallet == null)
                throw MotivaiException.ofValidation(String.Format("Não existe carteira configurada para o Usuário informado. ID do Usuário: {0}", userId));

            if (user.Wallet.CreditCards == null || !user.Wallet.CreditCards.Any())
                throw MotivaiException.ofValidation(String.Format("Não existe vínculos de Cartões de Crédito para o ID do Usuário informado: {0}", userId));

            var userCreditCardToUpdate = user.Wallet.CreditCards.Where(u => u.Id == userCreditCardId).FirstOrDefault();

            if (userCreditCardToUpdate == null)
                throw MotivaiException.ofValidation(String.Format("Cartão de Crédito não encontrado pelo UserCreditCardId informado: {0} ID do Usuário: {1}", userCreditCardId, userId));

            userCreditCardToUpdate.Active = false;
            userCreditCardToUpdate.SystemUpdateddDate = DateTime.UtcNow;
            userCreditCardToUpdate.SystemDeletedDate = DateTime.UtcNow;

            user.Wallet.CreditCards.Remove(userCreditCardToUpdate);
            user.Wallet.CreditCards.Add(userCreditCardToUpdate);

            await this.userRepository.Save(user);

            return true;
        }

        public async Task<UserCreditCard> Get(Guid userId, string cardId)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.");

            if (String.IsNullOrEmpty(cardId))
                throw MotivaiException.ofValidation("Deve ser informado o ID do Cartão de Crédito.");

            var user = await this.userRepository.Get(userId);

            if (user == null)
                throw MotivaiException.ofValidation(String.Format("Usuário não encontrado pelo ID informado: {0}", userId));

            if (user.Wallet == null)
                return null;

            if (user.Wallet.CreditCards == null || !user.Wallet.CreditCards.Any())
                return null;

            return user.Wallet.CreditCards.Where(u => u.CardId == cardId && u.Active).FirstOrDefault();
        }
    }
}