using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Services.Participants;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Transactions;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Services.Participants.Creation;
using Motivai.SharedKernel.Helpers.Generators;
using System.Linq;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.Models.UserParticipant;
using Motivai.Users.Domain.Entities.Participants;

namespace Motivai.Users.App
{
	public class UserExternalClientApp : IUserExternalClientApp
	{
		private readonly IUserApp userApp;
		private readonly IUserRepository userRepository;
		private readonly ICampaignRepository campaignRepository;
		private readonly ITransactionApiRepository transactionRepository;
		private readonly AddressVerifier addressVerifier;
		private readonly CampaignParticipantConfigurator postCreationConfigurer;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
        private readonly IUserMetadataApp userMetadataApp;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;

        public UserExternalClientApp(IUserApp userApp, IUserRepository userRepository,
				ICampaignRepository campaignRepository, ITransactionApiRepository transactionRepository,
				AddressVerifier addressVerifier, CampaignParticipantConfigurator postCreationConfigurer,
				ICampaignsGroupsApp campaignsGroupsApp, IUserMetadataApp userMetadataApp, IParticipantRegistrationLogRepository participantRegistrationLogRepository)
		{
			this.userApp = userApp;
			this.userRepository = userRepository;
			this.campaignRepository = campaignRepository;
			this.transactionRepository = transactionRepository;
			this.addressVerifier = addressVerifier;
			this.postCreationConfigurer = postCreationConfigurer;
			this.campaignsGroupsApp = campaignsGroupsApp;
            this.userMetadataApp = userMetadataApp;
			this.participantRegistrationLogRepository = participantRegistrationLogRepository;
        }

		public async Task<decimal> GetBalanceByUserId(Guid campaignId, string document)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (String.IsNullOrEmpty(document))
				throw MotivaiException.ofValidation("Documento inválido.");

			ParticipantInfo user = await this.userApp.GetUserInfoByDocument(document);

			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			try
			{
				return await this.transactionRepository.GetBalance(campaignId, user.UserId);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "UserExternalClient - Consulta Saldo", "Erro durante consulta do saldo");
				return 0;
			}
		}

		public async Task<bool> CreateParticipant(Guid campaignId, UserExternalModel userExternalModel)
		{
			if (userExternalModel == null)
			{
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
			}
			LoggerFactory.GetLogger().Info("ExternalClientApp - Cmp {0} - Usr {1} - Email {2} - Cellphone {3} - Recebido cadastro",
				campaignId, userExternalModel.Document, userExternalModel.Email, userExternalModel.Cellphone);
			userExternalModel.Validate();
			await ValidateCampaign(campaignId);

			var campaignSettings = await this.campaignRepository.GetSettings(campaignId);

			User user = await FindUserByDocument(userExternalModel.PersonType, userExternalModel.Document);

			UserParticipantCampaign participant = null;

			if (user != null)
			{
				participant = user.GetParticipantByCampaign(campaignId);
				if (participant != null)
					throw MotivaiException.ofValidation("Participante já está cadastrado na campanha.");

			}
			else
			{
				user = User.CreateUser(campaignSettings.Type, userExternalModel.PersonType, userExternalModel.Document);
				user.Name = userExternalModel.Name;
				user.CompanyName = userExternalModel.CompanyName;
				user.Rg = userExternalModel.Rg;
				if (userExternalModel.BirthDate.HasValue)
					user.BirthDate = userExternalModel.BirthDate.Value;

				if (userExternalModel.StateInscriptionExempt.HasValue) {
					user.StateInscriptionExempt = userExternalModel.StateInscriptionExempt.Value;
					user.StateInscription = userExternalModel.StateInscription;
					user.StateInscriptionUf = userExternalModel.StateInscriptionUf;
				}
			}

			if (participant == null)
			{
				participant = await CreateParticipantFromIntegrationData(campaignId, campaignSettings, user, userExternalModel);
			}

			var generatedPassword = AlphanumericGenerator.GenerateId16();
			if (!string.IsNullOrEmpty(userExternalModel.Password))
			{
				generatedPassword = userExternalModel.Password;
			}
			participant.OverridePassword(generatedPassword);

			user.UsersParticipantCampaign.Add(participant);

			await this.userRepository.CreateUser(user);

			if (userExternalModel.HasMetadata())
			{
				await this.userMetadataApp.SaveUserMetadata(user.Id, campaignId, participant.Id,
					userExternalModel.Metadata, "ExternalParticipantsApi");
			}

			await this.AddParticipantToGroupIfNeeded(campaignId, user.Id, userExternalModel.GroupsCodes);

			await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.EXTERNAL_CREATION,
				Guid.Empty, campaignId, user.Id, participant.Id, Guid.Empty
			);

			await postCreationConfigurer.ExecutePostCreationActions(campaignSettings, user, participant, generatedPassword);

			return true;
		}

		public async Task<bool> UpdateParticipant(Guid campaignId, string document, UserExternalModel userExternalModel)
		{
			userExternalModel.Validate();

			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			if (document != userExternalModel.Document)
				throw MotivaiException.ofValidation("CPF/CNPJ não confere com os dados informados.");

			User user = await FindUserByDocument(userExternalModel.PersonType, document);
			if (user == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");

			UserParticipantCampaign participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não está incluso nesta campanha.");

			user.Name = userExternalModel.Name;
			user.CompanyName = userExternalModel.CompanyName;
			user.Rg = userExternalModel.Rg;
			if (userExternalModel.BirthDate.HasValue)
				user.BirthDate = userExternalModel.BirthDate.Value;

			if (userExternalModel.StateInscriptionExempt.HasValue) {
				user.StateInscriptionExempt = userExternalModel.StateInscriptionExempt.Value;
				user.StateInscription = userExternalModel.StateInscription;
				user.StateInscriptionUf = userExternalModel.StateInscriptionUf;
			}

			var contactFromUserExternalModel = Contact.Of(userExternalModel.Telephone, userExternalModel.Cellphone, userExternalModel.Email);

			if (UserParticipantValidation.CheckForContactChanges(participant.Contact, contactFromUserExternalModel)) {
				var log = ParticipantRegistrationLog.OfContactUpdate(RegistrationAction.UPDATE_CONTACT,
					"Api Externa", participant.UserId, campaignId, participant.Contact, contactFromUserExternalModel
				);

				await this.participantRegistrationLogRepository.RegisterLog(log);
			}
			UserParticipantValidation.CopyContactFields(participant, contactFromUserExternalModel);
			participant.UpdateFromUserExternal(userExternalModel);

			if (userExternalModel.Address != null)
			{
				await addressVerifier.CorrectAddress(campaignId, userExternalModel.Document, userExternalModel.Address);
			}

			await SetParentParticipantIfNeeded(campaignId, participant, userExternalModel);

			await this.AddParticipantToGroupIfNeeded(campaignId, user.Id, userExternalModel.GroupsCodes);

			var wasUpdated = await this.userRepository.UpdateUserExternal(user.Id, participant.Id, user, participant);
			if (wasUpdated)
			{
				if (userExternalModel.HasMetadata())
				{
					await this.userMetadataApp.SaveUserMetadata(user.Id, campaignId, participant.Id, userExternalModel.Metadata, "ExternalParticipantsApi");
				}

				await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.EXTERNAL_UPDATE,
					Guid.Empty, campaignId, user.Id, participant.Id, Guid.Empty
				);
			}

			return wasUpdated;
		}

		private async Task<UserParticipantCampaign> CreateParticipantFromIntegrationData(Guid campaignId, CampaignSettingsModel campaignSettings,
			User user, UserExternalModel userExternalModel)
		{
			Employee employee = null;

			LoggerFactory.GetLogger().Info("ExternalClientApp - Cmp {0} - Usr {1} - Criando participante",
				campaignId, userExternalModel.Document);
			if (userExternalModel.HasCompanyIntegrationData())
			{
				employee = new Employee()
				{
					CompanyName = userExternalModel.IntegrationCompany.CompanyName,
					CompanyIdentifier = userExternalModel.IntegrationCompany.CompanyIdentifier
				};
			}

			var participant = UserParticipantCampaign.CreateForCampaign(campaignId, campaignSettings, user);
			participant.CreatedByIntegration = true;
			participant.Origin = "ExternalParticipantsApi";
			participant.ClientUserId = userExternalModel.ClientUserId;
			participant.Contact = Contact.Of(userExternalModel.Telephone, userExternalModel.Cellphone, userExternalModel.Email);
			participant.IntegrationCompany = userExternalModel.IntegrationCompany;
			participant.Employee = employee;

			if (userExternalModel.Address != null)
			{
				userExternalModel.Address.CreatedByIntegration = true;
				userExternalModel.Address.MainAddress = true; // forca o unico endereco como principal
				userExternalModel.Address.Active = true;

				await addressVerifier.CorrectAddress(campaignId, userExternalModel.Document, userExternalModel.Address);

				participant.AddAddress(userExternalModel.Address);
			}

			await SetParentParticipantIfNeeded(campaignId, participant, userExternalModel);

			return participant;
		}

		public async Task<bool> BlockParticipant(Guid campaignId, string document)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			if (String.IsNullOrEmpty(document))
				throw MotivaiException.ofValidation("Documento inválido.");

			User user = null;

			if (Cpf.IsCpf(document))
			{
				user = await this.userRepository.GetByCpf(document);
			}
			else if (Cnpj.IsCnpj(document))
			{
				user = await this.userRepository.GetByCnpj(document);
			}
			else
			{
				throw MotivaiException.ofValidation("CPF inválido.");
			}

			if (user == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não está incluso nesta campanha.");

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Bloqueando participante.", campaignId, user.Id);
			return await this.userRepository.BlockParticipant(user.Id, campaignId,
					BlockingDetails.OfLogin("Efetuado o bloqueamento através da API."), Domain.Enums.BlockingOrigin.Api);
		}

		private async Task<User> FindUserByDocument(PersonType personType, string document)
		{
			if (personType == PersonType.Fisica)
			{
				return await this.userRepository.GetByCpf(document);
			}
			else if (personType == PersonType.Juridica)
			{
				return await this.userRepository.GetByCnpj(document);
			}
			throw MotivaiException.ofValidation("CPF/CNPJ inválido.");
		}

		private async Task ValidateCampaign(Guid campaignId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			var campaignActive = await this.campaignRepository.GetActive(campaignId);
			if (!campaignActive)
				throw MotivaiException.ofValidation("A campanha não está ativa.");
		}

		private async Task SetParentParticipantIfNeeded(Guid campaignId, UserParticipantCampaign participant, UserExternalModel userExternalModel)
		{
			if (!userExternalModel.HasParent())
			{
				return;
			}

			var parentParticipant = await userRepository.GetUserBasicInfoByDocument(campaignId, userExternalModel.ParentParticipant.Document);
			if (parentParticipant == null)
				throw MotivaiException.ofValidation($"Participante pai não encontrado pelo CPF/CNPJ {userExternalModel.ParentParticipant.Document}");

			participant.ParentUserDetails = parentParticipant;
		}

		private async Task<bool> AddParticipantToGroupIfNeeded(Guid campaignId, Guid userId, List<string> groupsCodes)
		{
			if (groupsCodes == null || !groupsCodes.Any())
			{
				return false;
			}
			var added = await this.campaignRepository.AddParticipantToGroups(campaignId, userId, groupsCodes);
			if (!added)
			{
				throw MotivaiException.ofValidation("Ocorreu um erro ao adicionar o participante ao grupo.");
			}
			return true;
		}
	}
}
