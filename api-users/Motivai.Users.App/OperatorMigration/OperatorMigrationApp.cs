using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.OperatorMigration;
using Motivai.Users.Domain.IApp.OperatorMigration;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;

namespace Motivai.Users.App.OperatorMigration
{
    public class OperatorMigrationApp : IOperatorMigrationApp
    {
        private readonly IAccountOperatorRepository accountOperatorRepository;
        private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;
        public OperatorMigrationApp(IAccountOperatorRepository accountOperatorRepository,
            IParticipantRegistrationLogRepository participantRegistrationLogRepository)
        {
            this.accountOperatorRepository = accountOperatorRepository;
            this.participantRegistrationLogRepository = participantRegistrationLogRepository;
        }

        public async Task<bool> MigrateAccountOperatorAuthenticationMethod(OperatorMigrationModel operatorMigration)
        {
            if (operatorMigration == null)
                throw MotivaiException.ofValidation("Operador informado inválido.");

            operatorMigration.Validate();
            var accountOperator = await accountOperatorRepository.FindAccountOperatorByDocument(operatorMigration.Document);

            if (accountOperator == null)
                throw MotivaiException.ofValidation("Operador não encontrado.");

            this.ValidateOperatorMigration(accountOperator, operatorMigration);

            if (!HasAnyLoginToMigrate(accountOperator, operatorMigration))
            {
                LoggerFactory.GetLogger().Warn("Usr {0} - Doc {1} - Operador não possuí login a ser migrado para campanha(s) e email(s) informado(s).",
                    accountOperator.Name, accountOperator.Document);
                return true;
            }

            this.MigrateEligibleOperatorLogins(accountOperator, operatorMigration);

            try {
                var wasUpdate = await this.accountOperatorRepository.UpdateAccountOperatorLoginsByCampaigns(accountOperator, operatorMigration.CampaignIds);
                if (!wasUpdate)
                    throw MotivaiException.ofValidation("Não foi possível atualizar o operador.");

                await this.LogParticipantRegistrationForAuthenticationMigration(accountOperator, operatorMigration);
                return wasUpdate;
            }
            catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "OperatorMigration - Migrate Account Operator ", "Erro durante a migração do operador");
                throw ex;
            }
        }

        private void ValidateOperatorMigration(AccountOperator accountOperator, OperatorMigrationModel operatorMigration)
        {
            if (!accountOperator.HasLogin())
                throw MotivaiException.ofValidation("Operador não possui login.");

            if (!operatorMigration.CampaignIds.Any(l => accountOperator.HasLoginInCampaign(l)))
                throw MotivaiException.ofValidation("Operador não possuí nenhum login vinculado a(s) campanha(s) informadas.");

            if (!accountOperator.Logins.Any(l => accountOperator.HasLoginWithEmail(operatorMigration.Email)))
                throw MotivaiException.ofValidation("Operador não possuí nenhum login vinculado ao e-mail informado.");
        }

        private bool HasAnyLoginToMigrate(AccountOperator accountOperator, OperatorMigrationModel operatorMigration)
        {
            return accountOperator.Logins.Any(login => operatorMigration.CampaignIds.Any(campaignId =>
                login.IsEligibleForMigration(campaignId, operatorMigration.Email)
            ));
        }

        private void MigrateEligibleOperatorLogins(AccountOperator accountOperator, OperatorMigrationModel operatorMigration)
        {
            accountOperator.Name = operatorMigration.Name;
            foreach (var login in accountOperator.Logins)
            {
                foreach (var campaignId in operatorMigration.CampaignIds)
                {
                    login.MigrateIfEligible(campaignId, operatorMigration);

                    LoggerFactory.GetLogger().Warn("Cmp {0} - Doc {1} - Login {2} - Operador Migrado? {3}",
                        campaignId, accountOperator.Document, login.Login, login.IsMigrated() ? "Sim" : "Não"
                    );
                }
            }
        }

        private async Task LogParticipantRegistrationForAuthenticationMigration(AccountOperator accountOperator, OperatorMigrationModel operatorMigration)
        {
            await this.participantRegistrationLogRepository.RegisterLog(
                ParticipantRegistrationLog.ofAction(
                    RegistrationAction.VR_ACCESS_ACCOUNT_OPERATOR_AUTHENTICATION_MIGRATION,
                    accountOperator,
                    operatorMigration.Email,
                    operatorMigration.Document
                )
            );
        }
    }
}
