using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.ParticipantHistoryFiles;
using Motivai.Users.Domain.IApp.ParticipantHistoryFiles;
using Motivai.Users.Domain.IRepository.ParticipantHistoryFiles;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Users.App
{
    public class ParticipantHistoryFileApp : IParticipantHistoryFileApp
    {
        private readonly IParticipantHistoryFileRepository participantHistoryFileRepository;

        public ParticipantHistoryFileApp(IParticipantHistoryFileRepository participantHistoryFile)
        {
            this.participantHistoryFileRepository = participantHistoryFile;
        }
        public async Task<List<ParticipantHistoryFile>> GetByParticipant(Guid userId, Guid campaignId, bool active)
        {
            if (userId == null) {
                throw MotivaiException.ofValidation("Usuário inválido");
            }

            if (campaignId == null) {
                throw MotivaiException.ofValidation("Campanha inválida");
            }

            return await this.participantHistoryFileRepository.GetByParticipant(userId, campaignId, active);
        }

        public async Task<bool> Save(Guid userId, Guid campaignId, ParticipantHistoryFile participantHistoryFile)
        {
            if (userId == null) {
                throw MotivaiException.ofValidation("Usuário inválido");
            }

            if (campaignId == null) {
                throw MotivaiException.ofValidation("Campanha inválida");
            }

            if (participantHistoryFile == null) {
                throw MotivaiException.ofValidation("Arquivo inválido");
            }

            participantHistoryFile.Validate();

            return await this.participantHistoryFileRepository.Save(userId, campaignId, participantHistoryFile);
        }

        public async Task<bool> Unactive(Guid userId, Guid campaignId, Guid id)
        {
            if (userId == null) {
                throw MotivaiException.ofValidation("Usuário inválido");
            }

            if (campaignId == null) {
                throw MotivaiException.ofValidation("Campanha inválida");
            }

            List<ParticipantHistoryFile> participantHistoryFiles = await this.participantHistoryFileRepository.GetByParticipant(userId, campaignId);

            for (int i = 0; i < participantHistoryFiles.Count; i++) {
                if (participantHistoryFiles[i].Id == id) {
                    participantHistoryFiles[i].Active = false;
                    participantHistoryFiles[i].DeleteAt = DateTime.UtcNow;
                }
            }

            return await this.participantHistoryFileRepository.Unactive(userId, campaignId, participantHistoryFiles);

        }
    }
}