using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.PreRegister;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Credify;
using Motivai.Users.Domain.IRepository.PreRegister;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.Credify;
using Motivai.Users.Domain.Services.Participants.Creation;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson;
using Motivai.Users.Domain.IRepository.ClientIntegrations;
using Motivai.Users.Domain.Models.UserParticipant;

namespace Motivai.Users.App
{
	public class PreRegisterApp : IPreRegisterApp
	{
		private readonly IPreRegisterRepository _preRegisterRepository;
		private readonly IUserRepository _userRepository;
		private readonly IUserParticipationCampaignRepository _participantRepository;
		private readonly ICampaignRepository _campaignRepository;
		private readonly ICredifyRepository _credifyRepository;
		private readonly CampaignParticipantConfigurator participantConfigurator;
		private readonly IUserMetadataApp userMetadataApp;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
		private readonly IClientIntegrations clientIntegrations;

		public PreRegisterApp(IPreRegisterRepository preRegisterRepository, IUserRepository userRepository,
				IUserParticipationCampaignRepository participantRepository, ICampaignRepository campaignRepository,
				ICredifyRepository credifyRepository, CampaignParticipantConfigurator participantConfigurator,
				IUserMetadataApp userMetadataApp, ICampaignsGroupsApp campaignsGroupsApp, IClientIntegrations clientIntegrations)
		{
			this._preRegisterRepository = preRegisterRepository;
			this._userRepository = userRepository;
			this._participantRepository = participantRepository;
			this._campaignRepository = campaignRepository;
			this._credifyRepository = credifyRepository;
			this.participantConfigurator = participantConfigurator;
			this.userMetadataApp = userMetadataApp;
			this.campaignsGroupsApp = campaignsGroupsApp;
			this.clientIntegrations = clientIntegrations;
		}

		#region Fluxo do Pré-Cadastro

		public async Task<dynamic> ValidateDocument(Guid campaignId, string document)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (string.IsNullOrEmpty(document))
				throw MotivaiException.ofValidation("CPF/CNPJ é obrigatório.");

			PersonType personType = PersonTypeHelper.FromDocument(document);
			await VerifyIfUserAlreadyRegisteredInCampaign(campaignId, personType, document);

			// Consulta na Credify
			Person personData = null;
			try
			{
				personData = await _credifyRepository.QueryDocument(personType, document);
				if (personData == null)
				{
					return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(document, personType);
				}
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "PreRegistration - Validate Document", "Erro durante consulta da Credify");
				return null;
			}
			var consultPersonDataParams = await this._campaignRepository.GetPersonDataConsultParametrizations(campaignId);
			return ConsultPersonDataBuilder.BuildWithEnabledFieldsOrDefault(personData, consultPersonDataParams);
		}

		public async Task<bool> ValidateUserData(Guid campaignId, PreRegisteredUser user)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (user != null)
				user.CampaignId = campaignId;
			await ValidateUserUsingCampaignConfiguration(user);
			return true;
		}

		public async Task<bool> Create(PreRegisteredUser user)
		{
			await ValidateUserUsingCampaignConfiguration(user);

			var campaignSettings = await _campaignRepository.GetSettings(user.CampaignId);
			if (!campaignSettings.Parametrizations.IsSelfRegistrationEnabled())
			{
				throw MotivaiException.ofValidation("Pré-cadastro não está habilitado.");
			}

			user.Id = Guid.NewGuid();
			user.CreateDate = DateTime.UtcNow;

			await FindParentParticipantAndValidateExistenceIfNeeded(campaignSettings, user);

			// Finaliza precisa de aprovação
			if (campaignSettings.Parametrizations.SkipParticipantSelfRegistrationApproval)
			{
				user.RegisterSkipApproval();
				await CreateParticipant(user);
				await ExecutePartnerIntegration(user);
			}
			return await _preRegisterRepository.Save(user);
		}

 		private async Task ExecutePartnerIntegration(PreRegisteredUser user)
        {
            var integrationSettings = await _campaignRepository.GetIntegrationSettings(user.CampaignId);

			if (integrationSettings.HasClientIntegration())
			{
				if (integrationSettings.IntegrationSettings.EnablePreRegistration) {
					try {
						LoggerFactory.GetLogger().Info("Cmp {} - Pre {} - Pré-Cadastro - Enviando para integração com cliente",
							user.CampaignId, user.Id);
						var integrated = await this.clientIntegrations.SendPreRegister(user.CampaignId, user);
						user.SetIntegrated(integrated);
					} catch (Exception ex) {
						await ExceptionLogger.LogException(ex, "PreRegistration - Client Integration", "Erro durante a integração com cliente");
						user.SetIntegrationError(ex.Message);
					}
				}
			}
        }

        public async Task<bool> Reintegrate(Guid id){
			var preRegisteredUser = await _preRegisterRepository.FindById(id);
			await ExecutePartnerIntegration(preRegisteredUser);
			return await _preRegisterRepository.Save(preRegisteredUser);
		}

		 public async Task IntegrateByDocument(Guid campaignId, string document) {
			var user = await _userRepository.GetByDocument(document);
			if (user == null)
				throw MotivaiException.ofValidation("Nenhum participante encontrado com o documento informado");

			await ExecutePartnerIntegration(PreRegisteredUser.OfUser(user, campaignId));
		}

		#endregion

        #region Fluxo de Aprovação

        public Task<List<PreRegisteredUser>> Find(Guid campaignId, string document, bool? integrated, bool? integrationError, int? skip, int? limit)
		{
			return _preRegisterRepository.Find(campaignId, document, integrated, integrationError, skip, limit);
		}

		public Task<PreRegisteredUser> FindById(Guid id)
		{
			if (id == Guid.Empty)
				throw MotivaiException.ofValidation("Id deve ser informado para buscar");

			return _preRegisterRepository.FindById(id);
		}

		public async Task<bool> Approve(Guid id, PreRegisteredUser preUser)
		{
			if (id == Guid.Empty)
				throw MotivaiException.ofValidation("Id deve ser informado para aprovar");
			if (preUser == null)
				throw MotivaiException.ofValidation("Informações de aprovação são obrigatórias");
			preUser.ValidateApproval();

			var dbPreUser = await _preRegisterRepository.FindById(id);
			if (dbPreUser == null)
				throw MotivaiException.ofValidation("Usuário de pré cadastro não encontrado");
			if (dbPreUser.Refused)
				throw MotivaiException.ofValidation("Este usuário já foi reprovado anteriormente e não pode ser modificado");
			if (dbPreUser.Approved)
				return true;

			await CreateParticipant(dbPreUser);

			// Update approve information
			dbPreUser.Approved = true;
			dbPreUser.ApprovedBy = preUser.ApprovedBy;
			dbPreUser.ApprovedOn = DateTime.UtcNow;

			await ExecutePartnerIntegration(dbPreUser);

			return await _preRegisterRepository.Save(dbPreUser);
		}

		public async Task<bool> Refuse(Guid id, PreRegisteredUser preUser)
		{
			if (id == Guid.Empty)
				throw MotivaiException.ofValidation("Id deve ser informado para aprovar");
			if (preUser == null)
				throw MotivaiException.ofValidation("Informações de aprovação são obrigatórias");

			preUser.ValidateRefuse();
			var dbPreUser = await _preRegisterRepository.FindById(id);
			if (dbPreUser == null)
				throw MotivaiException.ofValidation("Usuário de pré cadastro não encontrado");
			if (dbPreUser.Approved)
				throw MotivaiException.ofValidation("Este usuário já foi aprovado anteriormente e não pode ser modificado");
			if (dbPreUser.Refused)
				return true;

			dbPreUser.Refused = true;
			dbPreUser.RefusedBy = preUser.RefusedBy;
			dbPreUser.RefusedOn = DateTime.UtcNow;
			dbPreUser.RefuseReason = preUser.RefuseReason;
			return await _preRegisterRepository.Save(dbPreUser);
		}

		#endregion

		#region Validações

		private async Task FindParentParticipantAndValidateExistenceIfNeeded(CampaignSettingsModel campaignSettings, PreRegisteredUser user)
		{
			if (!campaignSettings.Parametrizations.AllowInformParentParticipantOnPreRegistration)
			{
				return;
			}

			UserBasicInfo parentParticipant = null;
			if (campaignSettings.Parametrizations.ParentParticipantSearchFieldOnPreRegistration == ParticipantSearchField.DOCUMENT)
			{
				parentParticipant = await this._userRepository.GetUserBasicInfoByDocument(user.CampaignId, user.ParentParticipantIdentifier);
			}
			else
			{
				parentParticipant = await this._userRepository.GetUserBasicInfoByClientUserId(user.CampaignId, user.ParentParticipantIdentifier);
			}

			if (parentParticipant != null)
			{
				LoggerFactory.GetLogger().Info("{} - Doc {} - Pré-Cadastro - Participante pai encontrado: {}",
						user.CampaignId, user.Document, parentParticipant.UserId);
				user.ParentParticipant = parentParticipant;
			}
			else if (campaignSettings.Parametrizations.RequiredExistingParentParticipantOnPreRegistration)
			{
				// TODO: melhorar a mensagem
				throw MotivaiException.ofValidation("Participante não encontrado pelo campo informado.");
			}
		}

		private async Task ValidateUserUsingCampaignConfiguration(PreRegisteredUser user)
		{
			if (user == null)
				throw MotivaiException.ofValidation("Usuário de pré-cadastro é obrigatório");
			if (user.CampaignId == null)
				throw MotivaiException.ofValidation("Campanha inválida.");

			var settings = await _campaignRepository.GetPreRegistrationConfig(user.CampaignId);
			user.ValidateUsingConfig(settings);
			await VerifyIfUserAlreadyRegisteredInCampaign(user.CampaignId, user.Type.Value, user.Document);
		}

		private async Task VerifyIfUserAlreadyRegisteredInCampaign(Guid campaignId, PersonType personType, string document)
		{
			await ValidatePersonType(campaignId, personType);

			if (await _preRegisterRepository.VerifyExistingDocument(campaignId, document))
				throw MotivaiException.ofValidation("Já existe um usuário para aprovação com esse CPF/CNPJ.");

			bool foundUserWithSameDocument = false;
			if (personType == PersonType.Fisica)
			{
				foundUserWithSameDocument = await _participantRepository.ExistCpfWithCampaign(document, campaignId);
			}
			else if (personType == PersonType.Juridica)
			{
				foundUserWithSameDocument = await _participantRepository.ExistCnpjWithCampaign(document, campaignId);
			}
			if (foundUserWithSameDocument)
			{
				throw MotivaiException.ofValidation("Este CPF/CNPJ já está cadastrado na campanha");
			}
		}

		private async Task ValidatePersonType(Guid campaignId, PersonType personType)
		{
			var preRegisterSettings = await _campaignRepository.GetPreRegistrationConfig(campaignId);

			if (preRegisterSettings.PersonTypeAllowedInPreRegister == PersonTypeAllowed.Fisica && personType != PersonType.Fisica)
			{
				throw MotivaiException.ofValidation("É permitido apenas CPF no pré-cadastro");
			}
			if (preRegisterSettings.PersonTypeAllowedInPreRegister == PersonTypeAllowed.Juridica && personType != PersonType.Juridica)
			{
				throw MotivaiException.ofValidation("É permitido apenas CNPJ no pré-cadastro");
			}
		}

		#endregion


		private async Task CreateParticipant(PreRegisteredUser preRegisteredUser)
		{
			// Check user with same document
			User user = null;
			if (preRegisteredUser.IsFisica())
			{
				user = await _userRepository.GetByCpf(preRegisteredUser.Document);
			}
			else if (preRegisteredUser.IsJuridica())
			{
				user = await _userRepository.GetByCnpj(preRegisteredUser.Document);
			}

			var campaignSettings = await _campaignRepository.GetSettings(preRegisteredUser.CampaignId);

			// Create user
			if (user == null)
			{
				user = preRegisteredUser.ToUser(campaignSettings);
				preRegisteredUser.GeneratedUserId = user.Id;
				await _userRepository.CreateUser(user);

				await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.PARTICIPANT_PRE_REGISTER,
					Guid.Empty, preRegisteredUser.CampaignId, user.Id, Guid.Empty, Guid.Empty
				);
			}
			var participant = preRegisteredUser.ToParticipant(user, campaignSettings);

			var generatedPassword = preRegisteredUser.Password;
			if (string.IsNullOrEmpty(generatedPassword))
			{
				generatedPassword = AlphanumericGenerator.GenerateId16();
			}
			participant.OverridePassword(generatedPassword);

			var preRegisterSettings = await _campaignRepository.GetPreRegistrationConfig(preRegisteredUser.CampaignId);
			if (preRegisterSettings.PersonalData.EnableMetadataAtPreRegister && preRegisteredUser.UserMetadata != null && preRegisteredUser.UserMetadata.Count > 0)
			{
				await this.userMetadataApp.SaveUserMetadata(user.Id, preRegisteredUser.CampaignId, participant.Id, preRegisteredUser.UserMetadata, "PreRegistration");
			}

			preRegisteredUser.GeneratedUserId = user.Id;
			await this._userRepository.CreateParticipant(user.Id, participant);

			await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.PARTICIPANT_PRE_REGISTER, Guid.Empty,
				preRegisteredUser.CampaignId, user.Id, participant.Id, Guid.Empty
			);

			await participantConfigurator.ExecutePostCreationActions(campaignSettings, user, participant, generatedPassword);
		}
	}
}
