﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson;
using Motivai.Users.Domain.IApp.UsersParticipantCampaigns;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Credify;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Sms;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.Services.Participants;
using MongoDB.Bson;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Models.PrivacyPolicy;

namespace Motivai.Users.App {
    public class UserParticipatCampaignApp : IUserParticipatCampaignApp {
        private readonly IUserRepository _userRepository;
        private readonly ICampaignRepository _campaignRepository;
        private readonly IUserParticipationCampaignRepository _userParticipationCampaignRepository;
        private readonly IEmailRepository _emailRepository;
        private readonly ISmsRepository _smsRepository;
        private readonly ICredifyRepository _credifyRepository;
        private readonly ParticipantImporter participantImporter;
        private readonly IUsersCallcenterActionRegisterRepository usersCallcenterActionRegisterRepository;
        private readonly ICampaignsGroupsApp campaignsGroupsApp;

        public UserParticipatCampaignApp(IUserRepository userRepository, ICampaignRepository campaignApiRepository,
            IUserParticipationCampaignRepository userParticipationCampaignRepository,
            IEmailRepository emailRepository, ISmsRepository smsRepository, ICredifyRepository credifyRepository,
            ParticipantImporter participantImporter, IUsersCallcenterActionRegisterRepository usersCallcenterActionRegisterRepository,
            ICampaignsGroupsApp campaignsGroupsApp) {
            _userRepository = userRepository;
            _campaignRepository = campaignApiRepository;
            _userParticipationCampaignRepository = userParticipationCampaignRepository;
            _emailRepository = emailRepository;
            _smsRepository = smsRepository;
            this._credifyRepository = credifyRepository;
            this.participantImporter = participantImporter;
            this.usersCallcenterActionRegisterRepository = usersCallcenterActionRegisterRepository;
            this.campaignsGroupsApp = campaignsGroupsApp;
        }

        public async Task<UserParticipantCampaign> GetUserParticipantById(Guid userParticipantId) {
            if (userParticipantId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Usuário.", true);
            return await _userParticipationCampaignRepository.Get(userParticipantId);
        }

        public async Task<List<Guid>> GetRankingChildrenById(Guid campaignId, Guid rankingId) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (rankingId == Guid.Empty)
                throw MotivaiException.ofValidation("Ranking inválido.");
            return await _campaignRepository.GetRankingChildrenById(campaignId, rankingId);
        }

        private async Task<User> SearchParticipantByDocumentOrLogin(Guid? campaignId, string document, string login) {
            if (document != null) {
                if (Cpf.IsCpf(document)) {
                    return await _userRepository.GetByCpf(document);
                } else if (Cnpj.IsCnpj(document)) {
                    return await _userRepository.GetByCnpj(document);
                } else {
                    throw MotivaiException.ofValidation("CPF ou CNPJ inválido.");
                }
            } else if (!string.IsNullOrEmpty(login)) {
                if (campaignId.HasValue && campaignId.Value != Guid.Empty) {
                    return await _userRepository.GetByLogin(campaignId.Value, login, false);
                }
                return await _userRepository.GetByLogin(login);
            }
            throw MotivaiException.ofValidation("Informe um filtro para pesquisar os participantes.");
        }

        public async Task<dynamic> SearchParticipant(Guid? campaignId, string document, string login) {
            if (string.IsNullOrEmpty(document) && string.IsNullOrEmpty(login))
                throw MotivaiException.ofValidation("Informe o CPF/CNPJ ou login para pesquisa.");

            User user = await SearchParticipantByDocumentOrLogin(campaignId, document, login);
            if (user == null)
                return null;

            return new {
                UserId = user.Id,
                    Name = user.Name,
                    CompanyName = user.CompanyName,
                    Rg = user.Rg,
                    Cpf = user.Cpf,
                    Cnpj = user.Cnpj,
                    Campaigns = user.UsersParticipantCampaign.Select(p => p.CampaignId).ToList(),
                    UsersParticipantCampaign = user.UsersParticipantCampaign,
            };
        }

        private static ParticipantInfo CreateParticipantInfo(User user, UserParticipantCampaign participant) {
            return new ParticipantInfo() {
                UserId = user.Id,
                    CampaignId = participant.CampaignId,
                    ParticipantId = participant.Id,
                    Login = participant.Login,
                    Name = user.Name,
                    CompanyName = user.CompanyName,
                    Rg = user.Rg,
                    Type = user.Type,
                    Cpf = user.Cpf,
                    Cnpj = user.Cnpj,
                    MainEmail = participant.GetMainEmail(),
                    MobilePhone = participant.Contact != null ? participant.Contact.MobilePhone : ""
            };
        }

        public async Task<List<ParticipantInfo>> SearchParticipantInCampaign(Guid campaignId) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            var users = await _userRepository.GetByCampaign(campaignId);
            if (users == null) return null;

            return users.Select(u => {
                var participant = u.GetParticipantByCampaign(campaignId);
                if (participant == null)
                    throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");
                return CreateParticipantInfo(u, participant);
            }).ToList();
        }

        public async Task<List<ParticipantInfo>> SearchParticipantInCampaign(Guid campaignId, string document = null,
            string cpf = null, string cnpj = null, string name = null) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            List<User> users = null;
            User user = null;

            if (string.IsNullOrEmpty(document)) {
                document = cpf ?? cnpj;
            }

            // Pesquisa pelo filtro informado
            if (document != null && Cpf.IsCpf(document)) {
                user = await _userRepository.GetByCpf(document);
            } else if (document != null && Cnpj.IsCnpj(document)) {
                user = await _userRepository.GetByCnpj(document);
            } else if (!string.IsNullOrEmpty(name)) {
                users = await _userRepository.GetByCampaignAndName(campaignId, name);
            } else {
                throw MotivaiException.ofValidation("Informe um filtro para pesquisar os participantes.");
            }

            if (user != null) {
                users = new List<User>() { user };
            }
            if (users == null)
                return null;
            return users.Select(u => {
                var participant = u.GetParticipantByCampaign(campaignId);
                if (participant == null) {
                    // throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");
                    return null;
                }
                return CreateParticipantInfo(u, participant);
            }).Where(u => u != null).ToList();
        }

        public async Task<bool> RememberPassword(Guid campaignId, PasswordRecoveryRequest model) {
            model.Validate();
            var user = await _userParticipationCampaignRepository.GetUserByCampaignAndLogin(campaignId, model.Login);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");
            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");
            if (participant.Contact == null || participant.Contact.MainEmail != model.Email) {
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");
            }
            string generatedPassword = AlphanumericGenerator.GenerateTrackNumber();
            participant.Password = new Senha(generatedPassword, generatedPassword);
            participant.Password.GerarNovoTokenAlterarSenha();
            await _userRepository.Save(user);
            await _emailRepository.SendResetPasswordEmail(campaignId, user.Id, participant.Id, generatedPassword);
            return true;
        }

        public async Task<string> SendVerificationCodeBySms(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do participante");

            var participantContact = await _userParticipationCampaignRepository.GetMainContactInfo(userId, campaignId);
            if (participantContact == null)
                throw MotivaiException.ofValidation("Participante não encontrado");

            var campaign = await _campaignRepository.GetSettings(campaignId);
            var from = "";
            if (campaign != null && campaign.Parametrizations != null) {
                from = campaign.Parametrizations.FromForTransactionalSms;
            }

            if (string.IsNullOrEmpty(participantContact.MobilePhone))
                throw MotivaiException.ofValidation("Usuário não possui número de celular cadastrado");

            var verificationCode = new Random().Next(10000, 99999);
            var smsText = $"Redefina sua senha utilizando o codigo: {verificationCode}";
            var smsSent = await _smsRepository.send(from, participantContact.MobilePhone, smsText);

            return verificationCode.ToString();
        }

        public async Task<bool> ResetPasswordWithSms(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do participante");

            var user = await _userRepository.Get(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado");

            var participant = user.GetParticipantByCampaign(campaignId);

            if (participant.Contact == null)
                throw MotivaiException.ofValidation("Usuário não possui dados de contato cadastrados");
            if (string.IsNullOrEmpty(participant.Contact.MobilePhone))
                throw MotivaiException.ofValidation("Usuário não possui número de celular cadastrado");

            var campaign = await _campaignRepository.GetSettings(participant.CampaignId);
            var from = "";
            if (campaign != null && campaign.Parametrizations != null) {
                from = campaign.Parametrizations.FromForTransactionalSms;
            }

            var generatedPassword = AlphanumericGenerator.GenerateId16();
            participant.Password = new Senha(generatedPassword, generatedPassword);
            participant.Password.GerarNovoTokenAlterarSenha();
            await _userRepository.Save(user);
            await _emailRepository.SendResetPasswordEmail(participant.CampaignId, user.Id, participant.Id, generatedPassword);

            var smsText = $"Sua senha foi redefinida e agora voce pode continuar resgatando. Lembre-se de altera-la em seu proximo acesso. Sua nova senha: {generatedPassword}";
            return await _smsRepository.send(from, participant.Contact.MobilePhone, smsText);
        }

        public async Task<bool> UpdateBalanceAndPointsByMechanics(Guid userParticipantId, UserTotalPoints userTotalPoints) {
            if (userParticipantId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do Participante da Campanha.");

            var userParticipantCampaign = await _userParticipationCampaignRepository.Get(userParticipantId);

            if (userParticipantCampaign == null)
                throw MotivaiException.ofValidation("Usuário Participante Campanha não encontrado pelo ID informado.");

            var user = await _userRepository.Get(userParticipantCampaign.UserId);

            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado pelo ID informado. Contate o Administrador do Sisrtema.");

            var userParticipantCampaignUpdate = user.UsersParticipantCampaign.FirstOrDefault(u => u.Id == userParticipantId);
            userParticipantCampaignUpdate.Balance = userTotalPoints.Balance;
            userParticipantCampaignUpdate.TotalBalanceMechanic = userParticipantCampaign.TotalBalanceMechanic;

            await _userRepository.Save(user);

            return true;
        }

        public async Task<dynamic> ImportUserIfDoesntExist(Guid campaignId, ParticipantIntegrationData participantIntegrationData) {
            return await participantImporter.ImportUserIfDoesntExist(campaignId, participantIntegrationData);
        }

        public async Task<List<string>> GetDistinctEmployee(Guid campaignId, string propertieEmployee) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID da Campanha.");

            return await _userParticipationCampaignRepository.GetDistinctEmployee(campaignId, propertieEmployee);
        }

        public async Task<dynamic> QueryUserInfoByDocument(Guid campaignId, string document) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (string.IsNullOrEmpty(document))
                throw MotivaiException.ofValidation("CNPJ/CPF é obrigatório.");

            User user = null;
            PersonType personType;
            if (Cpf.IsCpf(document)) {
                personType = PersonType.Fisica;
                user = await _userRepository.GetByCpf(document);
            } else if (Cnpj.IsCnpj(document)) {
                personType = PersonType.Juridica;
                user = await _userRepository.GetByCnpj(document);
            } else {
                throw MotivaiException.ofValidation("CNPJ/CPF inválido.");
            }

            var consultPersonDataParams = await this._campaignRepository.GetPersonDataConsultParametrizations(campaignId);

            if (user != null) {
                return ConsultPersonDataBuilder.BuildWithEnabledFieldsOrDefault(user, campaignId, consultPersonDataParams);
            }
            // Consulta na Credify
            var personData = await _credifyRepository.QueryDocument(personType, document);
            if (personData == null)
            {
                return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(document, personType);
            }
            return ConsultPersonDataBuilder.BuildByPointsDistributionPageProcess(personData, consultPersonDataParams);
        }

        public async Task<bool> SearchDocumentInCampaign(Guid campaignId, string document) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID da Campanha.");

            if (String.IsNullOrEmpty(document))
                throw MotivaiException.ofValidation("CPF/CNPJ deve ser informado.");

            return await _userParticipationCampaignRepository.ExistParticipantWithDocument(campaignId, Guid.Empty, document);
        }

        public async Task<bool> UpdateUserContactById(Guid campaignId, Guid userId, UsersCallcenterAction callcenterActionRegister) {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID da Campanha.");
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Deve ser informado o ID do participante");

            if (callcenterActionRegister == null) {
                throw MotivaiException.ofValidation("Informações de contato são obrigatórias.");
            }

            callcenterActionRegister.Validate();
            callcenterActionRegister.Action = "PARTICIPANT_CONTACT_CHANGE";

            var contactPayload = callcenterActionRegister.ToContact();

            if (await _userParticipationCampaignRepository.UpdateUserContactById(campaignId, userId, contactPayload)) {
                await usersCallcenterActionRegisterRepository.Save(callcenterActionRegister);
            }
            else {
                throw MotivaiException.ofValidation("Ocorreu um erro ao atualizar as informações de contato.");
            }

            await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.CALLCENTER_CONTACT_UPDATE,
                Guid.Empty, campaignId, userId, Guid.Empty, Guid.Empty
            );
            return true;
        }
    }
}
