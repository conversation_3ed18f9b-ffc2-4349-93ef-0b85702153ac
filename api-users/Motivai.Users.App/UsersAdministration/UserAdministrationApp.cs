﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Enums.UsersAdministration;
using Motivai.SharedKernel.Domain.Model.Structures;
using Motivai.SharedKernel.Domain.Repository;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.App.ex;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.UsersAdministrators;
using Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog;
using Motivai.Users.Domain.IApp.UsersAdministrations;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Roles;
using Motivai.Users.Domain.IRepository.UsersAdministrations;
using Motivai.Users.Domain.IRepository.UsersAdministrations.AccessLog;
using Motivai.Users.Domain.Models.PasswordRecovery;
using Motivai.Users.Domain.Models.UserAdministration;
using Microsoft.AspNetCore.Http;

namespace Motivai.Users.App
{
    public class UserAdministrationApp : IUserAdministrationApp
    {
        private static readonly string USER_ADMINISTRATOR_MFA_KEY_PREFIX = "usr-admin-mfa-token";
        private static readonly string USER_ADMINISTRATOR_SESSION_DATA_KEY_PREFIX = "usr-admin-session-data";

        private static readonly int CODE_EXPIRATION = 300;

        private static readonly Random RANDOM = new Random();

        private readonly ICache cache;
        private readonly IHttpContextAccessor _context;
        private readonly IUserAdministrationRepository _userAdministrationRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IUserAdministrationAccessLogRepository accessLogRepository;
        private readonly IUserAdministrationActionLogApp actionLogsApp;
        private readonly IBusinessUnitCommomRepository _buRepository;
        private readonly IEmailRepository _emailRepository;

        public UserAdministrationApp(ICache cache, IUserAdministrationRepository userAdministrationRepository,
            IRoleRepository roleRepository, IUserAdministrationAccessLogRepository accessLogRepository,
            IUserAdministrationActionLogApp actionLogsApp, IHttpContextAccessor context,
            IBusinessUnitCommomRepository buRepository, IEmailRepository _emailRepository)
        {
            this.cache = cache;
            this._context = context;
            this._userAdministrationRepository = userAdministrationRepository;
            this._buRepository = buRepository;
            this._roleRepository = roleRepository;
            this.accessLogRepository = accessLogRepository;
            this.actionLogsApp = actionLogsApp;
            this._emailRepository = _emailRepository;
        }

        private Guid BuId()
        {
            if (!string.IsNullOrEmpty(this._context.HttpContext.Request.Headers["x-bu"]))
            {
                return Guid.Parse(this._context.HttpContext.Request.Headers["x-bu"]);
            }
            return Guid.Empty;
        }

        private async Task<List<Guid>> GetChildrenBus()
        {
            if (BuId() == Guid.Empty)
                return new List<Guid>();

            var bus = await this._buRepository.GetChildrens(BuId());
            return bus ?? new List<Guid>();
        }

        public async Task<UserAdministration> Get(Guid userId)
        {
            return await _userAdministrationRepository.Get(await GetChildrenBus(), userId);
        }

        public Task<string> GetUserName(Guid userId)
        {
            return _userAdministrationRepository.GetUserName(userId);
        }

        public async Task<Guid> AuthenticateAdministratorWithMfa(UserAdministrationAuthenticationRequest authenticationRequest)
        {
            var sessionId = Guid.NewGuid();
            var authenticationResponse = await this.AuthenticateAdministrator(authenticationRequest, sessionId);

            try
            {
                await cache.Set(USER_ADMINISTRATOR_SESSION_DATA_KEY_PREFIX + sessionId, authenticationResponse, CODE_EXPIRATION);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Envio",
                    $"Erro ao cachear informações da sessão do usuário: {authenticationResponse.Id} - Sessão {sessionId}", true);
                throw MotivaiException.of("Não foi possível efetuar a autenticação, por favor, tente novamente.", ex);
            }

            await this.SendAuthenticationMfaToken(authenticationResponse, sessionId);

            return sessionId;
        }

        public async Task<UserAdministrationAuthenticationResponse> AuthenticateAdministratorWithoutMfa(UserAdministrationAuthenticationRequest authenticationRequest)
        {
            var sessionId = Guid.NewGuid();
            return await this.AuthenticateAdministrator(authenticationRequest, sessionId);
        }

        private async Task<UserAdministrationAuthenticationResponse> AuthenticateAdministrator(UserAdministrationAuthenticationRequest authenticationRequest, Guid sessionId)
        {
            authenticationRequest.Validate();

            UserAdministration userAdministration = null;
            Role role = null;
            try
            {
                userAdministration = await _userAdministrationRepository.Get(authenticationRequest.Login);

                if (userAdministration == null)
                    throw new AuthenticationFailedException(UserAdministrationAccessLogAction.INVALID_USER, "Usuário e/ou senha inválido.");

                if (!userAdministration.Active)
                    throw new AuthenticationFailedException(UserAdministrationAccessLogAction.INACTIVE_USER, "Usuário e/ou senha inválido.");

                userAdministration.Password.ValidarSenha(authenticationRequest.Password);

                var buStatus = await _buRepository.IsActive(userAdministration.BuId);
                if (!buStatus)
                    throw new AuthenticationFailedException(UserAdministrationAccessLogAction.INACTIVE_BU, "Usuário e/ou senha inválido.");

                role = await _roleRepository.Get(null, userAdministration.RoleId);
                if (role == null)
                    throw new AuthenticationFailedException(UserAdministrationAccessLogAction.INACTIVE_ROLE, "Usuário e/ou senha inválido.");
                if (!role.Active)
                    throw new AuthenticationFailedException(UserAdministrationAccessLogAction.INACTIVE_ROLE, "Usuário e/ou senha inválido.");
            }
            catch (AuthenticationFailedException ex)
            {
                await RegisterTryLogin(ex.Action, authenticationRequest, userAdministration);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (MotivaiException)
            {
                await RegisterTryLogin(UserAdministrationAccessLogAction.INVALID_PASSWORD, authenticationRequest, userAdministration);
                throw;
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Admin - Authentication", "Erro durante a autenticação do usuário");
                throw ex.WrapIfNotValidationException("AUTHENTICATION_FAILED", "Ocorreu um erro durante a autenticação.");
            }

            var accessLog = UserAdministrationAccessLog.OfLoggedWithPassword(sessionId, userAdministration, authenticationRequest.LocationInfo);
            await accessLogRepository.RegisterLog(accessLog);

            await SendPlataformActionNotification("Login", userAdministration, authenticationRequest.LocationInfo);

            return new UserAdministrationAuthenticationResponse
            {
                Id = userAdministration.Id,
                Login = userAdministration.Login,
                Name = userAdministration.Name,
                Email = userAdministration.Email,
                RoleId = userAdministration.RoleId,
                BuId = userAdministration.BuId,
                ChangePassword = userAdministration.Password.TokenAlterarSenha != null,
                StartComponent = role.StartComponent.HasValue ? role.StartComponent.Value.ToString() : StartComponentType.DASHBOARD.ToString(),
                LayoutType = role.LayoutType.HasValue ? role.LayoutType.Value.ToString() : LayoutType.FULL.ToString()
            };
        }

        private async Task SendAuthenticationMfaToken(UserAdministrationAuthenticationResponse authenticationResponse, Guid sessionId)
        {
            var securityToken = GenerateVerificationCode();
            try
            {
                await cache.Set(USER_ADMINISTRATOR_MFA_KEY_PREFIX + sessionId, securityToken, CODE_EXPIRATION);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Admin Authentication MFA - Envio",
                    $"Erro ao cachear código de segurança MFA do usuário {authenticationResponse.Id} - Sessão {sessionId}", true);
                throw MotivaiException.of("Não foi efetuar a autenticação, por favor, tente novamente.", ex);
            }

            try
            {
                await this._emailRepository.SendAdminToken(authenticationResponse.Id, authenticationResponse.Name,
                    authenticationResponse.Email, SecurityTokenSendMethod.EMAIL, securityToken);
            }
            catch (MotivaiException ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Admin Authentication MFA - Validação",
                    $"Erro durante validação do código de segurança para o usuário {authenticationResponse.Id}", true);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Admin Authentication MFA - Envio",
                    $"Erro ao enviar código de segurança para o usuário {authenticationResponse.Id}", true);
                throw MotivaiException.of("Não foi possível efetuar a autenticação, por favor, tente novamente.", ex);
            }
        }

        private string GenerateVerificationCode()
        {
            return RANDOM.Next(10000000, 99999999).ToString();
        }

        public async Task<UserAdministrationAuthenticationResponse> ValidateAuthenticatedMfa(UserAdministrationAuthenticationValidateRequest authenticationRequest)
        {
            if (authenticationRequest == null)
            {
                throw MotivaiException.ofValidation("Validação da sessão inválida");
            }
            authenticationRequest.Validate();

            var sessionId = authenticationRequest.SessionId;
            try
            {
                var cachedSecurityCode = await cache.Get<string>(USER_ADMINISTRATOR_MFA_KEY_PREFIX + sessionId);
                if (string.IsNullOrEmpty(cachedSecurityCode))
                {
                    throw MotivaiException.ofValidation("INVALID_SESSION", "Sessão expirada.");
                }

                if (cachedSecurityCode != authenticationRequest.SecurityCode)
                {
                    throw new AuthenticationFailedException(UserAdministrationAccessLogAction.INVALID_SECURITY_CODE, "Código de segurança inválido.");
                }

                var userAdministration = await cache.Get<UserAdministrationAuthenticationResponse>(USER_ADMINISTRATOR_SESSION_DATA_KEY_PREFIX + sessionId);
                if (userAdministration == null)
                {
                    throw MotivaiException.ofValidation("INVALID_SESSION", "Sessão expirada.");
                }

                await cache.Remove(USER_ADMINISTRATOR_MFA_KEY_PREFIX + sessionId);
                await cache.Remove(USER_ADMINISTRATOR_SESSION_DATA_KEY_PREFIX + sessionId);

                var accessLog = UserAdministrationAccessLog.OfSecurityCodeValidated(sessionId, userAdministration, authenticationRequest.LocationInfo);
                await accessLogRepository.RegisterLog(accessLog);
                return userAdministration;
            }
            catch (AuthenticationFailedException ex)
            {
                await this.accessLogRepository.RegisterLog(
                    UserAdministrationAccessLog.OfAction(sessionId, UserAdministrationAccessLogAction.INVALID_SECURITY_CODE,
                        authenticationRequest.LocationInfo)
                );
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (MotivaiException ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Validação",
                    $"Erro durante validação do código de segurança para o usuário - Sessão: {sessionId}", true);
                throw;
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Motivai Authentication MFA - Validação",
                    $"Erro durante validação do código de segurança para o usuário - Sessão: {sessionId}", true);
                throw MotivaiException.of("Não foi possível validar o código de segurança, por favor, tente novamente.", ex);
            }
        }

        private Task RegisterTryLogin(UserAdministrationAccessLogAction action,
            UserAdministrationAuthenticationRequest authenticationRequest, UserAdministration userAdmin = null)
        {
            return this.accessLogRepository.RegisterLog(UserAdministrationAccessLog.OfAuthenticationRequest(action, authenticationRequest, userAdmin));
        }

        public async Task<List<UserAdministration>> GetUsers(string name, string email, string login, int? skip, int? limit)
        {
            return await _userAdministrationRepository.GetUsers(await GetChildrenBus(), name, email, login, skip, limit);
        }

        public async Task<string> CreateUser(UserAdministrationModel userModel)
        {
            UserAdministration user = await CreateUserAndValidate(userModel);

            string generatedPassword = AlphanumericGenerator.GenerateTrackNumber();
            user.Password = new Senha(generatedPassword, generatedPassword);
            user.Password.GerarNovoTokenAlterarSenha();

            await RegisterActionLog("Cadastro", user.Id, userModel);

            if (await _userAdministrationRepository.Create(user))
            {
                await SendPlataformActionNotification("Cadastro de Usuário", user);

                return generatedPassword;
            }
            return null;
        }

        public async Task<Guid> CreateAndGetId(UserAdministrationModel userModel)
        {
            UserAdministration user = await CreateUserAndValidate(userModel);

            string password = userModel.UseLoginAsPassword ? user.Login : AlphanumericGenerator.GenerateTrackNumber();
            user.UpdatePassword(password);

            await RegisterActionLog("Cadastro", user.Id, userModel);
            await _userAdministrationRepository.Create(user);

            await SendPlataformActionNotification("Criação de Usuário", user);

            return user.Id;
        }

        private async Task<UserAdministration> CreateUserAndValidate(UserAdministrationModel userModel)
        {
            if (userModel == null)
                throw MotivaiException.ofValidation("Usuário inválido.");
            userModel.Validate();

            var user = UserAdministration.From(userModel);
            if (await _userAdministrationRepository.IsLoginIsUseByOtherThan(user.Login, Guid.Empty))
            {
                throw MotivaiException.ofValidation("Login já está em uso.");
            }

            UserAdministration operationUser = await ValidateAccessWithBu(user, userModel);
            user.CreationBuId = operationUser.BuId;

            return user;
        }

        private async Task<UserAdministration> ValidateAccessWithBu(UserAdministration user,
            UserAdministrationModel userModel)
        {
            var busIds = await GetChildrenBus();
            if (!busIds.Contains(user.BuId))
            {
                throw MotivaiException.ofValidation("BU não autorizada.");
            }

            var operationUser = await _userAdministrationRepository.Get(busIds, userModel.OperationUser.UserId);
            if (operationUser == null || !operationUser.Active)
            {
                throw MotivaiException.ofValidation("Usuário da operação inválido.");
            }
            userModel.OperationUser.Email = operationUser.Email;
            userModel.OperationUser.Login = operationUser.Login;

            return operationUser;
        }

        public async Task<bool> UpdateUser(Guid userId, UserAdministrationModel userModel)
        {
            if (userModel == null)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário administrador inválido.");

            var user = await _userAdministrationRepository.Get(await GetChildrenBus(), userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário administrador não encontrado.");

            user.Update(userModel);

            await ValidateAccessWithBu(user, userModel);

            await RegisterActionLog("Atualização", userId, userModel);

            if (await _userAdministrationRepository.Save(user))
            {
                await SendPlataformActionNotification("Atualização de Usuário", user);
                return true;
            }
            return false;
        }

        public async Task<bool> DeleteUser(Guid userId)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário administrador inválido.");
            return await _userAdministrationRepository.Delete(userId);
        }

        public async Task<bool> ChangePassword(Guid userId, UserAdministrationPasswordModel passwordModel)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário administrador inválido.");
            var user = await _userAdministrationRepository.Get(await GetChildrenBus(), userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário administrador não encontrado.");
            passwordModel.Validate();

            try
            {
                user.Password.ValidarSenha(passwordModel.CurrentPassword);
            }
            catch (Exception)
            {
                throw MotivaiException.ofValidation("A senha atual inválida.");
            }
            user.Password.SetSenha(passwordModel.NewPassword, passwordModel.Confirmation, false);

            await RegisterActionLog("Alteração Senha", user, passwordModel);

            if (await _userAdministrationRepository.Save(user))
            {
                await SendPlataformActionNotification("Alteração de Senha", user);
                return true;
            }
            return false;
        }

        public async Task<string> ResetPassword(Guid userId, UserAdministrationAction actionLog)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do usuário administrador inválido.");
            var user = await _userAdministrationRepository.Get(await GetChildrenBus(), userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário administrador não encontrado.");

            string generatedPassword = AlphanumericGenerator.GenerateTrackNumber();
            user.UpdatePassword(generatedPassword);
            user.UpdateOperationUser = actionLog.OperationUser;
            user.UpdateLocationInfo = actionLog.LocationInfo;

            await RegisterActionLog("Reset de Senha", user, actionLog);

            if (await _userAdministrationRepository.Save(user))
            {
                await SendPlataformActionNotification("Reset de Senha", user);
                return generatedPassword;
            }
            return null;
        }

        private async Task RegisterActionLog(string action, Guid userId, UserAdministrationModel userModel)
        {
            var log = new UserActionOperationLog()
            {
                AccessDate = DateTime.UtcNow,
                OperationUser = userModel.OperationUser,
                LocationInfo = userModel.LocationInfo,
                Module = "Segurança",
                Feature = "Usuários Administradores",
                Action = action,
                Metadata = new Dictionary<string, string>()
                {
                    { "User Id", userId.ToString() },
                    { "Login", userModel.Login },
                    { "BU Id", userModel.BuId.ToString() },
                    { "Ativo", userModel.Active ? "Sim" : "Não" },
                    { "E-mail", userModel.Email },
                }
            };
            await actionLogsApp.RegisterLog(log);
        }

        private async Task<bool> SendPlataformActionNotification(string action, UserAdministration user,
                LocationInfo locationInfo = null)
        {
            try
            {
                var emailToNotify = action == "Login" ? user.Email : user.ContactEmail;
                if (string.IsNullOrEmpty(emailToNotify))
                {
                    return false;
                }

                var operationUser = user.GetLastOperationUser();
                var notification = new
                {
                    origin = "Admin",
                    action = action,
                    operationDate = DateTime.UtcNow,
                    operationUser = operationUser,
                    emailToNotify  = emailToNotify,
                    locationInfo = locationInfo ?? user.GetLastLocationInfo(),
                    metadata = new List<Entry<string, string>>()
                    {
                        Entry.Of("Login", user.Login),
                        Entry.Of("E-mail", user.Email),
                        Entry.Of("CPF", user.Document),
                        Entry.Of("Nome", user.Name),
                        Entry.Of("Celular", user.MobilePhone),
                        Entry.Of("E-mail de Contato", user.ContactEmail),
                        Entry.Of("Ativo", user.Active ? "Sim" : "Não")
                    }
                };

                return await this._emailRepository.SendPlataformActionNotification(notification);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Admin - Users", "Erro ao enviar notificação de segurança");
                return false;
            }
        }

        private async Task RegisterActionLog(string action, UserAdministration user, UserAdministrationAction actionLog)
        {
            var log = new UserActionOperationLog()
            {
                AccessDate = DateTime.UtcNow,
                OperationUser = actionLog.OperationUser,
                LocationInfo = actionLog.LocationInfo,
                Module = "Segurança",
                Feature = "Usuários Administradores",
                Action = action,
                Metadata = new Dictionary<string, string>()
                {
                    { "User Id", user.Id.ToString() },
                    { "Login", user.Login },
                    { "BU Id", user.BuId.ToString() },
                    { "Ativo", user.Active ? "Sim" : "Não" },
                    { "E-mail", user.Email },
                }
            };
            await actionLogsApp.RegisterLog(log);
        }
    }
}
