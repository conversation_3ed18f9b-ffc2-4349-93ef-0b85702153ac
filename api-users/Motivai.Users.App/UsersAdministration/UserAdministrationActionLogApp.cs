using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog;
using Motivai.Users.Domain.IApp.UsersAdministrations;
using Motivai.Users.Domain.IRepository.UsersAdministrations.ActionLog;

namespace Motivai.Users.App.UsersAdministration
{
    public class UserAdministrationActionLogApp : IUserAdministrationActionLogApp
    {
        private readonly IUserAdministrationActionLogRepository actionLogRepository;

        public UserAdministrationActionLogApp(IUserAdministrationActionLogRepository actionLogRepository)
        {
            this.actionLogRepository = actionLogRepository;
        }

        public async Task<bool> RegisterLog(UserActionOperationLog operationLog)
        {
            try
            {
                var actionLog = UserAdministrationActionLog.OfOperationLog(operationLog);
                await actionLogRepository.RegisterLog(actionLog);
                return true;
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "User Admin Logs", "Erro durante o registro de log do admin.");
                return false;
            }
        }
    }
}