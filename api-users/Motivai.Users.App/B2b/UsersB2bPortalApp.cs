using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums.B2bPortal;
using Motivai.SharedKernel.Domain.Repository;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.B2b;
using Motivai.Users.Domain.IRepository.B2b;

namespace Motivai.Users.App.B2b {
    public class UsersB2bPortalApp : IUsersB2bPortalApp {
        private IUsersB2bPortalRepository _usersB2bRepository;
        private IBusinessUnitCommomRepository _buRepository;
        public UsersB2bPortalApp(IUsersB2bPortalRepository usersB2bRepository, IBusinessUnitCommomRepository buRepository) {
            this._usersB2bRepository = usersB2bRepository;
            this._buRepository = buRepository;
        }

        private async Task<List<Guid>> GetChildrenBus(Guid buId) {
            if (buId == Guid.Empty)
                return null;

            var bus = await this._buRepository.GetChildrens(buId);
            return bus;
        }

        public async Task<dynamic> Authenticate(string login, string password) {
            login.ForNullOrEmpty("Login é obrigatório");
            password.ForNullOrEmpty("Senha é obrigatório");

            var user = await _usersB2bRepository.FindByLogin(login);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário e/ou Senha inválidos");

            user.Password.ValidarSenha(password);
            return new {
                Id = user.Id,
                Name = user.Name,
                Type = user.Type.Value,
                Email = user.Email,
                Document = user.Document,
                MobilePhone = user.MobilePhone,
                BuId = user.BuId,
                Active = user.Active
            };
        }

        public async Task<UserB2bPortal> Create(Guid buId, UserB2bPortal user) {
            if (user == null)
                throw MotivaiException.ofValidation("Usuário deve ser informado para criar");

            user.BuId = buId;
            user.CreateDate = DateTime.UtcNow;
            user.Validate();

            var foundByLogin = await _usersB2bRepository.FindByLogin(user.Login);
            if (foundByLogin != null)
                throw MotivaiException.ofValidation("Login informado já está cadastrado");

            var foundByDocument = await _usersB2bRepository.FindByDocument(user.Document);
            if (foundByDocument != null)
                throw MotivaiException.ofValidation("Documento informado já está cadastrado");

            var password = AlphanumericGenerator.GenerateTrackNumber();
            user.Password = new Senha(password, password);

            await _usersB2bRepository.Save(user);
            user.GeneratedPassword = password;

            return user;
        }

        public async Task<List<UserB2bPortal>> Find(Guid buId, string document, int skip, int limit) {
            var bus = await GetChildrenBus(buId);
            return await _usersB2bRepository.Find(bus, document, skip, limit);
        }

        public async Task<UserB2bPortal> FindById(Guid id) {
            if (id == Guid.Empty)
                throw MotivaiException.ofValidation("Id inválido");
            return await _usersB2bRepository.FindById(id);
        }

        public async Task<UserB2bPortal> ResetPassword(Guid userId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Id inválido");

            var user = await _usersB2bRepository.FindById(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado pelo Id informado");

            var password = AlphanumericGenerator.GenerateTrackNumber();
            user.Password = new Senha(password, password);

            await _usersB2bRepository.Save(user);
            user.GeneratedPassword = password;

            return user;
        }

        public async Task<bool> Update(UserB2bPortal user) {
            if (user == null)
                throw MotivaiException.ofValidation("Usuário deve ser informado para atualizar");
            if (user.Id == Guid.Empty)
                throw MotivaiException.ofValidation("Id do usuário deve ser informado para atualizar");

            user.StartValidation();
            var foundByLogin = await _usersB2bRepository.FindByLogin(user.Login);
            if (foundByLogin != null && foundByLogin.Id != user.Id)
                throw MotivaiException.ofValidation("Login informado já está cadastrado");

            var foundByDocument = await _usersB2bRepository.FindByDocument(user.Document);
            if (foundByDocument != null && foundByDocument.Id != user.Id)
                throw MotivaiException.ofValidation("Documento informado já está cadastrado");

            var dbUser = await _usersB2bRepository.FindById(user.Id);
            if (dbUser == null)
                throw MotivaiException.ofValidation("Usuário não encontrado pelo Id informado");

            dbUser.UpdateFieldsFrom(user);
            return await _usersB2bRepository.Save(dbUser) != null;
        }

        public async Task<bool> UpdatePassword(Guid userId, dynamic obj) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Id do usuário deve ser informado");
            if (obj == null)
                throw MotivaiException.ofValidation("É necessário informar a senha para atualizar");
            if (obj.password == null)
                throw MotivaiException.ofValidation("É necessário informar a senha para atualizar");
            if (string.IsNullOrEmpty(obj.password.ToString()))
                throw MotivaiException.ofValidation("É necessário informar a senha para atualizar");
            if (obj.password.ToString().Length < 6)
                throw MotivaiException.ofValidation("A senha deve conter pelo menos 6 caracteres");
            if (obj.currentPassword == null)
                throw MotivaiException.ofValidation("A senha atual deve ser informada");
            if (string.IsNullOrEmpty(obj.currentPassword.ToString()))
                throw MotivaiException.ofValidation("A senha atual deve ser informada");

            var user = await _usersB2bRepository.FindById(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado pelo Id informado");

            user.Password.AlterarSenha(obj.currentPassword.ToString(), obj.password.ToString(), obj.password.ToString());
            await _usersB2bRepository.Save(user);

            return true;
        }
    }
}