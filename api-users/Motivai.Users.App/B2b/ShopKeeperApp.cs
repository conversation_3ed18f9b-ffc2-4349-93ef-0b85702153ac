using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Repository;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.B2b;
using Motivai.Users.Domain.IApp.B2b;
using Motivai.Users.Domain.IRepository.B2b;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.App.B2b {
    public class ShopKeeperApp : IShopKeeperApp {
        private readonly IUserRepository _userRepository;
        private readonly ICampaignRepository _campaignRepository;
        private readonly IShopkeeperRepository _shopkeeperRepository;
        private readonly IBusinessUnitCommomRepository _buRepository;

        public ShopKeeperApp(IUserRepository userRepository, ICampaignRepository campaignRepository,
            IShopkeeperRepository shopkeeperRepository, IBusinessUnitCommomRepository buRepository) {
            this._userRepository = userRepository;
            this._campaignRepository = campaignRepository;
            this._shopkeeperRepository = shopkeeperRepository;
            this._buRepository = buRepository;
        }

        private async Task<List<Guid>> GetChildrenBus(Guid buId) {
            if (buId == Guid.Empty)
                return null;

            return await this._buRepository.GetChildrens(buId);
        }

        private static ParticipantInfo CreateParticipantInfo(User user, UserParticipantCampaign participant) {
            return new ParticipantInfo() {
                UserId = user.Id,
                    CampaignId = participant.CampaignId,
                    ParticipantId = participant.Id,
                    Login = participant.Login,
                    Name = user.Name,
                    CompanyName = user.CompanyName,
                    Rg = user.Rg,
                    Type = user.Type,
                    Cpf = user.Cpf,
                    Cnpj = user.Cnpj,
                    MainEmail = participant.GetMainEmail(),
                    MobilePhone = participant.Contact != null ? participant.Contact.MobilePhone : ""
            };
        }

        // Usado no processamento de pedidos B2B para poder ler ou criar o participante na campanha
        public async Task<ParticipantInfo> GetOrCreateParticipantInfo(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            var dbShopkeeper = await _shopkeeperRepository.FindByUserId(userId, null);
            if (dbShopkeeper == null)
                throw MotivaiException.ofValidation("Nenhum lojista encontrado para este usuário");

            dbShopkeeper.AddCampaign(campaignId, true);
            await Update(dbShopkeeper.Id, dbShopkeeper, Guid.Empty);

            var user = await _userRepository.Get(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado.");

            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant == null)
                throw MotivaiException.ofValidation("Participante não encontrado para lojista informado");

            return CreateParticipantInfo(user, participant);
        }

        // ! TODO: Verificar quem usa essa API pois no Catálogo em Regiões ainda está utilizando o Minha Conta - Main Address
        public async Task<Address> GetAddressByUser(Guid userId, Guid buId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do lojista inválido.");

            var bus = await GetChildrenBus(buId);
            var shopkeeper = await this._shopkeeperRepository.FindByUserId(userId, bus);
            if (shopkeeper == null)
                throw MotivaiException.ofValidation("Lojista não encontrado.");

            return shopkeeper.Address;
        }

        ///<summary>
        /// Pesquisa o lojista pelo nome ou documento.
        /// * Migração: definir se nome e documento do User será movido para o ShopKeepers, nesse caso alterar aqui.
        ///</summary>
        public async Task<List<ShopKeeperInfo>> SearchShopKeepers(string term, Guid buId) {
            if (string.IsNullOrEmpty(term))
                throw MotivaiException.ofValidation("Informe o nome ou documento para pesquisa");

            var bus = await GetChildrenBus(buId);
            List<Shopkeeper> shopkeepers = null;
            if (Cnpj.IsCnpj(term) || Cpf.IsCpf(term)) {
                var shopkeeper = await _shopkeeperRepository.FindByDocument(term, bus);
                if (shopkeeper != null) {
                    shopkeepers = new List<Shopkeeper>();
                    shopkeepers.Add(shopkeeper);
                }
            } else {
                shopkeepers = await _shopkeeperRepository.Find(null, term, 0, 20, bus);
            }

            if (shopkeepers == null || shopkeepers.Count == 0) return null;
            return shopkeepers.Select(s => new ShopKeeperInfo() {
                UserId = s.UserId,
                    Name = s.Name,
                    Document = s.Document
            }).ToList();
        }

        public async Task<List<Guid>> FindShopkeeperCampaigns(Guid shopkeeperId, Guid buId) {
            if (shopkeeperId == Guid.Empty)
                throw MotivaiException.ofValidation("Informe o ID do lojista para pesquisar");

            var bus = await GetChildrenBus(buId);
            var shopkeeper = await _shopkeeperRepository.FindById(shopkeeperId, bus);
            if (shopkeeper == null)
                throw MotivaiException.ofValidation("Lojista não encontrado pelo ID informado");
            if (shopkeeper.Campaigns == null)
                return null;

            return shopkeeper.Campaigns.Where(x => x.Active).Select(x => x.CampaignId).ToList();
        }

        public async Task<Shopkeeper> Create(Shopkeeper shopkeeper, Guid buId) {
            if (shopkeeper == null)
                throw MotivaiException.ofValidation("Lojista deve ser informado para inserir");

            shopkeeper.BuId = buId;
            shopkeeper.Validate();

            // verifica lojista com mesmo documento
            var shopkeeperByDocument = await _shopkeeperRepository.FindByDocument(shopkeeper.Document, null);
            if (shopkeeperByDocument != null)
                throw MotivaiException.ofValidation("Já existe um lojista com o mesmo documento");

            // cria lojista e usuario
            var user = shopkeeper.CreateUser();
            await _shopkeeperRepository.Save(shopkeeper);
            await _userRepository.Save(user);

            // atualiza lojista com o id do lojista gerado
            shopkeeper.UserId = user.Id;
            await _shopkeeperRepository.Save(shopkeeper);

            return shopkeeper;
        }

        public async Task<bool> Update(Guid shopkeeperId, Shopkeeper shopkeeper, Guid buId) {
            if (shopkeeper == null)
                throw MotivaiException.ofValidation("Lojista deve ser informado para atualizar");
            if (shopkeeperId == Guid.Empty)
                throw MotivaiException.ofValidation("Id do lojista é inválido");

            shopkeeper.Id = shopkeeperId;
            shopkeeper.Validate();

            // Verifica se lojista existe
            var bus = await GetChildrenBus(buId);
            var dbShopkeeper = await _shopkeeperRepository.FindById(shopkeeperId, bus);
            if (dbShopkeeper == null)
                throw MotivaiException.ofValidation("Lojista não encontrado pelo ID informado");

            // verifica lojista com mesmo documento
            var shopkeeperByDocument = await _shopkeeperRepository.FindByDocument(shopkeeper.Document, null);
            if (shopkeeperByDocument != null && shopkeeperByDocument.Id != shopkeeperId)
                throw MotivaiException.ofValidation("Já existe um lojista com o mesmo documento");

            // atualiza lojista
            dbShopkeeper.UpdateFields(shopkeeper);
            await _shopkeeperRepository.Save(dbShopkeeper);

            // atualiza usuário
            var user = await _userRepository.Get(dbShopkeeper.UserId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado para o lojista informado. Entre em contato com o suporte do sistema");

            dbShopkeeper.UpdateUserFields(user);
            await _userRepository.Save(user);

            return true;
        }

        public async Task<List<Shopkeeper>> Find(string document, string name, int skip, int limit, Guid buId) {
            var bus = await GetChildrenBus(buId);
            return await _shopkeeperRepository.Find(document, name, skip, limit, bus);
        }

        public async Task<Shopkeeper> FindById(Guid id, Guid buId) {
            if (id == Guid.Empty)
                throw MotivaiException.ofValidation("Id do lojista é inválido");

            var bus = await GetChildrenBus(buId);
            return await _shopkeeperRepository.FindById(id, bus);
        }
    }

}