using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IApp.Security;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Sms;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.PasswordRecovery;

namespace Motivai.Users.App.Security
{
    public class PasswordRecoveryService : IPasswordRecoveryService
    {
        private readonly ICampaignRepository campaignRepository;
        private readonly IUserRepository userRepository;
        private readonly IUserParticipationCampaignRepository userParticipationCampaignRepository;
        private readonly IAccountOperatorRepository accountOperatorRepository;
        private readonly IEmailRepository emailRepository;
        private readonly ISmsRepository smsRepository;
        private readonly ICampaignsGroupsApp campaignsGroupsApp;

        public PasswordRecoveryService(ICampaignRepository campaignRepository,
            IUserRepository userRepository, IUserParticipationCampaignRepository userParticipationCampaignRepository,
            IAccountOperatorRepository accountOperatorRepository,
            IEmailRepository emailRepository, ISmsRepository smsRepository, ICampaignsGroupsApp campaignsGroupsApp)
        {
            this.campaignRepository = campaignRepository;
            this.userRepository = userRepository;
            this.userParticipationCampaignRepository = userParticipationCampaignRepository;
            this.accountOperatorRepository = accountOperatorRepository;
            this.emailRepository = emailRepository;
            this.smsRepository = smsRepository;
            this.campaignsGroupsApp = campaignsGroupsApp;
        }

        #region Pesquisas

        private async Task<ParticipantRecoveryContact> GetParticipantContact(Guid campaignId, Guid userId)
        {
            var participant = await userParticipationCampaignRepository.Get(userId, campaignId);
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            return ParticipantRecoveryContact.OfParticipant(participant);
        }

        private async Task<ParticipantRecoveryContact> GetAccountOperatorContact(Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId)
        {
            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            var accountOperatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (accountOperatorLogin == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            return ParticipantRecoveryContact.OfAccountOperator(accountOperator, accountOperatorLogin);
        }

        private async Task<ParticipantRecoveryContact> SearchParticipantByEmailAndLogin(Guid campaignId, string email, string login)
        {
            var participant = await userParticipationCampaignRepository.GetParticipantByCampaignEmailAndLogin(campaignId, email, login);
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            return ParticipantRecoveryContact.OfParticipant(participant);
        }

        private async Task<ParticipantRecoveryContact> SearchAccountOperatorByEmailAndLogin(Guid campaignId, string email, string login)
        {
            var accountOperator = await accountOperatorRepository.GetAccountOperatorByCampaignEmailAndLogin(
                campaignId, email, login);
            if (accountOperator == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            var accountOperatorLogin = accountOperator.GetLoginByCampaignAndLogin(campaignId, login);
            if (accountOperatorLogin == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            return ParticipantRecoveryContact.OfAccountOperator(accountOperator, accountOperatorLogin);
        }

        #endregion

        #region Pesquisa do Participante/Operador

        public async Task<ParticipantRecoveryContact> GetParticipantContactToPasswordRecovery(Guid campaignId, PasswordRecoveryRequest passwordRecovery)
        {
            if (passwordRecovery == null)
                throw MotivaiException.ofValidation("Preencha o usuário e e-mail para continuar.");
            passwordRecovery.Validate();

            var campaignSettings = await campaignRepository.GetSettings(campaignId);
            if (campaignSettings.Parametrizations.AllowAccountOperators)
            {
                return await SearchAccountOperatorByEmailAndLogin(campaignId, passwordRecovery.Email, passwordRecovery.Login);
            }
            return await SearchParticipantByEmailAndLogin(campaignId, passwordRecovery.Email, passwordRecovery.Login);
        }

        #endregion

        #region Envio do Token

        public async Task<string> SendPasswordRecoveryToken(Guid campaignId, RecoveryTokenIssue tokenIssue)
        {
            if (tokenIssue == null)
                throw MotivaiException.ofValidation("Preencha o usuário e e-mail para continuar.");
            tokenIssue.Validate();

            ParticipantRecoveryContact contact = null;
            if (tokenIssue.IsAccountOperator())
            {
                contact = await GetAccountOperatorContact(campaignId, tokenIssue.AccountOperatorId, tokenIssue.AccountOperatorLoginId);
            }
            else
            {
                contact = await GetParticipantContact(campaignId, tokenIssue.UserId);
            }

            var verificationCode = GenerateVerificationCode();

            var tokenSent = await emailRepository.SendToken(campaignId, contact.UserId, contact.Name, contact.Email, contact.MobilePhone,
                tokenIssue.GetSendMethod(), verificationCode);
            if (!tokenSent)
            {
                throw MotivaiException.ofValidation("Não foi possível enviar o código de segurança, por favor, tente novamente.");
            }
            return Hashing.HashString(verificationCode);
        }

        private string GenerateVerificationCode()
        {
            return new Random().Next(10000, 99999).ToString();
        }

        #endregion

        #region Atualização da Senha

        public async Task<bool> UpdatePassword(Guid campaignId, RecoveryPasswordUpdate passwordUpdate)
        {
            if (passwordUpdate == null)
                throw MotivaiException.ofValidation("Preencha a nova senha e a confirmação de senha para continuar.");
            passwordUpdate.Validate();

            var campaignSettings = await campaignRepository.GetSettings(campaignId);
            if (campaignSettings.Parametrizations.AllowAccountOperators)
            {
                return await UpdateAccountOperatorPassword(campaignId, passwordUpdate);
            }
            return await UpdateParticipantPassword(campaignId, passwordUpdate);
        }

        private async Task<bool> UpdateParticipantPassword(Guid campaignId, RecoveryPasswordUpdate passwordUpdate)
        {
            var user = await userRepository.Get(passwordUpdate.UserId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");
            var participant = user.GetParticipantByCampaign(campaignId);
            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            var wasUpdate = await this.userRepository.UpdatePassword(user.Id, campaignId, passwordUpdate.ConfirmPassword);
            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.PASSWORD_UPDATE,
                    Guid.Empty, campaignId, passwordUpdate.UserId, participant.Id, Guid.Empty
                );
            }

            await NotifyPasswordReseted(campaignId, user.Id, user.Name, participant.Login, passwordUpdate.NewPassword,
                participant.GetMainEmail(), participant.GetMobilePhone());

            return wasUpdate;
        }

        private async Task<bool> UpdateAccountOperatorPassword(Guid campaignId, RecoveryPasswordUpdate passwordUpdate)
        {
            var accountOperator = await accountOperatorRepository.GetById(passwordUpdate.AccountOperatorId);
            if (accountOperator == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            var accountOperatorLogin = accountOperator.GetLoginById(passwordUpdate.AccountOperatorLoginId);
            if (accountOperatorLogin == null)
                throw MotivaiException.ofValidation("Usuário não encontrado. Verifique seu login e e-mail.");

            accountOperatorLogin.ResetPassword(passwordUpdate.NewPassword);
            var wasUpdate = await accountOperatorRepository.Update(accountOperator);
            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
                    Guid.Empty, campaignId, Guid.Empty, passwordUpdate.ParticipantId,
                    passwordUpdate.AccountOperatorId
                );
            }

            await NotifyPasswordReseted(campaignId, Guid.Empty, accountOperator.Name, accountOperatorLogin.Login,
                passwordUpdate.NewPassword, accountOperatorLogin.Email, accountOperatorLogin.MobilePhone);
            return wasUpdate;
        }

        private async Task NotifyPasswordReseted(Guid campaignId, Guid userId, string name, string login,
                string newPassword, string email, string mobilePhone)
        {
            try
            {
                await emailRepository.NotifyPasswordReseted(campaignId, userId, name, login, newPassword, email, mobilePhone);
            }
            catch (Exception ex)
            {
                LoggerFactory.GetLogger().Error("Cmp {0} - Login {1} - Email {2} - Celular {3} - Erro ao enviar nova senha para o participante: {3}",
                    campaignId, login, email, mobilePhone, ex.Message);
                await ExceptionLogger.LogException(ex, "Password Recovery", "Erro ao enviar nova senha para o participante.");
            }
        }

        public async Task<bool> ForceParticipantPasswordReset(Guid campaignId, string document, string password)
        {
            var userId = await userRepository.GetUserIdByDocument(document, campaignId);
            await userRepository.UpdatePassword(userId, campaignId, password);
            return true;
        }
        #endregion
    }
}