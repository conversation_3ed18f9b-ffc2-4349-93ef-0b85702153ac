﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Orders;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.Entities.Terms;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IApp.MyAccounts;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.ClientIntegrations;
using Motivai.Users.Domain.IRepository.ExtraServices;
using Motivai.Users.Domain.IRepository.Orders;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Models.Account.Registration;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.UserParticipant;

namespace Motivai.Users.App
{
    public class MyAccountApp : IMyAccountApp
	{
		private readonly IUserRepository _userRepository;
		private readonly IUserParticipationCampaignRepository _participationCampaignRepository;
		private readonly ICampaignTermsAcceptanceRepository campaignTermsRepository;
		private readonly ICampaignRepository _campaignRepository;
		private readonly ICryptography _cryptography;
		private readonly IOrderRepository _orderRepository;
		private readonly ICardRepository _cardRepository;
		private readonly ICashbackRepository cashbackRepository;
		private readonly ICampaignExtraServicesRepository campaignExtraServicesRepository;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;
		private readonly IUserMetadataApp userMetadata;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
        private readonly IClientIntegrations _clientIntegrations;

        public MyAccountApp(IUserRepository userRepository, ICampaignRepository campaignRepository,
			IUserParticipationCampaignRepository participationCampaignRepository, ICampaignTermsAcceptanceRepository campaignTermsRepository,
			 ICryptography cryptography, IOrderRepository orderRepository,
			ICardRepository cardRepository, ICashbackRepository cashbackRepository, IParticipantRegistrationLogRepository participantRegistrationLogRepository,
			IUserMetadataApp userMetadata, ICampaignExtraServicesRepository campaignExtraServicesRepository,
			ICampaignsGroupsApp campaignsGroupsApp, IClientIntegrations clientIntegrations)
		{
			this._campaignRepository = campaignRepository;
			this._userRepository = userRepository;
			this._participationCampaignRepository = participationCampaignRepository;
			this.campaignTermsRepository = campaignTermsRepository;
			this._orderRepository = orderRepository;
			this._cardRepository = cardRepository;
			this.cashbackRepository = cashbackRepository;
			this._cryptography = cryptography;
			this.participantRegistrationLogRepository = participantRegistrationLogRepository;
			this.userMetadata = userMetadata;
			this.campaignExtraServicesRepository = campaignExtraServicesRepository;
			this.campaignsGroupsApp = campaignsGroupsApp;
            this._clientIntegrations = clientIntegrations;
        }

		private async Task RegisterLog(ParticipantRegistrationLog participantRegistrationLog)
		{
			try
			{
				await participantRegistrationLogRepository.RegisterLog(participantRegistrationLog);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "RegistrationLog", "Erro ao registrar log de ação nos dados cadastrais");
			}
		}

		public async Task<bool> UpdatePassword(Guid userId, Guid campaignId, PasswordModel model)
		{
			if (model == null)
			{
				throw MotivaiException.ofValidation("Alteração de senha inválida");
			}
			model.Validate();

			var user = await _userRepository.GetUserAndParticipant(userId, campaignId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			if (!user.UsersParticipantCampaign.Any())
				throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

			// Validar a senha atual
			var currentPassword = new Senha(model.OldPassword, model.OldPassword, _cryptography);
			if (!participant.Password.Descricao.SequenceEqual(currentPassword.Descricao))
				throw MotivaiException.ofValidation("A senha atual está incorreta.");

			participant.Password = new Senha(model.NewPassword, model.ConfirmPassword, _cryptography);

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Alterando senha.", campaignId, userId);
			var wasUpdate = await this._userRepository.UpdatePassword(user.Id, campaignId, model.ConfirmPassword);
			if (wasUpdate)
			{
				await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.PASSWORD_UPDATE,
					Guid.Empty, campaignId, user.Id, participant.Id, Guid.Empty
				);
			}

			return wasUpdate;
		}

		public async Task<ParticipantInfo> GetParticipantInfo(Guid participantId)
		{
			if (participantId == Guid.Empty)
				throw MotivaiException.ofValidation("Participante inválido.");
			var user = await _userRepository.GetUserByParticipantId(participantId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");
			var participant = user.GetParticipantById(participantId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");
			return new ParticipantInfo()
			{
				UserId = user.Id,
				CampaignId = participant.CampaignId,
				ClientUserId = participant.ClientUserId,
				ParticipantId = participant.Id,
				Active = user.Active && participant.Active,
				Login = participant.Login,
				Name = user.Type == PersonType.Juridica ? user.CompanyName : user.Name,
				CompanyName = user.CompanyName,
				Type = user.Type,
				Rg = user.Rg,
				Cpf = user.Cpf,
				Cnpj = user.Cnpj,
				AccountRepresentative = participant.AccountRepresentative,
				StateInscriptionExempt = user.StateInscriptionExempt,
				StateInscription = user.StateInscription,
				StateInscriptionUf = user.StateInscriptionUf,
				CompanyIdentifier = participant.GetCompanyIdentifier(),
				MainEmail = participant.GetMainEmail(),
				MobilePhone = participant.GetMobilePhone()
			};
		}

		public async Task<string> GetParticipantDocument(Guid participantId)
		{
			// LoggerHelper.GetLogger().Info("{date} - Users - MyAccount App Start - {methodName}", DateTime.UtcNow, nameof(GetParticipantDocument));
			if (participantId == Guid.Empty)
				throw MotivaiException.ofValidation("Participante inválido.");
			var user = await _userRepository.GetDocumentByParticipantId(participantId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");
			// LoggerHelper.GetLogger().Info("{date} - Users - MyAccount App End - {methodName}", DateTime.UtcNow, nameof(GetParticipantDocument));
			return user.GetDocument();
		}

		public async Task<ParticipantDataModel> GetRegistrationData(Guid userId, Guid campaignId)
		{
			var user = await _userRepository.GetUserAndParticipant(userId, campaignId);

			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			if (!user.UsersParticipantCampaign.Any())
				throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

			var participant = user.GetParticipantByCampaign(campaignId);

			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

			var firstAccessSettings = await _campaignRepository.GetFirstAccessSettings(campaignId);
			if (firstAccessSettings.EnableMetadataAtFirstAccess == true)
			{
				var usersMetadataValue = await userMetadata.GetUsersMetadataValue(campaignId, userId);
				if (usersMetadataValue != null)
				{
					var participantData = ParticipantFrom(campaignId, user, participant);
					participantData.Metadata = usersMetadataValue.Metadata;
					participantData.Login = participant.Login;
					return participantData;
				};
			}

			return ParticipantFrom(campaignId, user, participant);
		}

		private ParticipantDataModel ParticipantFrom(Guid campaignId, User user, UserParticipantCampaign participant)
		{
			return new ParticipantDataModel
			{
				UserId = user.Id,
				CampaignId = campaignId,
				ParticipantID = participant.Id,
				ClientUserId = participant.ClientUserId,
				Name = user.Name,
				Type = user.Type,
				Rg = user.Rg,
				Gender = user.Gender,
				MaritalStatus = user.MaritalStatus,
				BirthDate = user.BirthDate.ToJavaScriptUTC(),
				GpInf = user.GpInf,
				GpPartnerInf = user.GpPartnerInf,
				Cpf = user.Cpf,
				Cnpj = user.Cnpj,
				Login = participant.Login,
				CompanyName = user.CompanyName,
				StateInscriptionExempt = user.StateInscriptionExempt,
				StateInscription = user.StateInscription,
				StateInscriptionUf = user.StateInscriptionUf,
				PhotoUrl = user.PhotoUrl,
				Contact = participant.Contact,
				RankingId = participant.RankingId,
				Active = participant.Active,
				Blocked = participant.Blocked,
				AccountRepresentative = participant.AccountRepresentative,
				AuthenticationMfaSettings = participant.AuthenticationMfaSettings,
				Metadata = { }
			};
		}

		public async Task<bool> UpdateRegistrationData(Guid userId, Guid campaignId, ParticipantDataModel participantData)
		{
			var user = await _userRepository.GetUserAndParticipant(userId, campaignId);

			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");
			if (!user.UsersParticipantCampaign.Any())
				throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

			var campaignSettings = await _campaignRepository.GetSettings(campaignId);

			if (campaignSettings.Parametrizations.EnableDocumentFieldAtFirstAccess)
			{
				if (string.IsNullOrEmpty(user.Cpf) && string.IsNullOrEmpty(user.Cnpj))
				{
					if (await _userRepository.ExistUserWithDocument(userId, participantData.GetDocument()))
					{
						LoggerFactory.GetLogger().Warn("Cmp {0} - Usr {1} - Doc {2} - Login {3} - CPF/CNPJ já registrado na campanha",
							campaignId, userId, participantData.GetDocument(), participant.Login);
						throw MotivaiException.ofValidation("CPF/CNPJ já registrado na campanha.");
					}
				}
			}

			user.Name = participantData.Name;

			if (user.IsFisicPerson())
			{
				if (campaignSettings.Parametrizations.EnableDocumentFieldAtFirstAccess)
				{
					user.Cpf = participantData.Cpf;
				}
				user.Rg = participantData.Rg;
				user.Gender = participantData.Gender;
				user.MaritalStatus = participantData.MaritalStatus;
				user.BirthDate = participantData.BirthDate.ToDate();

			}
			else if (user.IsJuridicPerson())
			{
				if (campaignSettings.Parametrizations.EnableDocumentFieldAtFirstAccess)
				{
					user.Cnpj = participantData.Cnpj;
				}
				user.CompanyName = participantData.CompanyName;
				participant.AccountRepresentative = participantData.AccountRepresentative;
				user.StateInscriptionExempt = participantData.StateInscriptionExempt;
				if (user.StateInscriptionExempt)
				{
					user.StateInscription = null;
					user.StateInscriptionUf = null;
				}
				else
				{
					user.StateInscription = participantData.StateInscription;
					user.StateInscriptionUf = participantData.StateInscriptionUf;
				}
			}

			user.GpInf = participantData.GpInf;
			user.GpPartnerInf = participantData.GpPartnerInf;
			user.PhotoUrl = participantData.PhotoUrl;

			if (participantData.Contact != null)
			{
				if (participant.Contact == null)
				{
					LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - RegistrationData - Cadastrando contatos - '{2} - {3} - {4} - {5}'",
						campaignId, userId, participantData.Contact.MobilePhone, participantData.Contact.MainEmail, participantData.Contact.PersonalEmail, participantData.Contact.CommercialEmail);
				}
				else
				{
					LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - RegistrationData - Atualizando contatos - De '{2} - {3} - {4} - {5}' para '{6} - {7} - {8} - {9}'",
						campaignId, userId, participant.Contact.MobilePhone, participant.Contact.MainEmail, participant.Contact.PersonalEmail, participant.Contact.CommercialEmail,
						participantData.Contact.MobilePhone, participantData.Contact.MainEmail, participantData.Contact.PersonalEmail, participantData.Contact.CommercialEmail);
				}

				if (UserParticipantValidation.CheckForContactChanges(participant.Contact, participantData.Contact))
				{
					var log = ParticipantRegistrationLog.OfContactUpdate(RegistrationAction.UPDATE_CONTACT,
						"Minha Conta", userId, campaignId, participant.Contact, participantData.Contact, participantData.LocationInfo
					);

					await this.participantRegistrationLogRepository.RegisterLog(log);
				}
				UserParticipantValidation.CopyContactFields(participant, participantData.Contact);
				participant.Contact.StartValidation();
			}

			user.Validate();

			var wasUpdate = await _userRepository.UpdateRegistrationData(user, participant);
			// Registrando a ação
			await RegisterLog(ParticipantRegistrationLog.OfParticipantDataUpdate(userId, campaignId, participantData.AccountOperator, participantData.LocationInfo));

			if (wasUpdate)
			{
				await ExecuteClientIntegration(campaignId, user);
				await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.REGISTRATION_UPDATE,
					Guid.Empty, campaignId, user.Id, participant.Id, Guid.Empty);
			}
			return wasUpdate;
		}

		public async Task<bool> UpdateContactInfo(Guid userId, Guid campaignId, UserParticipantDataModel participantData) {
			var user = await _userRepository.GetUserAndParticipant(userId, campaignId);

			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			if (!user.UsersParticipantCampaign.Any())
				throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

			var wasUpdate = await _userRepository.UpdateContactInfo(campaignId, userId, participantData);

			if (!wasUpdate) {
				throw MotivaiException.ofValidation("UPDATE_CONTACT_ERROR", "Não foi possivel atualizar as informações de contato do participante");
			}

			var log = ParticipantRegistrationLog.OfAction(RegistrationAction.UPDATE_CONTACT,
				participantData.RegistrationOriginActionLog, userId, campaignId, participantData.Contact.MainEmail, participant.Contact.MainEmail,
				participantData.Contact.MobilePhone, participant.Contact.MobilePhone, participantData.Name, user.Name );

			await this.participantRegistrationLogRepository.RegisterLog(log);

			return wasUpdate;
		}


    private async Task ExecuteClientIntegration(Guid campaignId, User user)
    {
      var integrationSettings = await _campaignRepository.GetIntegrationSettings(campaignId);
			if (integrationSettings.HasClientIntegration())
			{
				if (integrationSettings.IntegrationSettings.EnableChangeRegistrationData)
				{
                    var registrationData = Domain.Entities.PreRegisteredUser.OfUser(user, campaignId);
					try {
						LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Atualização Cadatral - Enviando para integração com cliente",
							campaignId, user.Id);
						await this._clientIntegrations.UpdateRegistrationData(campaignId, registrationData);
					} catch (Exception ex) {
						await ExceptionLogger.LogException(ex, "Atualização Cadatral - Client Integration", "Erro durante a integração com cliente");
					}
				}
			}
     }

		public async Task<ParticipantContact> GetPrincipalContact(Guid userId, Guid campaignId)
		{
			// LoggerHelper.GetLogger().Info("{date} - Users - MyAccount App Start - {methodName}", DateTime.UtcNow, nameof(GetPrincipalContact));
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			var contact = await _participationCampaignRepository.GetContactInfo(userId, campaignId);
			if (contact == null) return null;
			contact.UserId = userId;
			// LoggerHelper.GetLogger().Info("{date} - Users - MyAccount App End - {methodName}", DateTime.UtcNow, nameof(GetPrincipalContact));
			return contact;
		}

		private async Task<UserParticipantCampaign> GetParticipantAndValidateCampaign(Guid userId, Guid campaignId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Deve ser informado a campanha.");

			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Deve ser informado o usuário.");

			var participant = await _participationCampaignRepository.Get(userId, campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha.");
			return participant;
		}

		public Task<List<ResumedOrder>> GetOrdersWithProducts(Guid userId, string status, DateTime? startDate, DateTime? endDate)
		{
			return _orderRepository.GetOrdersWithProductsByUserId(userId, status, startDate, endDate);
		}

		public async Task<List<ResumedOrder>> GetOrders(Guid userId, Guid campaignId, string status, DateTime? initialDate, DateTime? finalDate)
		{
			var participant = await GetParticipantAndValidateCampaign(userId, campaignId);

			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada. Campanha: " + campaignId);

			var orders = new List<ResumedOrder>();

			var productsOrders = await _orderRepository.GetOrdersByParticipant(participant.Id, status, initialDate, finalDate);
			if (productsOrders != null && productsOrders.Count > 0)
			{
				productsOrders.ForEach(productOrder =>
				{
					productOrder.OrderType = OrderType.PRODUCT_ORDER;
					orders.Add(productOrder);
				});
			}

			// var cardsOrders = await _cardRepository.GetOrdersByParticipant(campaignId, userId, status, initialDate, finalDate);
			// if (cardsOrders != null && cardsOrders.Count > 0)
			// {
			// 	cardsOrders.ForEach(cardOrder => orders.Add(cardOrder.ToResumedOrder()));
			// }

			// var cashbackOrders = await cashbackRepository.GetOrdersByParticipant(campaignId, userId, status, initialDate, finalDate);
			// if (cashbackOrders != null && cashbackOrders.Count > 0)
			// {
			// 	cashbackOrders.ForEach(cashbackOrder => orders.Add(cashbackOrder.ToResumedOrder()));
			// }

			// var billPaymentsOrders = await campaignExtraServicesRepository.GetExtraServicesOrdersByParticipant(campaignId, userId, status, initialDate, finalDate);
			// if (billPaymentsOrders != null && billPaymentsOrders.Count > 0)
			// {
			// 	billPaymentsOrders.ForEach(billPaymentOrder => orders.Add(billPaymentOrder.ToResumedOrder()));
			// }

			return orders;
		}

		public async Task<bool> RegisterRegulationAcceptance(Guid userId, Guid campaignId, string regulationId, string version)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (string.IsNullOrEmpty(regulationId))
				throw MotivaiException.ofValidation("Regulamento inválido.");

			if (!await _participationCampaignRepository.IsUserActive(userId, campaignId))
			{
				throw MotivaiException.ofValidation("Participante não está ativo na campanha.");
			}

			await campaignTermsRepository.RegisterAcceptance(TermsAcceptance.ofRegulation(userId, campaignId, regulationId, version));
			return true;
		}

		public async Task<bool> UpdateParent(Guid userId, Guid campaignId, UserBasicInfo parent)
		{
			Validate(userId, campaignId);
			if (parent == null || !parent.UserId.HasValue || parent.UserId == Guid.Empty)
				throw MotivaiException.ofValidation("Informe o participante pai.");
			if (userId == parent.UserId)
			{
				throw MotivaiException.ofValidation("Não é permitido referenciar o próprio participante.");
			}
			var parentDb = await _userRepository.GetUserBasicInfo(campaignId, parent.UserId.Value);
			if (parentDb == null)
			{
				throw MotivaiException.ofValidation("Participant pai não foi encontrado.");
			}
			return await this._participationCampaignRepository.UpdateParticipantParent(userId, campaignId, parentDb);
		}

		public Task<bool> BlockParticipant(Guid userId, Guid campaignId, UserBlockingRequest blockingRequest)
		{
			Validate(userId, campaignId);

			return this._userRepository.BlockParticipant(userId, campaignId, blockingRequest.ToBlockingDetails(),
					blockingRequest.Origin);
		}

		private static void Validate(Guid userId, Guid campaignId)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("Usuário inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
		}

		public async Task<bool> HasAcceptedAnyTerm(Guid userId, Guid campaignId) {
			Validate(userId, campaignId);
			return await this.campaignTermsRepository.HasAcceptedAnyTerm(userId, campaignId);
		}
	}
}