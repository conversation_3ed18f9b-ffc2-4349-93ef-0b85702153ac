using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Model.Structures;
using Motivai.SharedKernel.Domain.Repository;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Roles;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Roles;
using Motivai.Users.Domain.IRepository.UsersAdministrations;
using Microsoft.AspNetCore.Http;

namespace Motivai.Users.App
{
    public class RoleApp : IRoleApp
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IHttpContextAccessor _context;
        private readonly IBusinessUnitCommomRepository _buRepository;
        private readonly IEmailRepository _emailRepository;
        private readonly IUserAdministrationRepository _userRepository;

        public RoleApp(IRoleRepository roleRepository, IHttpContextAccessor context, IBusinessUnitCommomRepository buRepository, IEmailRepository emailRepository, IUserAdministrationRepository userRepository)
        {
            this._roleRepository = roleRepository;
            this._context = context;
            this._buRepository = buRepository;
            this._emailRepository = emailRepository;
            this._userRepository = userRepository;
        }

        private Guid BuId()
        {
            if (!string.IsNullOrEmpty(this._context.HttpContext.Request.Headers["x-bu"]))
            {
                return Guid.Parse(this._context.HttpContext.Request.Headers["x-bu"]);
            }
            return Guid.Empty;
        }

        private async Task<List<Guid>> GetChildrenBus()
        {
            if (BuId() == Guid.Empty)
                return null;

            var bus = await this._buRepository.GetChildrens(BuId());
            return bus;
        }

        public async Task<List<Role>> GetRoles(string name, int? skip, int? limit)
        {
            return await _roleRepository.GetRoles(await GetChildrenBus(), name, skip, limit);
        }

        public async Task<Role> GetRole(Guid roleId)
        {
            if (roleId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do perfil inválido.");
            return await _roleRepository.Get(await GetChildrenBus(), roleId);
        }

        public async Task<bool> Save(Role role)
        {
            if (role == null)
                throw MotivaiException.ofValidation("Perfil inválido.");

            role.Validate();
            role.BuId = role.BuId == Guid.Empty ? BuId() : role.BuId;
            if (role.GenerateToken && string.IsNullOrEmpty(role.Token))
            {
                role.Token = AlphanumericGenerator.GenerateId16();
            }

            await _roleRepository.Save(role);
            var metadata = new List<Entry<string, string>>() {
                Entry.Of("Nome", role.Name),
                Entry.Of("Ativo", role.Master ? "Sim" : "Não"),
                Entry.Of("Gerar Token", role.GenerateToken ? "Sim" : "Não")
            };

            await SendPlataformActionNotification("Atualização de Perfil", role, metadata);
            return true;
        }

        public async Task<bool> Delete(Guid roleId)
        {
            if (roleId == Guid.Empty)
                throw MotivaiException.ofValidation("ID do perfil inválido.");
            return await _roleRepository.Delete(roleId);
        }

        public async Task<Role> GetRoleByToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                throw MotivaiException.ofValidation("Token deve ser informado para buscar");

            return await this._roleRepository.GetRoleByToken(token);
        }

        private async Task<bool> SendPlataformActionNotification(string action, Role role, List<Entry<string, string>> metadata)
        {
            if (role.OperationUser != null)
            {
                var userAdmin = await this._userRepository.Get(role.OperationUser.UserId);
                if (userAdmin != null)
                {
                    role.OperationUser.Email = userAdmin.Email;
                    role.OperationUser.Login = userAdmin.Login;
                }
            }

            var notification = new
            {
                action = action,
                createDate = DateTime.Now,
                operationDate = role.OperationUser.OperationDate,
                origin = "Admin",
                operationUser = role.OperationUser,
                emailToNotify = "<EMAIL>",
                metadata = metadata,
                locationInfo = role.LocationInfo
            };

            try
            {
                return await this._emailRepository.SendPlataformActionNotification(notification);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Admin - Roles", "Erro ao enviar notificação de segurança");
                return false;
            }
        }
    }
}