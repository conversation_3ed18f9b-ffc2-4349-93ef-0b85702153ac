using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.Users.Domain.Entities.AccountOperators.Actions;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IApp.AccountOperators;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Credify;
using Motivai.Users.Domain.IRepository.Email;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.Models.AccountOperators;
using Motivai.Users.Domain.Models.MyAccount;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.App.AccountOperators {
    public class AccountOperatorManager : IAccountOperatorManager {
        private readonly IUsersAccountsOperatorsActionsHistoryApp usersAccountsOperatorsActionsHistoryApp;
        private readonly IAccountOperatorRepository accountOperatorRepository;
        private readonly IUserRepository userRepository;
        private readonly ICredifyRepository credifyRepository;
        private readonly IEmailRepository emailRepository;
        private readonly ICampaignsGroupsApp campaignsGroupsApp;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;


        public AccountOperatorManager(IUsersAccountsOperatorsActionsHistoryApp usersAccountsOperatorsActionsHistoryApp,
            IAccountOperatorRepository accountOperatorRepository,
            IUserRepository userRepository, ICredifyRepository credifyRepository,
            IEmailRepository emailRepository, ICampaignsGroupsApp campaignsGroupsApp, IParticipantRegistrationLogRepository participantRegistrationLogRepository) {
            this.usersAccountsOperatorsActionsHistoryApp = usersAccountsOperatorsActionsHistoryApp;
            this.accountOperatorRepository = accountOperatorRepository;
            this.userRepository = userRepository;
            this.credifyRepository = credifyRepository;
            this.emailRepository = emailRepository;
            this.campaignsGroupsApp = campaignsGroupsApp;
            this.participantRegistrationLogRepository = participantRegistrationLogRepository;
        }

        public async Task<List<ResumedAccountOperator>> GetAccountOperators(Guid userId, Guid campaignId) {
            ValidateUserAndCampaign(userId, campaignId);

            var operators = await accountOperatorRepository.GetAccountOperators(userId, campaignId);
            if (operators.IsNullOrEmpty()) {
                return null;
            }
            var accountOperators = new List<ResumedAccountOperator>(operators.Count);
            foreach (var operat in operators) {
                var login = operat.Logins.FirstOrDefault(l => l.CampaignId == campaignId && l.AccessibleAccounts.Any(a => a.UserId == userId));
                accountOperators.Add(new ResumedAccountOperator {
                    AccountOperatorId = operat.Id,
                        AccountOperatorLoginId = login.Id,
                        Blocked = login.GetAccessibleAccountByUserId(userId).Blocked,
                        Document = operat.Document,
                        Name = operat.Name,
                        Email = login.Email,
                        Login = login.Login,
                        MobilePhone = login.MobilePhone,
                        PersonType = operat.PersonType,
                        IsMigrated = login.AuthenticationAccess != null ? login.AuthenticationAccess.Migrated : false,
                        MigrationDate = login.AuthenticationAccess != null ? login.AuthenticationAccess.MigrationDate : DateTime.UtcNow,
                        Role = login.GetAccessibleAccountByUserId(userId).Role
                });
            }
            return accountOperators;
        }

        public async Task<ResumedAccountOperator> QueryAccountOperatorByDocument(Guid userId, Guid campaignId, string document) {
            if (string.IsNullOrEmpty(document)) {
                throw MotivaiException.ofValidation("CPF_CNPJ_REQUIRED", "Informe o CPF para pesquisa");
            }
            ValidateUserAndCampaign(userId, campaignId);

            var personType = PersonTypeHelper.FromDocument(document);

            var foundOperator = await accountOperatorRepository.FindAccountOperatorByDocumentAndCampaign(document, campaignId);
            if (foundOperator != null) {
                var login = foundOperator.Logins.FirstOrDefault(l => l.CampaignId == campaignId);
                var accessibleAccount = login.GetAccessibleAccountByUserId(userId);
                return new ResumedAccountOperator {
                    AccountOperatorId = foundOperator.Id,
                        AccountOperatorLoginId = login.Id,
                        Blocked = accessibleAccount == null ? false : accessibleAccount.Blocked,
                        Document = foundOperator.Document,
                        Name = foundOperator.Name,
                        Email = login.Email,
                        Login = login.Login,
                        MobilePhone = login.MobilePhone,
                        PersonType = foundOperator.PersonType,
                        IsMigrated = foundOperator.IsMigatedOperatorByCampaign(campaignId),
                        MigrationDate = login.AuthenticationAccess != null ? login.AuthenticationAccess.MigrationDate : DateTime.UtcNow
                };
            }

            var person = await credifyRepository.QueryDocument(personType, document);
            if (person == null || string.IsNullOrEmpty(person.Name))
                return null;

            return new ResumedAccountOperator {
                Name = person.Name,
                    PersonType = personType
            };
        }

        public async Task<List<UserCampaignsInfo>> GetOperatorAccessibleAccounts(Guid accountOperatorId, Guid accountOperatorLoginId) {
            if (accountOperatorId == Guid.Empty || accountOperatorLoginId == Guid.Empty) {
                throw MotivaiException.ofValidation("Operador inválido.");
            }
            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var operatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (operatorLogin == null) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                return null;
            }
            var usersIds = operatorLogin.AccessibleAccounts
                .Where(a => a.Active && !a.Blocked)
                .Select(a => a.UserId)
                .ToList();
            if (usersIds.IsNullOrEmpty()) {
                return null;
            }

            var usersInfo = await userRepository.GetUsersInfoByIds(operatorLogin.CampaignId, usersIds);
            if (usersInfo != null) {
                usersInfo.ForEach(i => i.ValidateName());
            }

            return usersInfo;
        }

        public async Task<bool> ResetAccountOperatorPassword(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId,
            OperatorUser operatorUser) {
            ValidateUserAndCampaign(userId, campaignId);

            // Quando é site campanha valida se o user é um master
            if (operatorUser != null) {
                operatorUser.OperationDate = DateTime.UtcNow;
                if (operatorUser.OperationChannelOrigin == OperationOrigin.CAMPAIGN_SITE) {
                    await VerifyIfIsMaster(userId, campaignId, operatorUser);
                }
            }

            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var operatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var generatedPassword = AlphanumericGenerator.GenerateId16();
            operatorLogin.ResetPassword(generatedPassword);

            if (operatorUser != null) {
                LoggerFactory.GetLogger().Warn("AccountOperatorManager - {0} - Usr [{1} - {2}] - Resetando senha do [{3} - {4}]",
                    operatorUser.OperationChannelOrigin, operatorUser.UserId, operatorUser.UserName, accountOperator.Document, operatorLogin.Email);
            } else {
                LoggerFactory.GetLogger().Warn("AccountOperatorManager - Sem Origem - Usr [Nao Informado] - Resetando senha do [{0} - {1}]",
                    accountOperator.Document, operatorLogin.Email);
            }

            var wasUpdate = await accountOperatorRepository.Update(accountOperator);
            if (!wasUpdate)
                throw MotivaiException.ofValidation("Não foi possível resetar a senha.");

            await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
                Guid.Empty, campaignId, Guid.Empty, Guid.Empty, accountOperatorId
            );

            var emailSent = await emailRepository.SendResetPasswordEmail(campaignId, userId, accountOperator.Name, operatorLogin.Email, null,
                operatorLogin.Login, generatedPassword);
            if (!emailSent) {
                throw MotivaiException.ofValidation("Senha resetada com sucesso, porém não foi possível enviar e-mail para o operador com a nova senha.");
            }
            return wasUpdate;
        }

        public async Task<bool> BlockAccountOperator(Guid userId, Guid campaignId, AccountOperatorAccessChange operatorBlocking) {
            ValidateUserAndCampaign(userId, campaignId);
            if (operatorBlocking == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            operatorBlocking.Validate();

            // Quando é site campanha valida se o user é um master
            if (operatorBlocking.OperationUserBlocking != null) {
                operatorBlocking.OperationUserBlocking.OperationDate = DateTime.UtcNow;
                if (operatorBlocking.OperationUserBlocking.OperationChannelOrigin == OperationOrigin.CAMPAIGN_SITE) {
                    await VerifyIfIsMaster(userId, campaignId, operatorBlocking.OperationUserBlocking);
                }
            }

            var accountOperator = await accountOperatorRepository.GetById(operatorBlocking.AccountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var operatorLogin = accountOperator.GetLoginById(operatorBlocking.AccountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var account = operatorLogin.GetAccessibleAccountByUserId(userId);
            if (account == null) {
                throw MotivaiException.ofValidation("Operador não tem acesso a essa conta.");
            }
            account.Block(operatorBlocking.OperationUserBlocking, operatorBlocking.Reason);

            var wasUpdate = await accountOperatorRepository.Update(accountOperator);
            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
                    Guid.Empty, campaignId, Guid.Empty, Guid.Empty, accountOperator.Id
                );
                var additionalInformation = new Dictionary<string, string>()
                {
                    {   "Motivo", operatorBlocking.Reason },
                    {   "CPF Operador", accountOperator.Document },
                    {   "E-mail", operatorLogin.Email },
                };
                OperationUser operationUsr = new OperationUser();
                operationUsr.Email = operatorBlocking.OperationUserBlocking.Email;
                operationUsr.Name = operatorBlocking.OperationUserBlocking.UserName;
                operationUsr.UserId = operatorBlocking.OperationUserBlocking.UserId;
                operationUsr.OperationDate = operatorBlocking.OperationUserBlocking.OperationDate;

                var log = ParticipantRegistrationLog.OfAction(RegistrationAction.ACCOUNT_OPERATOR_ACCESS_BLOCKED,
					userId, campaignId, operationUsr, null, additionalInformation);
				try {
					await this.participantRegistrationLogRepository.RegisterLog(log);
				} catch (Exception ex) {
					await ExceptionLogger.LogException(ex, "User - Wallet Reset", "Erro durante registro de log do reset de wallet");
				}
            }
            return wasUpdate;
        }

        public async Task<bool> ActiveAccountOperator(Guid userId, Guid campaignId, AccountOperatorAccessChange operatorActive)
        {
            ValidateUserAndCampaign(userId, campaignId);
            if (operatorActive == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            operatorActive.Validate();

            if (operatorActive.OperationUserBlocking != null) {
                operatorActive.OperationUserBlocking.OperationDate = DateTime.UtcNow;
                if (operatorActive.OperationUserBlocking.OperationChannelOrigin == OperationOrigin.CAMPAIGN_SITE) {
                    await VerifyIfIsMaster(userId, campaignId, operatorActive.OperationUserBlocking);
                }
            }

            var accountOperator = await accountOperatorRepository.GetById(operatorActive.AccountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var operatorLogin = accountOperator.GetLoginById(operatorActive.AccountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var account = operatorLogin.GetAccessibleAccountByUserId(userId);
            if (account == null) {
                throw MotivaiException.ofValidation("Operador não tem acesso a essa conta.");
            }

            account.ActiveOperator(operatorActive.OperationUserBlocking, operatorActive.Reason);

            var wasUpdate = await accountOperatorRepository.Update(accountOperator);
            if (wasUpdate)
            {
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
                    Guid.Empty, campaignId, Guid.Empty, Guid.Empty, accountOperator.Id
                );
                var additionalInformation = new Dictionary<string, string>()
                {
                    {   "Motivo", operatorActive.Reason },
                    {   "CPF Operador", accountOperator.Document },
                    {   "E-mail", operatorLogin.Email },
                };
                OperationUser operationUsr = new OperationUser();
                operationUsr.Email = operatorActive.OperationUserBlocking.Email;
                operationUsr.Name = operatorActive.OperationUserBlocking.UserName;
                operationUsr.UserId = operatorActive.OperationUserBlocking.UserId;
                operationUsr.OperationDate = operatorActive.OperationUserBlocking.OperationDate;

                var log = ParticipantRegistrationLog.OfAction(RegistrationAction.ACCOUNT_OPERATOR_ACCESS_UNBLOCKED,
					userId, campaignId, operationUsr, null, additionalInformation);
				try {
					await this.participantRegistrationLogRepository.RegisterLog(log);
				} catch (Exception ex) {
					await ExceptionLogger.LogException(ex, "User - Wallet Reset", "Erro durante registro de log do reset de wallet");
				}
            }

            return wasUpdate;
        }

        private async Task VerifyIfIsMaster(Guid userId, Guid campaignId, OperatorUser operatorUser) {
            var accountOperator = await accountOperatorRepository.GetById(operatorUser.AccountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var operatorLogin = accountOperator.GetLoginById(operatorUser.AccountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var account = operatorLogin.GetAccessibleAccountByUserId(userId);
            if (account == null) {
                throw MotivaiException.ofValidation("Operador não tem acesso a essa conta.");
            }
            if (account.Role != OperatorRole.MASTER) {
                throw MotivaiException.ofValidation("Você não tem permissão para realizar está operação.");
            }
        }

        public async Task<Guid> CreateOrUpdateAccountOperator(Guid userId, Guid campaignId, ResumedAccountOperator newOperator) {
            ValidateUserAndCampaign(userId, campaignId);
            if (newOperator == null)
                throw MotivaiException.ofValidation("OPERATOR_INVALID", "Preencha os campos corretamente para prosseguir.");
            newOperator.Validate();

            var existingOperatorInCampaign = await accountOperatorRepository.VerifyDifferentOperatorByEmail(campaignId, newOperator.Document, newOperator.Email);
            if (existingOperatorInCampaign) {
                throw MotivaiException.ofValidation("OPERATOR_EMAIL_IN_USE", "Já existe um operador cadastrado com mesmo e-mail.");
            }

            var existingOperator = await accountOperatorRepository.FindAccountOperatorByDocument(newOperator.Document);

            Guid accountOperatorId;
            if (existingOperator == null) {
                accountOperatorId = await CreateNewOperator(userId, campaignId, newOperator);
            } else {
                accountOperatorId = await UpdateAccountOperator(userId, campaignId, existingOperator, newOperator);
            }

            if (accountOperatorId != Guid.Empty)
            {
                await this.campaignsGroupsApp.UpdateAccountOperatorCampaignsInCoalitionIfNeeded(
                    EventType.ACCOUNT_OPERATOR_UPDATE, Guid.Empty, campaignId, accountOperatorId, newOperator.CreationOrigin
                );
            }
            return accountOperatorId;
        }

        public async Task<bool> CreateAccountOperator(Guid userId, Guid campaignId, ResumedAccountOperator accountOperator) {
            var accountOperatorId = await CreateOrUpdateAccountOperator(userId, campaignId, accountOperator);
            return accountOperatorId != Guid.Empty;
        }

        public async Task<bool> UpdateAccountOperatorPassword(Guid accountOperatorId, Guid accountOperatorLoginId, PasswordModel passwordUpdate) {
            if (passwordUpdate == null) {
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            }
            passwordUpdate.Validate();

            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            var operatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }
            operatorLogin.ValidatePassword(passwordUpdate.OldPassword);

            operatorLogin.ResetPassword(passwordUpdate.ConfirmPassword);

            var wasUpdate = await accountOperatorRepository.Update(accountOperator);
            if (wasUpdate)
            {
               	await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
					Guid.Empty, operatorLogin.CampaignId, Guid.Empty, Guid.Empty, accountOperatorId
				);
            }
            return wasUpdate;
        }

        private static void ValidateUserAndCampaign(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
        }

        private async Task<Guid> CreateNewOperator(Guid userId, Guid campaignId, ResumedAccountOperator newOperator) {
            LoggerFactory.GetLogger().Info("AccountOperator - Usr {0} - Cmp {1} - CPF {2} - Email {3} - Criando",
                userId, campaignId, newOperator.Document, newOperator.Email);

            var accountOperator = new AccountOperator() {
                Active = true,
                CreateDate = DateTime.UtcNow,
                Name = newOperator.Name,
                PersonType = newOperator.PersonType,
                Document = newOperator.Document,
                OperationUserCreation = newOperator.OperationUserCreation
            };
            accountOperator.StartValidation();
            var generatedPassword = AlphanumericGenerator.GenerateId16();
            var accountOperatorLogin = CreateOperatorLogin(userId, campaignId, newOperator, generatedPassword);
            accountOperator.AddLogin(accountOperatorLogin);
            await accountOperatorRepository.Create(accountOperator);
            if (accountOperatorLogin.Active) {
                await NotifyNewLogin(userId, campaignId, newOperator.Document, newOperator.Name, newOperator.Email, newOperator.Login, generatedPassword);
            }
            return accountOperator.Id;
        }

        private async Task<Guid> UpdateAccountOperator(Guid userId, Guid campaignId, AccountOperator existingOperator, ResumedAccountOperator newOperator) {
            // TODO: identificador aqui é o Login (verificar se será usando Email também)
            var campaignLogin = existingOperator.Logins.FirstOrDefault(l => l.CampaignId == campaignId && l.Login == newOperator.Login);
            if (campaignLogin == null) {
                var generatedPassword = AlphanumericGenerator.GenerateId16();
                var accountOperatorLogin = CreateOperatorLogin(userId, campaignId, newOperator, generatedPassword);
                existingOperator.AddLogin(accountOperatorLogin);
                if (accountOperatorLogin.Active) {
                    await NotifyNewLogin(userId, campaignId, newOperator.Document, newOperator.Name, newOperator.Email, newOperator.Login, generatedPassword);
                }
            } else {
                campaignLogin.AddAccessibleAccount(newOperator.OperationUserCreation, userId, newOperator.AccountDocument, newOperator.GetRole());
            }
            var wasUpdated = await accountOperatorRepository.Update(existingOperator);
            if (!wasUpdated)
                throw MotivaiException.ofValidation("Não foi possível atualizar o operador.");
            return existingOperator.Id;
        }

        private static AccountOperatorLogin CreateOperatorLogin(Guid userId, Guid campaignId, ResumedAccountOperator newOperator, string generatedPassword) {
            var accountOperatorLogin = AccountOperatorLogin.Create(userId, campaignId, newOperator.AccountDocument,
                newOperator.Email, newOperator.MobilePhone, newOperator.AcceptReceiveCommunications, newOperator.Login, generatedPassword,
                newOperator.CreationOrigin, newOperator.GetRole(), newOperator.OperationUserCreation);
            if (newOperator.Active.HasValue && newOperator.Active == false) {
                accountOperatorLogin.Active = false;
            }
            return accountOperatorLogin;
        }

        public async Task<ResumedAccountOperator> GetOperatorBy(Guid userId, Guid campaignId, string document) {
            if (string.IsNullOrEmpty(document)) {
                return null;
            }
            var accountOperator = await accountOperatorRepository.FindAccountOperatorBy(document, campaignId, userId);
            if (accountOperator == null) {
                return null;
            }
            var accountOperatorLogin = accountOperator.GetLoginByCampaignAndUser(campaignId, userId);
            if (accountOperatorLogin == null) {
                return null;
            }
            return ResumedAccountOperator.FromOperator(accountOperator, accountOperatorLogin);
        }

        private async Task NotifyNewLogin(Guid userId, Guid campaignId, string document, string name, string email, string login, string generatedPassword) {
            LoggerFactory.GetLogger().Info("AccountOperator - Usr {0} - Cmp {1} - CPF {2} - Email {3} - Enviando acesso p/ operador",
                userId, campaignId, document, email);
            try {
                var sent = await emailRepository.SendNewParticipantNotification(campaignId, userId, name, email, null, login, generatedPassword);
                if (!sent) {
                    LoggerFactory.GetLogger().Error("AccountOperator - Usr {0} - Cmp {1} - CPF {2} - Email {3} - E-mail de acesso não enviado",
                        userId, campaignId, document, email);
                }
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "AccountOperator", "Erro ao enviar e-mail com acessos p/ operador");
                LoggerFactory.GetLogger().Error("AccountOperator - Usr {0} - Cmp {1} - CPF {2} - Email {3} - Erro no email: {4}",
                    userId, campaignId, document, email, ex.Message);
            }
        }

        public async Task<OperatorRole?> GetAccountOperatorRoleInAccount(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId) {
            ValidateUserAndCampaign(userId, campaignId);

            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var operatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var account = operatorLogin.GetAccessibleAccountByUserId(userId);
            if (account == null) {
                throw MotivaiException.ofValidation("Operador não tem acesso a essa conta.");
            }
            return account.Role;
        }

        public async Task<bool> UpdateAccountOperatorEmail(Guid userId, Guid campaignId, Guid accountOperatorId, Guid accountOperatorLoginId, UpdateDataAccountOperator updateDataAccountOperator ) {
            if (string.IsNullOrEmpty(updateDataAccountOperator.FieldValue)) {
                throw MotivaiException.ofValidation("Email do operador inválido.");
            }

            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var operatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            if (operatorLogin.CampaignId != campaignId) {
                throw MotivaiException.ofValidation("Operador não pertence a essa campanha.");
            }

            var existingOperatorInCampaign = await accountOperatorRepository.VerifyDifferentOperatorByEmail(campaignId, accountOperator.Document, updateDataAccountOperator.FieldValue);
            if (existingOperatorInCampaign) {
                throw MotivaiException.ofValidation("OPERATOR_EMAIL_IN_USE", "Já existe um operador cadastrado com mesmo e-mail.");
            }

            var wasUpdate = await accountOperatorRepository.UpdateAccountOperatorLoginEmail(accountOperatorId, accountOperatorLoginId, updateDataAccountOperator);
            if (wasUpdate)
            {
                updateDataAccountOperator.FromValue = operatorLogin.Email;
                updateDataAccountOperator.ToValue = updateDataAccountOperator.FieldValue;

                await this.usersAccountsOperatorsActionsHistoryApp.Save(updateDataAccountOperator.ToEntity());

                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
                    Guid.Empty, campaignId, Guid.Empty, Guid.Empty, accountOperatorId
                );
            }
            return wasUpdate;
        }

        public async Task<bool> UpdateAccountOperatorRole(Guid userId, Guid accountOperatorId, Guid accountOperatorLoginId, UpdateDataAccountOperator updateDataAccountOperator) {
            if (string.IsNullOrEmpty(updateDataAccountOperator.FieldValue)) {
                throw MotivaiException.ofValidation("Perfil do operador inválido.");
            }

            var accountOperator = await accountOperatorRepository.GetById(accountOperatorId);
            if (accountOperator == null || accountOperator.Logins.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var operatorLogin = accountOperator.GetLoginById(accountOperatorLoginId);
            if (operatorLogin.AccessibleAccounts.IsNullOrEmpty()) {
                throw MotivaiException.ofValidation("Operador não encontrado.");
            }

            var accessibleAccount = operatorLogin.GetAccessibleAccountByUserId(userId);

            if (accessibleAccount == null) {
                throw MotivaiException.ofValidation("Dados da conta do operador não foram encontrados.");
            }

            OperatorRole operatorRole = EnumHelper<OperatorRole>.Parse(updateDataAccountOperator.FieldValue);

            var wasUpdate = await accountOperatorRepository.UpdateAccountOperatorLoginRole(userId, accountOperatorId, accountOperatorLoginId, operatorRole);
            if (wasUpdate)
            {
                updateDataAccountOperator.FromValue = accessibleAccount.Role.ToString();
                updateDataAccountOperator.ToValue = operatorRole.ToString();

                await this.usersAccountsOperatorsActionsHistoryApp.Save(updateDataAccountOperator.ToEntity());

                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.ACCOUNT_OPERATOR_UPDATE,
                    Guid.Empty, operatorLogin.CampaignId, Guid.Empty, Guid.Empty, accountOperatorId
                );
            }
            return wasUpdate;
        }

        public async Task<List<OperatorAcessibleAccounts>> GetOperatorAcessibleAccounts(string document, Guid campaignId)
        {
            Validate(document, campaignId);

            document = Extractor.RemoveMasks(document);
            if (!Cpf.IsCpf(document))
                throw MotivaiException.ofValidation("CPF inválido.");

            var accountsOperator = await this.accountOperatorRepository.FindAcessibleAccountsBy(document, campaignId);
            if (accountsOperator == null)
                throw MotivaiException.ofValidation("usuário não encontrado.");

            return accountsOperator;
        }

        private void Validate(string document, Guid campaignId)
        {
            if (String.IsNullOrEmpty(document))
                throw MotivaiException.ofValidation("CPF é obrigatório.");

            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
        }
    }
}
