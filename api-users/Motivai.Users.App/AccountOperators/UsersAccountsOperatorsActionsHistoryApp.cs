using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.AccountOperators.Actions;
using Motivai.Users.Domain.IApp.AccountOperators;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;

namespace Motivai.Users.App.AccountOperators {
    public class UsersAccountsOperatorsActionsHistoryApp : IUsersAccountsOperatorsActionsHistoryApp {

        private IUsersAccountsOperatorsActionsHistoryRepository accountsOperatorsActionsHistoryRepository;

        public UsersAccountsOperatorsActionsHistoryApp(IUsersAccountsOperatorsActionsHistoryRepository accountsOperatorsActionsHistoryRepository) {
            this.accountsOperatorsActionsHistoryRepository = accountsOperatorsActionsHistoryRepository;
        }

        public async Task<bool> Save(UsersAccountsOperatorsDataActionsHistory usersAccountsOperatorsDataActionsHistory) {
            await this.accountsOperatorsActionsHistoryRepository.Save(usersAccountsOperatorsDataActionsHistory);

            return true;
        }
    }
}