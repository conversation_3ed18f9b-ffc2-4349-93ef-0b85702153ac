using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Searches;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.Searches;

namespace Motivai.Users.App.Searches
{
	public class ParticipantSearchApp : IParticipantSearchApp
	{
		private readonly IUserRepository userRepository;
		private readonly IParticipantSearchRepository participantSearchRepository;

		public ParticipantSearchApp(IUserRepository userRepository, IParticipantSearchRepository participantSearchRepository)
		{
			this.userRepository = userRepository;
			this.participantSearchRepository = participantSearchRepository;
		}

		public async Task<List<ParticipantInfo>> SearchParticipantsIncampaign(Guid campaignId, ParticipantSearchField searchByField, string searchValue)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (string.IsNullOrEmpty(searchValue))
				throw MotivaiException.ofValidation("Informe o filtro de pesquisa.");

			switch (searchByField)
			{
				case ParticipantSearchField.DOCUMENT:
					return await participantSearchRepository.FindParticipantsByDocument(campaignId, searchValue);
				case ParticipantSearchField.LOGIN:
					return await participantSearchRepository.FindParticipantByLogin(campaignId, searchValue);
				case ParticipantSearchField.CLIENT_USER_ID:
					return await participantSearchRepository.FindParticipantByClientUserId(campaignId, searchValue);
				default:
					throw MotivaiException.ofValidation("Opção de pesquisa inválida.");
			}
		}

		public Task<User> FindParticipantByDocument(string document)
		{
			return FindParticipantByDocument(document, null);
		}

		///<summary>
		/// Busca o participante pelo CPF/CNPJ, que tenta evitar casos duplicados.
		/// Se for informado o ID da campanha, será utilizado a busca pelo CPF/CNPJ na campanha (evitar casos duplicados).
		/// Se não for informado o ID da campanha, será utilizado a busca pelo CPF/CNPJ.
		///</summary>
		public async Task<User> FindParticipantByDocument(string document, Guid? campaignId)
		{
			var personType = PersonTypeHelper.FromDocument(document);

			if (campaignId.HasValue && campaignId != Guid.Empty)
			{
				// LoggerFactory.GetLogger().Info("Cmp {} - Doc {} - Buscando participante por documento e campanha.", campaignId, document);
				var user = await userRepository.GetUserByDocumentAndCampaign(PersonType.Fisica, document, campaignId.Value);
				if (user != null)
					return user;
			}

			// LoggerFactory.GetLogger().Info("Cmp {} - Doc {} - Buscando participante por documento.", campaignId, document);
			switch (personType)
			{
				case PersonType.Fisica:
					return await userRepository.GetByCpf(document);
				case PersonType.Juridica:
					return await userRepository.GetByCnpj(document);
				default:
					throw MotivaiException.of("CPF_CNPJ_INVALID", "CPF/CNPJ inválido.");
			}
		}
	}
}