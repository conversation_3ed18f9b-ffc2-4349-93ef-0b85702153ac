﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.Campaigns.Processes.ConsultPerson;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Credify;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.Models.ParticipantCampaign;

namespace Motivai.Users.App
{
	public class UserApp : IUserApp
	{
		private readonly IUserRepository _userRepository;
		private readonly ICredifyRepository credifyRepository;
		private readonly ICampaignRepository campaignRepository;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;
		private readonly IAccountOperatorRepository accountOperatorRepository;

		public UserApp(IUserRepository userRepository, ICredifyRepository credifyRepository,
				ICampaignRepository campaignRepository, IParticipantRegistrationLogRepository participantRegistrationLogRepository,
				IAccountOperatorRepository accountOperatorRepository)
		{
			_userRepository = userRepository;
			this.credifyRepository = credifyRepository;
			this.campaignRepository = campaignRepository;
			this.participantRegistrationLogRepository = participantRegistrationLogRepository;
			this.accountOperatorRepository = accountOperatorRepository;
		}

		private static ParticipantInfo CreateParticipantInfo(User user, UserParticipantCampaign participant)
		{
			return new ParticipantInfo()
			{
				UserId = user.Id,
				CampaignId = participant.CampaignId,
				ClientUserId = participant.ClientUserId,
				ParticipantId = participant.Id,
				Active = user.Active && participant.Active,
				Login = participant.Login,
				Name = user.Type == PersonType.Juridica && string.IsNullOrEmpty(user.Name) ? user.CompanyName : user.Name,
				BirthDate = user.BirthDate,
				CompanyName = user.CompanyName,
				Type = user.Type,
				Rg = user.Rg,
				Cpf = user.Cpf,
				Cnpj = user.Cnpj,
				AccountRepresentative = participant.AccountRepresentative,
				StateInscriptionExempt = user.StateInscriptionExempt,
				StateInscription = user.StateInscription,
				StateInscriptionUf = user.StateInscriptionUf,
				CompanyIdentifier = participant.GetCompanyIdentifier(),
				MainEmail = participant.GetMainEmail(),
				MobilePhone = participant.GetMobilePhone()
			};
		}

		public async Task<ParticipantInfo> GetUserInfoByDocument(string document)
		{
			User user = null;
			var personType = PersonTypeHelper.FromDocument(document);
			// Pesquisa pelo filtro informado
			if (personType == PersonType.Fisica)
			{
				user = await _userRepository.GetByCpf(document);
			}
			else if (personType == PersonType.Juridica)
			{
				user = await _userRepository.GetByCnpj(document);
			}

			if (user == null)
				return null;
			return new ParticipantInfo()
			{
				UserId = user.Id,
				Name = user.Name,
				Type = user.Type,
				Cpf = user.Cpf,
				Cnpj = user.Cnpj,
				Rg = user.Rg,
				CompanyName = user.CompanyName,
				Active = user.Active
			};
		}

		public async Task<User> GetUserByCpf(string cpf)
		{
			cpf.ForNullOrEmpty("Deve ser informado o CPF do Usuário.");

			if (!Cpf.IsCpf(cpf))
				throw MotivaiException.ofValidation("CPF informado está inválido.");

			return await _userRepository.GetByCpf(cpf);
		}

		public async Task<bool> IsUserActiveAtCampaign(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			var participant = await _userRepository.GetParticipantByCampaign(userId, campaignId);
			return participant != null && participant.Active;
		}

		public async Task<bool> IsParticipantActiveAtCampaign(Guid participantId, Guid campaignId)
		{
			if (participantId == Guid.Empty)
				throw MotivaiException.ofValidation("Participante inválido.");
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			var user = await _userRepository.GetUserByParticipantId(participantId);
			if (user == null) return false;
			var participant = user.GetParticipantByCampaign(campaignId);
			return participant != null && participant.Active;
		}

		public async Task<Guid> GetParticipantIdIfActive(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			return await _userRepository.GetParticipantIdByUserAndCampaign(userId, campaignId);
		}

		public async Task<UserParticipantCampaign> GetParticipantByUserAndCampaign(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);

			var participant = await _userRepository.GetParticipantByCampaign(userId, campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");
			return participant;
		}

		public async Task<List<UserCampaignsInfo>> SearchParticipantInBusinessType(UserBusinessType businessType, string document, string name)
		{
			List<User> users = null;
			if (document != null)
			{
				User user = null;
				if (Cpf.IsCpf(document))
				{
					user = await _userRepository.GetByCpf(document);
				}
				else if (Cnpj.IsCnpj(document))
				{
					user = await _userRepository.GetByCnpj(document);
				}
				else
				{
					throw MotivaiException.ofValidation("CPF ou CNPJ inválido.");
				}
				if (user != null)
					users = new List<User>() { user };
			}
			else if (!string.IsNullOrEmpty(name))
			{
				users = await _userRepository.GetByBusinessTypeAndName(businessType, name);
			}
			else
			{
				throw MotivaiException.ofValidation("Informe o nome ou documento para pesquisa.");
			}

			if (users == null || users.Count == 0) return null;
			return users.Select(u => new UserCampaignsInfo()
			{
				UserId = u.Id,
				Name = u.GetName(),
				Document = u.GetDocument(),
				Campaigns = u.GetActiveCampaigns()
			}).ToList();
		}

		public async Task<ParticipantInfo> GetParticipantInfo(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);

			var user = await _userRepository.Get(userId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");
			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null) return null;

			return CreateParticipantInfo(user, participant);
		}

		public async Task<ParticipantInfo> GetParticipantBillingInfo(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);

			var user = await _userRepository.Get(userId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				return null;

			var participantInfo = CreateParticipantBillingInfo(user, participant);
			if (participant.Addresses != null && participant.Addresses.Count > 0)
			{
				var mainAddress = participant.Addresses.FirstOrDefault(a => a.Active && a.AddressName == "Casa");
				if (mainAddress == null)
					mainAddress = participant.Addresses.FirstOrDefault(a => a.Active && a.MainAddress);
				if (mainAddress == null)
					mainAddress = participant.Addresses.FirstOrDefault(a => a.Active);

				participantInfo.MainAddress = mainAddress;
			}
			if (user.Type == PersonType.Fisica)
				return participantInfo;

			if (participant.AccountRepresentative != null)
			{
				participantInfo.AccountRepresentative = participant.AccountRepresentative;
				return participantInfo;
			}

			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			if (campaignSettings.Parametrizations.EnableAccountRepresentative)
			{
				throw MotivaiException.ofValidation("O representante da conta deve ser preenchido.");
			}
			return participantInfo;
		}

		private static ParticipantInfo CreateParticipantBillingInfo(User user, UserParticipantCampaign participant)
		{
			return new ParticipantInfo()
			{
				UserId = user.Id,
				ParticipantId = participant.Id,
				CampaignId = participant.CampaignId,
				ClientUserId = participant.ClientUserId,
				Active = user.Active && participant.Active,
				Login = participant.Login,
				Name = user.Type == PersonType.Juridica && string.IsNullOrEmpty(user.Name) ? user.CompanyName : user.Name,
				BirthDate = user.BirthDate,
				CompanyName = user.CompanyName,
				Type = user.Type,
				Rg = user.Rg,
				Cpf = user.Cpf,
				Cnpj = user.Cnpj,
				AccountRepresentative = participant.AccountRepresentative,
				StateInscriptionExempt = user.StateInscriptionExempt,
				StateInscription = user.StateInscription,
				StateInscriptionUf = user.StateInscriptionUf,
				CompanyIdentifier = participant.GetCompanyIdentifier(),
				MainPhone = participant.GetMainPhone(),
				MobilePhone = participant.GetMobilePhone(),
				HomePhone = participant.GetHomePhone(),
				CommercialPhone = participant.GetCommercialPhone(),
				MainEmail = participant.GetMainEmail(),
				PersonalEmail = participant.GetPersonalEmail(),
				CommercialEmail = participant.GetCommercialEmail(),
				MainPhoneLastUpdateDate = participant.GetMainPhoneLastUpdateDate(),
				MobilePhoneLastUpdateDate = participant.GetMobilePhoneLastUpdateDate(),
				HomePhoneLastUpdateDate = participant.GetHomePhoneLastUpdateDate(),
				CommercialPhoneLastUpdateDate = participant.GetCommercialPhoneLastUpdateDate(),
				MainEmailLastUpdateDate = participant.GetMainEmailLastUpdateDate(),
				PersonalEmailLastUpdateDate = participant.GetPersonalEmailLastUpdateDate(),
				CommercialEmailLastUpdateDate = participant.GetCommercialEmailLastUpdateDate()
			};
		}

		public async Task<dynamic> GetParticipantClientData(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);

			var user = await _userRepository.Get(userId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");
			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null) return null;

			return new
			{
				UserId = user.Id,
				CampaignId = campaignId,
				Type = user.Type,
				Document = user.GetDocument(),
				ClientUserId = participant.ClientUserId
			};
		}

		public Task<ParticipantInfo> GetUserData(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			return this._userRepository.GetUserData(userId, campaignId);
		}

		public Task<dynamic> GetDocumentBy(Guid userId)
		{
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("ID do Usuário deve ser informado.");

			return this._userRepository.GetDocumentBy(userId);
		}

		public Task<List<UserCampaignsInfo>> GetUsersByListOfIds(List<Guid> usersIds)
		{
			if (usersIds == null)
				throw MotivaiException.ofValidation("Informe a lista de IDs para buscar");

			return this._userRepository.GetUsersByListOfIds(usersIds);
		}

		public Task<UserBasicInfo> GetUserBasicInfo(Guid campaignId, string document)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("ID da campanha deve ser informado.");

			if (document == null)
				throw MotivaiException.ofValidation("Documento do Usuário deve ser informado.");

			return this._userRepository.GetUserBasicInfoByDocument(campaignId, document);
		}

		public async Task<dynamic> SearchPersonByDocument(string document, Guid campaignId = default(Guid))
		{
			if (document == null)
			{
				throw MotivaiException.ofValidation("CPF/CNPJ deve ser informado.");
			}

			var personType = PersonTypeHelper.FromDocument(document);

			var consultPersonDataParams = await this.campaignRepository.GetPersonDataConsultParametrizations(campaignId);

			var participant = await GetUserInfoByDocument(document);

			if (participant != null)
			{
				return ConsultPersonDataBuilder.BuildWithEnabledFieldsOrDefault(participant, consultPersonDataParams);
			}

			var person = await credifyRepository.QueryDocument(personType, document);
			if (person == null || string.IsNullOrEmpty(person.Name))
			{
				return ConsultPersonDataBuilder.BuildWithDefaultEnabledFields(document, personType);
			}
			return ConsultPersonDataBuilder.BuildWithEnabledFieldsOrDefault(person, consultPersonDataParams);
		}

		public Task<AccountRepresentative> GetAccountRepresentative(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			return _userRepository.GetAccountRepresentative(userId, campaignId);
		}

		public Task<bool> ExistUserWithDocument(Guid userId, string document)
		{
			if (userId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("ID do Usuário deve ser informado.");
			}
			if (string.IsNullOrEmpty(document))
			{
				throw MotivaiException.ofValidation("Documento deve ser informado.");
			}

			return _userRepository.ExistUserWithDocument(userId, document);
		}

		public async Task<bool> SaveUserAppCard(Guid campaignId, Guid userId, Card userCard)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			if (userCard == null)
				throw MotivaiException.ofValidation("Dados do cartão inválido.");

			userCard.Validate();

			await this.ValidateAcceptedCards(campaignId, userCard);
			var userCardsDb = await _userRepository.GetAppCards(userId, campaignId, false);
			if (userCardsDb == null)
				userCardsDb = new List<Card>();

			userCard.AddOrUpdateCard(userCardsDb);

			var result = await this._userRepository.UpdateUserAppCards(userId, campaignId, userCardsDb);
			if (result)
			{
				var log = ParticipantRegistrationLog.OfCardUpdate(userId, campaignId, userCard, userCardsDb);
				try
				{
					await this.participantRegistrationLogRepository.RegisterLog(log);
				}
				catch (Exception ex)
				{
					await ExceptionLogger.LogException(ex, "User - Card Update",
						"Erro durante registro de log da atualização do cartão"
					);
				}
			}
			return result;
		}

		private async Task ValidateAcceptedCards(Guid campaignId, Card userCard)
		{
			var cardsAccepted = await this.getAcceptedCardsCodesFromVrFlexSettings(campaignId);

			if (!((userCard.CardCode != null && cardsAccepted.Contains(userCard.CardCode)) ||
				(userCard.CardCodes != null && userCard.CardCodes.Any(code => cardsAccepted.Contains(code))))
			)
			{
				throw MotivaiException.ofValidation("Tipo de cartão inválido.");
			}
		}

		public Task<List<Card>> GetAppCards(Guid campaignId, Guid userId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			return this._userRepository.GetAppCards(userId, campaignId, false);
		}

		public async Task<List<Card>> GetActiveAppCards(Guid campaignId, Guid userId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			var cardsAccepted = await this.getAcceptedCardsCodesFromVrFlexSettings(campaignId);

			return await this._userRepository.GetActiveAppCards(userId, campaignId, cardsAccepted);
		}

		public async Task<bool> ResetAppCards(Guid campaignId, Guid userId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			var cardsAccepted = await this.getAcceptedCardsCodesFromVrFlexSettings(campaignId);

			var result = await this._userRepository.ResetAppCards(campaignId, userId, cardsAccepted);
			if (result)
			{
				var log = ParticipantRegistrationLog.OfAction(RegistrationAction.FIRST_ACCESS_AND_WALLET_RESET,
					userId, campaignId, null, (SharedKernel.Domain.Entities.References.Security.LocationInfo)null);
				try {
					await this.participantRegistrationLogRepository.RegisterLog(log);
				}
				catch (Exception ex)
				{
					await ExceptionLogger.LogException(ex, "User - Wallet Reset", "Erro durante registro de log do reset de wallet");
				}
			}
			return result;
		}

		private async Task<List<string>> getAcceptedCardsCodesFromVrFlexSettings(Guid campaignId)
		{
            return null;
        }

		public async Task<List<UserCampaignsInfo>> GetUserChildrenParticipants(Guid userId, Guid campaignId)
		{
			this.ValidateCampaignAndUser(campaignId, userId);
			var users = await this._userRepository.GetUserChildrenParticipants(userId, campaignId);
			if (users.IsNullOrEmpty())
			{
				return null;
			}
			return users.Select(u => new UserCampaignsInfo
			{
				UserId = u.Id,
				Document = u.Cpf ?? u.Cnpj,
				Name = u.Name,
				Active = u.Active,
				Blocked = u.GetParticipantByCampaign(campaignId).Blocked
			})
				.ToList();
		}

		private void ValidateCampaignAndUser(Guid campaignId, Guid userId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("ID da campanha deve ser informado.");
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("ID do usuário deve ser informado.");
		}
    }
}