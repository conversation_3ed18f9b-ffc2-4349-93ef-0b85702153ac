using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IApp.UsersManagement;
using Motivai.Users.Domain.IRepository.UsersManagement;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.Models.UserParticipant;

namespace Motivai.Users.App
{
	public class UserManagementApp : IUserManagementApp
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly IUserManagementRepository userManagementRepository;
		private readonly IUserRepository userRepository;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;

		public UserManagementApp(ICampaignRepository campaignRepository, IUserManagementRepository userManagementRepository,
			IUserRepository userRepository, ICampaignsGroupsApp campaignsGroupsApp,
			IParticipantRegistrationLogRepository participantRegistrationLogRepository)
		{
			this.campaignRepository = campaignRepository;
			this.userManagementRepository = userManagementRepository;
			this.userRepository = userRepository;
			this.campaignsGroupsApp = campaignsGroupsApp;
			this.participantRegistrationLogRepository = participantRegistrationLogRepository;
		}

		public async Task<List<UserCampaignsInfo>> SearchParticipantByDocument(Guid campaignId, string document, string login)
		{
			List<User> users = null;

			if (String.IsNullOrEmpty(document) && String.IsNullOrEmpty(login))
				throw MotivaiException.ofValidation("Informe um filtro para pesquisar os participantes.");

			User user = null;

			if (!String.IsNullOrEmpty(document))
			{
				if (Cpf.IsCpf(document))
				{
					user = await this.userManagementRepository.GetByCpf(campaignId, document);
				}
				else if (Cnpj.IsCnpj(document))
				{
					user = await this.userManagementRepository.GetByCnpj(campaignId, document);
				}
				else
				{
					throw MotivaiException.ofValidation("CPF ou CNPJ inválido.");
				}

			}
			else if (!String.IsNullOrEmpty(login))
			{
				user = await this.userRepository.GetByLogin(campaignId, login, false);
			}

			if (user == null) return null;

			users = new List<User>() { user };

			return users.Select(u => new UserCampaignsInfo()
			{
				UserId = u.Id,
				Name = u.GetName(),
				Document = u.GetDocument(),
				Active = u.GetParticipantByCampaign(campaignId).Active
			}).ToList();
		}

		public async Task<bool> UpdateRegistrationDataRanking(Guid userId, Guid campaignId, ParticipantDataModel participantData)
		{
			if (participantData == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

			var user = await userRepository.Get(userId);
			if (user == null)
				throw MotivaiException.ofValidation("Usuário não encontrado.");

			if (!user.UsersParticipantCampaign.Any())
				throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

			user.Name = participantData.Name;

			if (user.IsFisicPerson())
			{
				user.Cpf = Extractor.RemoveMasks(participantData.Cpf);
				user.Rg = Extractor.RemoveMasks(participantData.Rg);
				user.Gender = participantData.Gender;
				user.MaritalStatus = participantData.MaritalStatus;
				user.BirthDate = participantData.BirthDate.ToDate();
			}
			else if (user.IsJuridicPerson())
			{
				user.Cnpj = Extractor.RemoveMasks(participantData.Cnpj);
				user.CompanyName = participantData.CompanyName;

				user.StateInscriptionExempt = participantData.StateInscriptionExempt;
				if (user.StateInscriptionExempt)
				{
					user.StateInscription = null;
					user.StateInscriptionUf = null;
				}
				else
				{
					user.StateInscription = participantData.StateInscription;
					user.StateInscriptionUf = participantData.StateInscriptionUf;
				}
			}

			user.GpInf = participantData.GpInf;
			user.GpPartnerInf = participantData.GpPartnerInf;
			user.PhotoUrl = participantData.PhotoUrl;

			if (participantData.Contact != null)
			{
				if (UserParticipantValidation.CheckForContactChanges(participant.Contact, participantData.Contact)) {
					var log = ParticipantRegistrationLog.OfContactUpdate(RegistrationAction.UPDATE_CONTACT,
						"Gestão de Participantes (antigo)", userId, campaignId, participant.Contact, participantData.Contact,
						participantData.LocationInfo
					);

					await this.participantRegistrationLogRepository.RegisterLog(log);
				}

				UserParticipantValidation.CopyContactFields(participant, participantData.Contact);
				participant.Contact.StartValidation();
			}

			participant.Active = participantData.Active;
			participant.Blocked = participantData.Blocked;
			participant.RankingId = participantData.RankingId;

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			user.Validate(campaignSettings);

			var wasUpdate = await this.userRepository.UpdateRegistrationData(user, participant, true, true);
			if (wasUpdate)
			{
				await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.REGISTRATION_UPDATE,
					Guid.Empty, campaignId, user.Id, participant.Id, Guid.Empty
				);
			}
			return wasUpdate;
		}

		public async Task<bool> ResetParticipantFirstAccess(Guid userId, Guid campaignId, OperationUser operationUser)
		{
			var result = await this.userRepository.ResetFirstAccess(userId, campaignId);
			if (result)
			{
				var log = ParticipantRegistrationLog.OfAction(RegistrationAction.FIRST_ACCESS_RESET,
					userId, campaignId, operationUser, null);
				await this.participantRegistrationLogRepository.RegisterLog(log);
			}
			return result;
		}
	}
}
