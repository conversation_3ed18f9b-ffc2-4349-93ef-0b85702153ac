using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.Users;

namespace Motivai.Users.App
{
	public class UserMetadataApp : IUserMetadataApp
	{
		private readonly IUserMetadataValueRepository userMetadataValuesRepository;
		public UserMetadataApp(IUserMetadataValueRepository userMetadaValueRepository)
		{
			this.userMetadataValuesRepository = userMetadaValueRepository;
		}

		public Task<UserMetadataValue> GetUsersMetadataValue(Guid campaignId, Guid userId)
		{
			return userMetadataValuesRepository.GetUsersMetadataValue(campaignId, userId);
		}

		public Task<string> GetUserMetadataFieldValue(Guid userId, Guid campaignId, string fieldKey)
		{
			return userMetadataValuesRepository.GetUserMetadataFieldValue(userId, campaignId, fieldKey);
		}

		public async Task<bool> SaveUserMetadata(Guid userId, Guid campaignId, Guid participantId, Dictionary<string, string> userMetadata, string origin = null)
		{
			var currentMetadata = await GetUsersMetadataValue(campaignId, userId);

			if (currentMetadata == null)
			{
				currentMetadata = new UserMetadataValue
				{
					Id = Guid.NewGuid(),
					CreateDate = DateTime.UtcNow,
					UpdateDate = DateTime.UtcNow,
					Origin = origin,
					CampaignId = campaignId,
					ParticipantId = participantId,
					UserId = userId,
					Metadata = userMetadata
				};
			}
			else
			{
				currentMetadata.Metadata = userMetadata;
				currentMetadata.UpdateDate = DateTime.UtcNow;
			}

			await userMetadataValuesRepository.SaveUserMetadata(userId, campaignId, currentMetadata);
			return true;
		}
	}
}