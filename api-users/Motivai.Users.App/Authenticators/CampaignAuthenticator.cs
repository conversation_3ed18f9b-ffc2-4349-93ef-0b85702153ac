using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.IApp.Authenticators;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.ClientIntegrations;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Services.Authentication;

namespace Motivai.Users.App.Authenticators
{
	///<summary>
	/// Autenticação de participante na campanha.
	/// A autenticação é efetuado de acordo com o parâmetro `LoginType` na campanha.
	///</summary>
	public class CampaignAuthenticator : ICampaignAuthenticator
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly BaseAuthenticator catalogAuthenticator;
		private readonly AccountOperatorAuthenticator operatorAuthenticator;
		private readonly IClientIntegrations clientIntegrations;

		public CampaignAuthenticator(ICampaignRepository campaignRepository, CatalogAuthenticator catalogAuthenticator,
			AccountOperatorAuthenticator operatorAuthenticator,
			IClientIntegrations clientIntegrations)
		{
			this.campaignRepository = campaignRepository;
			this.catalogAuthenticator = catalogAuthenticator;
			this.operatorAuthenticator = operatorAuthenticator;
			this.clientIntegrations = clientIntegrations;
		}

		public async Task<UserParticipantModel> AuthenticateParticipantByCampaignLoginType(Guid campaignId, ParticipantLoginModel login)
		{
			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			switch (campaignSettings.Parametrizations.LoginType)
			{
				case CampaignLoginType.Password:
				case CampaignLoginType.PasswordAndSsoOAuth:
					return await AuthenticateParticipantByPassword(campaignId, campaignSettings, login);
				case CampaignLoginType.IntegratedPassword:
					return await AuthenticateParticipantByClientIntegration(campaignId, login);
				default:
					throw MotivaiException.ofValidation("Campanha não suporta o formato configurado.");
			}
		}

		public Task<UserParticipantModel> AuthenticateParticipantByPassword(Guid campaignId,
			CampaignSettingsModel campaignSettings, ParticipantLoginModel participantLogin)
		{
			if (campaignSettings.Parametrizations.AllowAccountOperators)
			{
				return operatorAuthenticator.Authenticate(campaignId, participantLogin);
			}
			return catalogAuthenticator.Authenticate(campaignId, participantLogin);
		}

		private Task<UserParticipantModel> AuthenticateParticipantByClientIntegration(Guid campaignId, ParticipantLoginModel login)
		{
			return clientIntegrations.AuthenticateUsingIntegratedPassword(campaignId, login);
		}
	}
}