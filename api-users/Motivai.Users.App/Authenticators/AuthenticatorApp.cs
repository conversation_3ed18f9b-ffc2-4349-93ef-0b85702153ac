using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Domain.Entities.Integrations;
using Motivai.Users.Domain.Entities.Integrations.CampaignsGroup;
using Motivai.Users.Domain.Enums;
using Motivai.Users.Domain.IApp.Ath;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.Models.ParticipantCampaign;
using Motivai.Users.Domain.Services.Authentication;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.IRepository.UsersParticipantCampaigns;
using Motivai.Users.Domain.Services.Participants.Creation;
using Motivai.Users.Domain.Services.Users.Creation;
using Motivai.Users.Domain.IApp.Authenticator;
using Motivai.Users.Domain.Entities.Configurations.Security;
using Motivai.Users.Domain.Models.Security;
using Motivai.Users.Domain.Services.Security;
using Motivai.Users.Domain.ex;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.AccountOperators.Authentication;
using Motivai.Users.Domain.IRepository.Users.AccountOperators;
using Motivai.Users.Domain.IRepository.Security;
using Motivai.Users.Domain.Entities.AccountOperators;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Users.App
{
    public class AuthenticatorApp : IAuthenticatorApp
	{
		private readonly ICampaignRepository _campaignRepository;
		private readonly IUserRepository _userRepository;
		private readonly IUserParticipationCampaignRepository _participantRepository;
		private readonly IAccessLogRepository _accessLogRepository;
		private readonly IAuthenticationLogRepository _authenticationLogRepository;
		private readonly BaseAuthenticator catalogAuthenticator;
		private readonly BaseAuthenticator mobileAuthenticator;
		private readonly AccountOperatorAuthenticator operatorAuthenticator;
		private readonly CampaignGroupSsoAuthenticator campaignGroupAuthentication;
		private readonly CallcenterAuthenticator callcenterAuthenticator;
        private readonly CampaignIntegrationAuthenticator campaignIntegrationAuthenticator;
        private readonly CampaignIntegrationParticipantCreator campaignIntegrationParticipantCreator;
		private readonly IAccessControlRegisterApp accessControlRegister;
		private readonly SecurityCodeSender securityCodeSender;
		private readonly IUserConfigurationRepository userConfiguratioRepository;
		private readonly IAccountOperatorRepository accountOperatorRepository;
        private readonly IBlockListedIpRepository blockListedIpRepository;

        public AuthenticatorApp(ICampaignRepository campaignApiRepository,
			IUserRepository userRepository,
			IUserParticipationCampaignRepository userParticipationCampaignRepository,
			IAccessLogRepository accessLogRepository, IAuthenticationLogRepository authenticationLogRepository, CatalogAuthenticator catalogAuthenticator,
			MobileAuthenticator mobileAuthenticator, AccountOperatorAuthenticator operatorAuthenticator,
			CampaignGroupSsoAuthenticator campaignGroupAuthentication,
			CallcenterAuthenticator callcenterAuthenticator,
			CampaignIntegrationAuthenticator campaignIntegrationAuthenticator,
			CampaignIntegrationParticipantCreator campaignIntegrationParticipantCreator,
			IAccessControlRegisterApp accessControlRegister,
			SecurityCodeSender securityCodeSender,
			IUserConfigurationRepository userConfiguratioRepository,
			IAccountOperatorRepository accountOperatorRepository,
			IBlockListedIpRepository blockListedIpRepository
		)
		{
			this._campaignRepository = campaignApiRepository;
			this._userRepository = userRepository;
			this._participantRepository = userParticipationCampaignRepository;
			this._accessLogRepository = accessLogRepository;
			this._authenticationLogRepository = authenticationLogRepository;
			this.catalogAuthenticator = catalogAuthenticator;
			this.mobileAuthenticator = mobileAuthenticator;
			this.operatorAuthenticator = operatorAuthenticator;
			this.campaignGroupAuthentication = campaignGroupAuthentication;
			this.campaignIntegrationParticipantCreator = campaignIntegrationParticipantCreator;
			this.callcenterAuthenticator = callcenterAuthenticator;
            this.campaignIntegrationAuthenticator = campaignIntegrationAuthenticator;
            this.accessControlRegister = accessControlRegister;
			this.securityCodeSender = securityCodeSender;
			this.userConfiguratioRepository = userConfiguratioRepository;
			this.accountOperatorRepository = accountOperatorRepository;
            this.blockListedIpRepository = blockListedIpRepository;
        }

		private async Task ValidateCampaignSite(Guid campaignId, CampaignSiteLogin login)
		{
			var campaignActive = await _campaignRepository.GetActive(campaignId);

			try
            {
				if (!campaignActive)
					throw new AuthenticantionException(AuthenticationActionLog.INVALID_CAMPAIGN, "Usuário e/ou senha inválido(s).");
            }
            catch (AuthenticantionException ex)
            {
                await RegisterActionLogCampaignSite(campaignId, ex.Action, login);
                throw MotivaiException.ofValidation("AUTHENTICATION_FAILED", ex.Message);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Authentication - Catalog", "Erro ao efetuar autenticação", true);
                throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
            }
		}

		public async Task<bool> RegisterAccessLog(LogRegisterAction action, Guid userId, Guid campaignId, string document, LocationInfo locationInfo = null)
		{
			await _accessLogRepository.RegisterLogin(action, userId, campaignId, document, locationInfo);
			await _authenticationLogRepository.RegisterLogin(action.MapActionToAuthenticationActionLog(), userId, campaignId, document, locationInfo);
			return true;
		}

		public Task<UserParticipantModel> AuthenticateForMobile(Guid campaignId, ParticipantLoginModel login)
		{
			return mobileAuthenticator.Authenticate(campaignId, login);
		}

		public async Task<bool> ExistLoginInCampaign(Guid campaignId, string login)
		{
			if (campaignId == null)
				throw MotivaiException.ofValidation("Campanha inválida.");
			var user = await _participantRepository.GetUserByCampaignAndLogin(campaignId, login);
			if (user == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");
			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");
			return user.Active && participant.Active;
		}

		///<summary>
		/// Será depreceado em favor da autenticação pela CampaignAuthenticator.
		/// Aqui é fixo por Login e Senha da plataforma.
		///</summary>
		public async Task<UserParticipantModel> AuthenticateParticipantByPassword(Guid campaignId, ParticipantLoginModel participantLogin)
		{
			var campaignSettings = await _campaignRepository.GetSettings(campaignId);
			if (campaignSettings.Parametrizations.AllowAccountOperators)
			{
				return await operatorAuthenticator.Authenticate(campaignId, participantLogin);
			}
			return await catalogAuthenticator.Authenticate(campaignId, participantLogin);
		}

		public Task<UserParticipantModel> AuthenticateOperatorInAccount(Guid campaignId, OperatorAuthenticationModel operatorAuthentication)
		{
			return operatorAuthenticator.AuthenticateOperatorInAccount(campaignId, operatorAuthentication);
		}

		public Task<UserParticipantModel> AuthenticateParticipantForCallcenter(Guid campaignId, CallcenterLogin login)
		{
			return callcenterAuthenticator.AutheticateParticipantForCallcenter(campaignId, login);
		}

		public Task<Guid> AuthenticateParticipantForCallcenterSso(Guid campaignId, CallcenterLogin login)
		{
			return callcenterAuthenticator.AuthenticateParticipantForCallcenterSso(campaignId, login);
		}

		///<summary>
		/// Autenticação do login via SSO pelo site campanha.
		///</summary>
		public async Task<UserParticipantModel> AuthenticateCampaignSiteIntegration(Guid campaignId, CampaignSiteLogin campaignSiteRequest)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (campaignSiteRequest == null)
				throw MotivaiException.ofValidation("Informe os dados para autenticação.");
			campaignSiteRequest.Validate();

			if (campaignSiteRequest.IsAccountOperator())
			{
				return await operatorAuthenticator.AuthenticateOperatorInAccount(campaignId,
					LogRegisterAction.LoginFromSite,  campaignSiteRequest.CreateOperatorAuthentication());
			}

			await ValidateCampaignSite(campaignId, campaignSiteRequest);

            LoggerFactory.GetLogger().Info("Cmp {} - Autenticando participante site: {}", campaignId, campaignSiteRequest.UserId);

			var user = await _userRepository.Get(campaignSiteRequest.UserId);
			if (user == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");
			user.ValidateForSession();

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");
			participant.ValidateForSession();

			await VerifyIfOriginIsBlockListed(campaignSiteRequest.ConnectionInfo);

			await this.campaignIntegrationParticipantCreator.UpdateParticipantBalance(user, participant);

			var campaignSettings = await _campaignRepository.GetSettings(campaignId);

			var participantSession = UserParticipantModel.Of(campaignId, user, participant, campaignSiteRequest.Origin, campaignSiteRequest.Timezone, campaignSettings);
			if (campaignSiteRequest.SessionId.HasValue)
			{
				participantSession.SessionId = campaignSiteRequest.SessionId;
			}
			await accessControlRegister.RegisterAccessAndNotificate(LogRegisterAction.LoginFromSite, participantSession, campaignSiteRequest.ConnectionInfo, campaignSettings);
			return participantSession;
		}

		public Task<CampaignGroupSsoResult> AuthenticateAtTargetGroupCampaign(Guid campaignId, CampaignGroupSsoRequest ssoRequest)
		{
			return campaignGroupAuthentication.AuthenticateAtTargetGroupCampaign(campaignId, ssoRequest);
		}

        public Task<UserParticipantModel> AuthenticateParticipantByIntegration(Guid campaignId, ParticipantIntegrationData login)
		{
			return campaignIntegrationAuthenticator.AuthenticateParticipantByIntegration(campaignId, login);
		}

		public Task<Guid> AuthenticateByPlatformSso(Guid campaignId, ParticipantIntegrationData login)
		{
			return campaignIntegrationAuthenticator.AuthenticateByPlatformSso(campaignId, login);
		}

		public Task<UserParticipantModel> FinalizeAuthenticatedUserUsingSsoToken(Guid campaignId, LoginSsoEndingRequest loginSso)
		{
			return campaignIntegrationAuthenticator.FinalizeAuthenticatedUserUsingSsoToken(campaignId, loginSso);
		}

		public async Task<UserParticipantModel> AuthenticateOperatorBySSO(Guid campaignId, AccountOperatorSsoRequest operatorSso)
		{
			if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (operatorSso == null)
                throw MotivaiException.ofValidation("Dados de autenticação são obrigatórios.");

			LoggerFactory.GetLogger().Info("Cmp {} - Autenticando integração de operadores - Cpf: {} - Email: {} - na conta Cnpj - {}",
				campaignId, operatorSso.Cpf, operatorSso.Email, operatorSso.Cnpj);

			try
            {
                return await CreateOperatorSession(campaignId, operatorSso);
            }
            catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Operators - SSO", "Erro ao efetuar autenticação do operador via SSO", true);
				if (ex is MotivaiException)
					throw;
                throw MotivaiException.ofException("Ocorreu um erro durante o login, por favor, tente novamente.", ex);
			}
		}

        private async Task<UserParticipantModel> CreateOperatorSession(Guid campaignId, AccountOperatorSsoRequest operatorRequest,
             LogRegisterAction action = LogRegisterAction.VrIdSso)
        {
            Guid userId = Guid.Empty;
            var campaignSettings = await _campaignRepository.GetSettings(campaignId);
            if (!campaignSettings.Parametrizations.AllowAccountOperators)
                throw MotivaiException.ofValidation("Não é permitido login por operador.");

            var accountOperator = await this.accountOperatorRepository.FindAccountOperatorByEmailAndDocument(campaignId, operatorRequest);
            if (accountOperator == null)
                throw MotivaiException.ofValidation("Conta não encontrada, por favor, efetue login utilizando o e-mail.");

            AccountOperatorLogin operatorLogin;
            if (Cnpj.IsCnpj(operatorRequest.Cnpj))
            {
                userId = await _userRepository.GetUserIdByDocument(operatorRequest.Cnpj, campaignId);
                if (userId == Guid.Empty)
                {
                    throw MotivaiException.ofValidation("Conta não encontrada, por favor, efetue login utilizando o e-mail.");
                }

                operatorLogin = accountOperator.GetLoginByCampaignUserAndLogin(campaignId, userId, operatorRequest.Email);
            }
            else
            {
                operatorLogin = accountOperator.GetLoginByCampaignAndLogin(campaignId, operatorRequest.Email);
            }

            if (operatorLogin == null)
                throw MotivaiException.ofValidation("Conta não encontrada, por favor, efetue login utilizando o e-mail.");

            await _authenticationLogRepository.RegisterLogin(action.MapActionToAuthenticationActionLog(), Guid.Empty,
                campaignId, accountOperator.Document, operatorRequest.LocationInfo);

            if (userId != Guid.Empty)
            {
                return await AuthenticateOperatorInAccount(campaignId, new OperatorAuthenticationModel()
                {
                    UserId = userId,
                    AccountOperatorId = accountOperator.Id,
                    AccountOperatorLoginId = operatorLogin.Id,
                    Timezone = operatorRequest.LocationInfo.Timezone,
                    ConnectionInfo = operatorRequest.LocationInfo.ConnectionInfo
                });
            }

            return UserParticipantModel.OfExternalAccess(accountOperator, operatorLogin);
        }

        public async Task<UserParticipantModel> RefreshLoggedParticipant(Guid campaignId, Guid userId)
		{
			var user = await _userRepository.GetUserAndParticipant(userId, campaignId);
			if (user == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");

			user.ValidateForSession();

			var participant = user.GetParticipantByCampaign(campaignId);
			if (participant == null)
				throw MotivaiException.ofValidation("Participante não encontrado.");

			participant.ValidateForSession();

			await this.campaignIntegrationParticipantCreator.UpdateParticipantBalance(user, participant);

			var campaignSettings = await _campaignRepository.GetSettings(campaignId);

			var participantSession = UserParticipantModel.Of(campaignId, user, participant, null, null, campaignSettings);
			// participante já passou pelo MFA
			participantSession.NeedAuthenticationMfa = false;
			return participantSession;
		}

		public async Task<bool> SendAuthenticationCode(Guid userId, SimpleSecurityCodeRequest securityCodeRequest) {
			if (securityCodeRequest == null)
				throw MotivaiException.ofValidation("Método de envio do código de segurança inválido.");

			var securityCode = securityCodeSender.GenerateCode();

			var generatedCode = GeneratedSecurityCode.Of(securityCodeRequest.CampaignId, userId.ToString(), securityCode);
			await userConfiguratioRepository.SaveSecurityCode(userId, generatedCode);

			await securityCodeSender.SendSecurityCodeByMethod(userId, securityCodeRequest.CampaignId, securityCodeRequest.SendMethod, securityCode);
			return true;
		}

		public async Task<bool> ValidateAuthenticationCode(Guid userId, SimpleSecurityCodeValidation codeValidation) {
			if (codeValidation == null || string.IsNullOrEmpty(codeValidation.Code))
				throw MotivaiException.ofValidation("Código de segurança inválido.");

			var generatedCode = await userConfiguratioRepository.GetSecurityCodeForValidation(userId);
			if (generatedCode == null || string.IsNullOrEmpty(generatedCode.HashedSecurityCode))
				throw MotivaiException.ofValidation("Código de segurança inválido.");

			generatedCode.ValidateSecurityCodeAndSubject(codeValidation.Code, userId.ToString());
			return true;
		}

		private Task RegisterActionLogCampaignSite(Guid campaignId, AuthenticationActionLog action, CampaignSiteLogin login)
        {
			var authenticationLog = CampaignAuthenticationLog.CampaignSiteAuthenticationTried(campaignId, action, login);
            return accessControlRegister.RegisterAuthenticationLog(authenticationLog);
        }

		private async Task VerifyIfOriginIsBlockListed(ConnectionInfo connectionInfo)
		{
			if (await blockListedIpRepository.ContainsIp(connectionInfo.GetRemoteIpAddress())) {
				throw new AuthenticantionException(AuthenticationActionLog.BLOCK_LISTED_IP, "Usuário e/ou senha inválido(s).");
			}
		}
	}
}
