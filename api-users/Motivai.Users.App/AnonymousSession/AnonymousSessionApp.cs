using System;
using System.Threading.Tasks;
using Motivai.Users.Domain.Entities.AnonymousSession;
using Motivai.Users.Domain.IApp.AnonymousSession;
using Motivai.Users.Domain.IRepository.AnonymousSession;
using Motivai.Users.Domain.Models.AnonymousSession;

namespace Motivai.Users.App.AnonymousSession
{
	public class AnonymousSessionApp : IAnonymousSessionApp
	{
		private readonly IAnonymousSessionRepository anonymousSessionRepository;

		public AnonymousSessionApp(IAnonymousSessionRepository anonymousSessionRepository)
		{
			this.anonymousSessionRepository = anonymousSessionRepository;
		}

		public async Task CookieAccepted(AnonymousSessionAcceptanceInfo sessionInfo)
		{
			await this.anonymousSessionRepository.RegisterCookieAcceptance(AnonymousSessionCookieAcceptance.Of(sessionInfo));
		}
	}
}