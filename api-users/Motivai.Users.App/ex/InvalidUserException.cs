using System;
using Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog;

namespace Motivai.Users.App.ex
{
	[Serializable]
	public class AuthenticationFailedException : Exception
	{
		public UserAdministrationAccessLogAction Action { get; set; }

		public AuthenticationFailedException(UserAdministrationAccessLogAction Action, string message) : base(message)
		{
			this.Action = Action;
		}
	}
}