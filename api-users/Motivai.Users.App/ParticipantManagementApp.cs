﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.Users.Domain.Entities;
using Motivai.Users.Domain.Entities.AccessLog;
using Motivai.Users.Domain.Entities.CampaignsGroups;
using Motivai.Users.Domain.Entities.Users;
using Motivai.Users.Domain.Entities.UsersAdministrators.AccessLog;
using Motivai.Users.Domain.IApp.CampaignsGroups;
using Motivai.Users.Domain.IApp.ParticipantManagementApp;
using Motivai.Users.Domain.IApp.Users;
using Motivai.Users.Domain.IRepository.AccessLog;
using Motivai.Users.Domain.IRepository.Camapaigns;
using Motivai.Users.Domain.IRepository.Users;
using Motivai.Users.Domain.Models.UserParticipant;
using Motivai.Users.Domain.IRepository.UsersAdministrations.ActionLog;
using Motivai.Users.Domain.Services.Participants;
using Motivai.Users.Domain.IRepository.ClientIntegrations;

namespace Motivai.Users.App
{
    public class ParticipantManagementApp : IParticipantManagementApp
    {
		private readonly IUserRepository userRepository;
		private readonly ICampaignRepository campaignRepository;
		private readonly IUserMetadataApp userMetadata;
		private readonly IParticipantRegistrationLogRepository participantRegistrationLogRepository;
        private readonly IUserAdministrationActionLogRepository actionLogRepository;
		private readonly ICampaignsGroupsApp campaignsGroupsApp;
        private readonly ParticipantsGroupService participantsGroupService;
        private readonly IClientIntegrations clientIntegrations;

        public ParticipantManagementApp(IUserRepository userRepository, ICampaignRepository campaignRepository,
            IUserMetadataApp userMetadata, IParticipantRegistrationLogRepository participantRegistrationLogRepository,
            ICampaignsGroupsApp campaignsGroupsApp, ParticipantsGroupService participantsGroupService,
            IUserAdministrationActionLogRepository actionLogRepository, IClientIntegrations clientIntegrations)
        {
            this.participantRegistrationLogRepository = participantRegistrationLogRepository;
            this.userRepository = userRepository;
            this.campaignRepository = campaignRepository;
            this.userMetadata = userMetadata;
            this.campaignsGroupsApp = campaignsGroupsApp;
            this.participantsGroupService = participantsGroupService;
            this.actionLogRepository = actionLogRepository;
            this.clientIntegrations = clientIntegrations;
        }

        public async Task<ParticipantManagementModel> GetParticipantRegistration(Guid userId, Guid campaignId)
        {
            var user = await userRepository.GetUserAndParticipant(userId, campaignId);

            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado.");

            if (!user.UsersParticipantCampaign.Any())
                throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

            var participant = user.GetParticipantByCampaign(campaignId);

            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

            var firstAccessSettings = await campaignRepository.GetFirstAccessSettings(campaignId);
            if (firstAccessSettings.EnableMetadataAtFirstAccess == true)
            {
                var usersMetadataValue = await userMetadata.GetUsersMetadataValue(campaignId, userId);
                if (usersMetadataValue != null)
                {
                    var participantData = ParticipantDetailsFrom(campaignId, user, participant);
                    participantData.Metadata = usersMetadataValue.Metadata;
                    participantData.Login = participant.Login;
                    return participantData;
                };
            }

            return ParticipantDetailsFrom(campaignId, user, participant);
        }

		public async Task<bool> UpdateParticipantRegistration(Guid userId, Guid campaignId, ParticipantManagementModel participant)
        {
            if (participant == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

            var user = await userRepository.Get(userId);
            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado.");

            if (!user.UsersParticipantCampaign.Any())
                throw MotivaiException.ofValidation("Usuário não possui nenhuma Campanha ativa.");

            var participantFromCampaign = user.GetParticipantByCampaign(campaignId);
            if (participantFromCampaign == null)
                throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");

            user.Name = participant.Name;

            if (user.IsFisicPerson())
            {
                user.Rg = Extractor.RemoveMasks(participant.Rg);
                user.Gender = participant.Gender;
                user.MaritalStatus = participant.MaritalStatus;
                user.BirthDate = participant.BirthDate.ToDate();
            }
            else if (user.IsJuridicPerson())
            {
                user.CompanyName = participant.CompanyName;

                user.StateInscriptionExempt = participant.StateInscriptionExempt;
                if (user.StateInscriptionExempt)
                {
                    user.StateInscription = null;
                    user.StateInscriptionUf = null;
                }
                else
                {
                    user.StateInscription = participant.StateInscription;
                    user.StateInscriptionUf = participant.StateInscriptionUf;
                }
            }

            if (participant.Contact != null)
            {
                if (UserParticipantValidation.CheckForContactChanges(participantFromCampaign.Contact, participant.Contact)) {
					var updated = ParticipantRegistrationLog.OfContactUpdate(RegistrationAction.UPDATE_CONTACT,
                        "Gestão de Participante", userId, campaignId, participantFromCampaign.Contact, participant.Contact,
                        participant.LocationInfo
                    );

					await this.participantRegistrationLogRepository.RegisterLog(updated);
				}

                UserParticipantValidation.CopyContactFields(participantFromCampaign, participant.Contact);
                participantFromCampaign.Contact.StartValidation();
            }

            participantFromCampaign.Active = participant.Active;
            participantFromCampaign.Blocked = participant.Blocked;
            participantFromCampaign.AllowLoginSite = participant.AllowLoginSite;
            participantFromCampaign.FirstAccess = participant.FirstAccess;
            participantFromCampaign.RankingId = participant.RankingId;

            var campaignSettings = await campaignRepository.GetSettings(campaignId);

            user.Validate(campaignSettings);

            await ExecuteClientIntegration(campaignId, user);

            var wasUpdate = await this.userRepository.UpdateRegistrationData(user, participantFromCampaign, true, true);

            var log = ParticipantRegistrationLog.OfAdminParticipantDataUpdate(userId, campaignId, participant);
            await RegisterLog(log);

            if (wasUpdate)
            {
                if (participant.HasAnyGroup())
                    {
                        await AddParticipantToGroups(campaignId, user, participant);
                    }

                await this.userMetadata.SaveUserMetadata(userId, campaignId, participant.ParticipantID, participant.Metadata, "ParticipantManagement");
                await this.campaignsGroupsApp.UpdateCampaignsInCoalitionIfNeeded(EventType.REGISTRATION_UPDATE,
                    Guid.Empty, campaignId, user.Id, participantFromCampaign.Id, Guid.Empty);
            }
            return wasUpdate;
        }

        public async Task<bool> UnblockingParticipant(Guid userId, Guid campaignId, UnblockingParticipantDetails unblockingRequest)
        {
            unblockingRequest.Validate();
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("PARTICIPANT_UNBLOCKING_ERROR",
						"Id do usuário inválido.");

            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("PARTICIPANT_UNBLOCKING_ERROR",
						"Id da campanha inválido.");

            try
            {
                var participantRegisterLog = new UserActionOperationLog() {
                        AccessDate = new DateTime(),
                        CampaignId = campaignId,
                        Action = "Desbloqueio de Participante",
                        Module = "Campanha - Participante",
                        Feature = "Gestão de Participantes",
                        Metadata = new Dictionary<string, string>(){{"Motivo", unblockingRequest.Reason}},
                        OperationUser = unblockingRequest.OperationUser,
                        LocationInfo = unblockingRequest.LocationInfo,
                };
                await this.actionLogRepository.RegisterLog(UserAdministrationActionLog.OfOperationLog(participantRegisterLog));

                LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Desbloqueando participante",
					campaignId, userId);

                var wasUpdate = await this.userRepository.UnblockingParticipant(userId, campaignId, unblockingRequest);
                if (!wasUpdate)
                    throw MotivaiException.ofValidation("PARTICIPANT_UNBLOCKING_ERROR",
						"Houve um erro ao desbloquear o participante.");

                return wasUpdate;
            }
            catch (Exception ex)
            {
                LoggerFactory.GetLogger().Error("Cmp {0} - Usr {1} - Não foi possível efetuar o desbloqueio do participante",
					campaignId, userId);
                await ExceptionLogger.LogException(ex, "Desbloqueio de Participant", "Erro ao desbloquear participante.", true);
                var participantRegisterLog = new UserActionOperationLog() {
                        AccessDate = new DateTime(),
                        CampaignId = campaignId,
                        Action = "Desbloqueio de Participante",
                        Module = "Campanha - Participante",
                        Feature = "Gestão de Participantes",
                        Metadata = new Dictionary<string, string>(){{"Motivo", unblockingRequest.Reason}},
                        OperationUser = unblockingRequest.OperationUser,
                        LocationInfo = unblockingRequest.LocationInfo,
                };
                await this.actionLogRepository.RegisterLog(UserAdministrationActionLog.OfOperationLog(participantRegisterLog));
                throw ex;
            }
        }

        private ParticipantManagementModel ParticipantDetailsFrom(Guid campaignId, User user, UserParticipantCampaign participant)
		{
			return new ParticipantManagementModel
			{
				UserId = user.Id,
				CreateDate = participant.CreateDate,
				CampaignId = campaignId,
				ParticipantID = participant.Id,
				ClientUserId = participant.ClientUserId,
				Name = user.Name,
				Type = user.Type,
				Rg = user.Rg,
				Gender = user.Gender,
				MaritalStatus = user.MaritalStatus,
				BirthDate = user.BirthDate.ToJavaScriptUTC(),
				GpInf = user.GpInf,
				GpPartnerInf = user.GpPartnerInf,
				Cpf = user.Cpf,
				Cnpj = user.Cnpj,
				Login = participant.Login,
				AllowLoginSite = participant.AllowLoginSite,
				FirstAccess = participant.FirstAccess,
				FirstAccessDate = participant.FirstAccessDate,
				LastAccessDate = participant.LastAccessDate,
				CompanyName = user.CompanyName,
				StateInscriptionExempt = user.StateInscriptionExempt,
				StateInscription = user.StateInscription,
				StateInscriptionUf = user.StateInscriptionUf,
				PhotoUrl = user.PhotoUrl,
				Contact = participant.Contact,
				RankingId = participant.RankingId,
				Active = participant.Active,
				Blocked = participant.Blocked,
				AccountRepresentative = participant.AccountRepresentative,
				AuthenticationMfaSettings = participant.AuthenticationMfaSettings,
				Metadata = { },
                BlockingDetails = participant.BlockingDetails,
			};
		}

        private async Task ExecuteClientIntegration(Guid campaignId, User user)
        {
            var integrationSettings = await campaignRepository.GetIntegrationSettings(campaignId);
			if (integrationSettings.HasClientIntegration())
			{
				if (integrationSettings.IntegrationSettings.EnableChangeRegistrationData)
                {
                    var registrationData = PreRegisteredUser.OfUser(user, campaignId);
					try {
						LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Atualização Cadatral - Enviando para integração com cliente",
							campaignId, user.Id);
						await this.clientIntegrations.UpdateRegistrationData(campaignId, registrationData);
					} catch (Exception ex) {
						await ExceptionLogger.LogException(ex, "Atualização Cadatral - Client Integration", "Erro durante a integração com cliente");
					}
				}
			}
        }

        public async Task AddParticipantToGroups(Guid campaignId, User user, ParticipantManagementModel participant)
		{
			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Doc {2} - Inserindo participante nos grupos",
					campaignId, user.Id, participant.Cpf);
			try
			{
				var result = await this.participantsGroupService.AddParticipantToGroupIfNeed(campaignId, user.Id, participant.GroupsCodes);
				if (!result) {
					throw MotivaiException.ofValidation("PARTICIPANT_GROUPS_ERROR",
						"Não foi possível adicionar o participante no grupo.");
				}
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "ParticipantImporter - Grupos", "Erro ao atualizar o participante nos grupos", true);
			}
		}

        private async Task RegisterLog(ParticipantRegistrationLog participantRegistrationLog)
        {
            try
            {
                await participantRegistrationLogRepository.RegisterLog(participantRegistrationLog);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "RegistrationLog", "Erro ao registrar log de ação nos dados cadastrais pelo admin");
            }
        }
    }
}
