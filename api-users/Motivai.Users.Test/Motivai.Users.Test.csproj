<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
  <Content Include="appsettings.json" CopyToOutputDirectory="Always" />
  <Content Include="appsettings.Development.json" CopyToOutputDirectory="Always" />
</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.0.0-preview-20161123-03" />
    <PackageReference Include="xunit" Version="2.2.0-beta4-build3444" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.2.0-beta4-build1194" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Motivai.Users.App\Motivai.Users.App.csproj" />
    <ProjectReference Include="..\Motivai.Users.Domain\Motivai.Users.Domain.csproj" />
    <ProjectReference Include="..\Motivai.Users.Repository\Motivai.Users.Repository.csproj" />
  </ItemGroup>
</Project>
