using System.IO;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Users.Test.Utils
{
    public abstract class BaseTest
    {
        protected abstract void SetInstances();

        protected void ConfigEnvironment(EEnvironment env)
        {
            var directory = Directory.GetCurrentDirectory();
            ConfigurationHelper.InitConfiguration(directory, "Development");
            var config = ConfigurationHelper.GetConfig();
            var conn = config["MONGODB_URI"];

            SetInstances();
        }
    }
}