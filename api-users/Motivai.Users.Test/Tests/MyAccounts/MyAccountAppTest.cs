using Motivai.Usuarios.Domain.IApp.MyAccounts;
using Motivai.Usuarios.App;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.Usuarios.Repository.Repositorys.Users;
using Motivai.Usuarios.Repository.Repositorys.Transactions;
using Motivai.Usuarios.Repository.Repositorys.UserParticipantCampaigns;
using Motivai.Usuarios.Domain.Models.MyAccount;
using Motivai.Usuarios.Domain.Models.Adress;
using Motivai.Usuarios.Domain.IRepository.Users;
using System;
using Xunit;
using Microsoft.Extensions.Configuration;
using Motivai.SharedKernel.Helpers;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using Motivai.Users.Test.Utils;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.Users.Test.Tests.MyAccounts.Models;
using Motivai.Users.Repository.Repositorys.Orders;
using Motivai.Users.Domain.IRepository.Orders;
using System.Linq;

namespace Motivai.Users.Test.Tests.MyAccounts
{
    public class MyAccountAppTest : BaseTest
    {
        private IMyAccountApp _myAccountApp;
        private IUserRepository _userRepository;
        private IOrderRepository _orderRepository;
        protected override void SetInstances()
        {
            _userRepository = new UserRepository();
            _orderRepository = new OrderRepository();
            var userParticipantCampaignRepository = new UserParticipationCampaignRepository(_userRepository);
            var transactionRepository = new TransactionApiRepository();
            var crypt = new Criptografia();

            _myAccountApp = new MyAccountApp(_userRepository
            , userParticipantCampaignRepository
            , transactionRepository
            , crypt
            , _orderRepository);
        }

        [Fact]
        public async Task GetLastAccumulationsTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            Assert.IsNotType<Exception>(await _myAccountApp.GetLastAccumulations(userId,campaignId));
        }

        [Fact]
        public async Task GetLastAccumulationsNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetLastAccumulations(Guid.Empty,campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task GetLastAccumulationsNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetLastAccumulations(userId,Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetExtractTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            Assert.IsNotType<Exception>(await _myAccountApp.GetExtract(userId,campaignId));
        }

        [Fact]
        public async Task GetExtractNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetExtract(Guid.Empty,campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task GetExtractNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetExtract(userId,Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetAddressTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var addressId = new Guid("91c1ffb7-67d8-4491-ba0c-930ffddbafda");
            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            Assert.IsNotType<Exception>(await _myAccountApp.GetAdress(userId,campaignId,addressId));
        }

        [Fact]
        public async Task GetAddressNotInformAddressIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAsync<MotivaiException>(() => _myAccountApp.GetAdress(userId,campaignId, Guid.Empty));

            Assert.Equal("Deve ser informado o ID do Endereço.", ex.Message);
        }

        // [Fact]
        // public async Task GetAllAddressTest()
        // {
        //     ConfigEnvironment(EEnvironment.Development);

        //     var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
        //     var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

        //     var allAddressModel = new AdressesUserModel(userId, campaignId);
        //     Assert.IsNotType<Exception>(await _myAccountApp.GetAllAdress(allAddressModel));
        // }

        // [Fact]
        // public void GetAllAddressNotInformUserIdTest()
        // {
        //     ConfigEnvironment(EEnvironment.Development);

        //     var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

        //     var ex = Assert.Throws<Exception>(() => new AdressesUserModel(Guid.Empty, campaignId));

        //     Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        // }

        // [Fact]
        // public void GetAllAddressNotInformCampaignIdTest()
        // {
        //     ConfigEnvironment(EEnvironment.Development);

        //     var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

        //     var ex = Assert.Throws<Exception>(() => new AdressesUserModel(userId, Guid.Empty));

        //     Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        // }


        [Fact]
        public async Task RegisterAddressTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var addressViewModel = AddressViewModelTestHelper.GetAddressViewModel(userId);

            var addressSave = await _myAccountApp.RegisterAddress(campaignId,userId,addressViewModel);

            var addressId = addressSave.GetIdDecriptadaGuid();

            Assert.IsNotType<Exception>(await _myAccountApp.GetAdress(addressId));
        }

        [Fact]
        public async Task RegisterAddressNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var addressViewModel = AddressViewModelTestHelper.GetAddressViewModel(Guid.Empty);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.RegisterAddress(campaignId,Guid.Empty,addressViewModel));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task RegisterAddressNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var addressViewModel = AddressViewModelTestHelper.GetAddressViewModel(userId);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.RegisterAddress(Guid.Empty,userId,addressViewModel));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task SaveAddressNotInformAddressNameTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var addressViewModel = AddressViewModelTestHelper.GetAddressViewModel(userId);
            addressViewModel.Nome = string.Empty;

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.RegisterAddress(campaignId,userId,addressViewModel));

            Assert.Equal("Nome do endereço é obrigatório.", ex.Message);
        }

        [Fact]
        public async Task GetPrincipalContactTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            Assert.IsNotType<Exception>(await _myAccountApp.GetPrincipalContact(userId,campaignId));
        }

        [Fact]
        public async Task GetPrincipalContactNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetPrincipalContact(userId,Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetPrincipalContactNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetPrincipalContact(Guid.Empty,campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task GetPrincipalContactInvalidUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetPrincipalContact(userId,campaignId));

            Assert.Equal("Usuário não encontrado pelo ID informado. ID: " + userId, ex.Message);
        }

        [Fact]
        public async Task GetPrincipalContactInvalidCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetPrincipalContact(userId,campaignId));

            Assert.Equal("Usuário não relacionado com a Campanha informada. Campanha: " + campaignId, ex.Message);
        }

        [Fact]
        public async Task GetRegistrationDataTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var getContactModel = new GetContactModel(userId, campaignId);

            Assert.IsNotType<Exception>(await _myAccountApp.GetRegistrationData(getContactModel));
        }

        [Fact]
        public async Task GetRegistrationDataInvalidUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var getContactModel = new GetContactModel(userId, campaignId);

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetRegistrationData(getContactModel));

            Assert.Equal("Usuário não encontrado pelo ID informado. ID: " + userId, ex.Message);
        }

        [Fact]
        public async Task GetRegistrationDataInvalidCampaingIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var getContactModel = new GetContactModel(userId, campaignId);

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetRegistrationData(getContactModel));

            Assert.Equal("Usuário não relacionado com a Campanha informada. Campanha: " + campaignId, ex.Message);
        }

        [Fact]
        public async Task UpdateRegistrationDataTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);
            var cpf = HelperTest.GerarCpf();

            var userUpdate = UpdateDataRegistrationModelTestHelper.GetDataUpdate(user, cpf);

            var updateDataRegistrationModel = new UpdateDataRegistrationModel(userId, campaignId, userUpdate);

            var dataUpdate = await _myAccountApp.UpdateRegistrationData(updateDataRegistrationModel);

            user = await _userRepository.Get(user.Id);

            Assert.True(user.Cpf == cpf);
        }

        [Fact]
        public async Task UpdateRegistrationDataNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);
            var cpf = HelperTest.GerarCpf();

            var userUpdate = UpdateDataRegistrationModelTestHelper.GetDataUpdate(user, cpf);

            var ex = Assert.Throws<MotivaiException>(() => new UpdateDataRegistrationModel(Guid.Empty, campaignId, userUpdate));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task UpdateRegistrationDataNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);
            var cpf = HelperTest.GerarCpf();

            var userUpdate = UpdateDataRegistrationModelTestHelper.GetDataUpdate(user, cpf);

            var ex = Assert.Throws<MotivaiException>(() => new UpdateDataRegistrationModel(userId
            , Guid.Empty, userUpdate));

            Assert.Equal("Deve ser informado o ID do Campanha.", ex.Message);
        }

        [Fact]
        public void UpdateRegistrationDataNotInformUserUpdateTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = Assert.Throws<MotivaiException>(() => new UpdateDataRegistrationModel(userId
            , campaignId, null));

            Assert.Equal("Deve ser informado os Dados Cadastrais do Usuário para serem Atualizados.", ex.Message);
        }

        [Fact]
        public async Task UpdateRegistrationDataNotInformNameTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);
            var cpf = HelperTest.GerarCpf();

            var userUpdate = UpdateDataRegistrationModelTestHelper.GetDataUpdate(user, cpf);
            userUpdate.Name = string.Empty;

            var updateDataRegistrationModel = new UpdateDataRegistrationModel(userId, campaignId, userUpdate);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.UpdateRegistrationData(updateDataRegistrationModel));

            Assert.Equal("Deve ser informado o Nome do Usuário.", ex.Message);
        }

        [Fact]
        public async Task UpdateRegistrationDataNotInformCpfTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);
            var cpf = HelperTest.GerarCpf();

            var userUpdate = UpdateDataRegistrationModelTestHelper.GetDataUpdate(user, cpf);
            userUpdate.Cpf = string.Empty;

            var updateDataRegistrationModel = new UpdateDataRegistrationModel(userId, campaignId, userUpdate);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.UpdateRegistrationData(updateDataRegistrationModel));

            Assert.Equal("Deve ser informado o CPF do Usuário.", ex.Message);
        }

        [Fact]
        public async Task UpdateRegistrationDataInvalidCpfTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);
            var cpf = HelperTest.GerarCpf();

            var userUpdate = UpdateDataRegistrationModelTestHelper.GetDataUpdate(user, cpf);
            userUpdate.Cpf = "********";

            var updateDataRegistrationModel = new UpdateDataRegistrationModel(userId, campaignId, userUpdate);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.UpdateRegistrationData(updateDataRegistrationModel));

            Assert.Equal("CPF inválido.", ex.Message);
        }

        [Fact]
        public async Task DeleteAdressTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var addressViewModel = AddressViewModelTestHelper.GetAddressViewModel(userId);

            var saveAddressModel = new SaveAdressModel(addressViewModel, campaignId, userId);
            var addressSave = await _myAccountApp.SaveAdress(saveAddressModel);

            var addressId = addressSave.GetIdDecriptadaGuid();

            var address = await _myAccountApp.DeleteAdress(addressId);

            Assert.True(address);
        }

        [Fact]
        public async Task DeleteAdressNotInformAddressIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.DeleteAdress(Guid.Empty));

            Assert.Equal("Deve ser informado o ID do Endereço.", ex.Message);
        }


        [Fact]
        public async Task UpdateAdressTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var addressViewModel = AddressViewModelTestHelper.GetAddressViewModel(userId);

            var saveAddressModel = new SaveAdressModel(addressViewModel, campaignId, userId);
            var addressSave = await _myAccountApp.SaveAdress(saveAddressModel);

            Random rnd = new Random();
            int number = rnd.Next(1, 10);

            addressSave.Numero = number.ToString();

            var addressUpdate = await _myAccountApp.UpdateAdress(addressSave);

            Assert.True(addressUpdate.Numero == number.ToString());
        }

        [Fact]
        public async Task UpdateAdressNotInformAddressUpdateTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var ex = await Assert.ThrowsAnyAsync<Exception>(() => _myAccountApp.UpdateAdress(null));

            Assert.Equal("Deve ser informado o Endereço para ser inserido ou atualizado.", ex.Message);
        }

        [Fact]
        public async Task LoadSummaryTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var loadSummaryModel = new LoadSummaryModel(userId, campaignId);

            Assert.IsNotType<Exception>(await _myAccountApp.LoadSummary(loadSummaryModel));
        }

        [Fact]
        public void LoadSummaryNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = Assert.Throws<MotivaiException>(() => new LoadSummaryModel(Guid.Empty, campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public void LoadSummaryNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = Assert.Throws<MotivaiException>(() => new LoadSummaryModel(userId, Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task LoadSummaryInvalidDataPutTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var loadSummaryModel = new LoadSummaryModel(userId, campaignId);

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.LoadSummary(loadSummaryModel));

            Assert.Equal("Campanha não relacionada com o Usuário informado. CampanhaID: " + loadSummaryModel.CampaignId
                     + " UsuarioID: " + loadSummaryModel.UserId, ex.Message);
        }

        [Fact]
        public async Task GetLastRedemptionsTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var lastRedemptionsModel = new LastRedemptions(userId, campaignId);

            Assert.IsNotType<Exception>(await _myAccountApp.GetLastRedemptions(lastRedemptionsModel));
        }

        [Fact]
        public void GetLastRedemptionsNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = Assert.Throws<MotivaiException>(() => new LastRedemptions(Guid.Empty, campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public void GetLastRedemptionsNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = Assert.Throws<MotivaiException>(() => new LastRedemptions(userId, Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetPointsExpireTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var pointsExpireModel = new PointsExpireModel(userId, campaignId);

            Assert.IsNotType<Exception>(await _myAccountApp.GetPointsExpire(pointsExpireModel));
        }

        [Fact]
        public void GetPointsExpireNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = Assert.Throws<MotivaiException>(() => new PointsExpireModel(Guid.Empty, campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public void GetPointsExpireNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = Assert.Throws<MotivaiException>(() => new PointsExpireModel(userId, Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetPointsLockedTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var pointsLockedModel = new PointsLockedModel(userId, campaignId);

            Assert.IsNotType<Exception>(await _myAccountApp.GetPointsLocked(pointsLockedModel));
        }

        [Fact]
        public void GetPointsLockedNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = Assert.Throws<MotivaiException>(() => new PointsLockedModel(Guid.Empty, campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public void GetPointsLockedNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = Assert.Throws<MotivaiException>(() => new PointsLockedModel(userId, Guid.Empty));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetOrdersTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            Assert.IsNotType<Exception>(await _myAccountApp.GetOrders(userId, campaignId, string.Empty, null, null));
        }

        [Fact]
        public async Task GetOrdersTestNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrders(userId, Guid.Empty, string.Empty, null, null));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetOrdersTestNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrders(Guid.Empty, campaignId, string.Empty, null, null));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task GetOrdersTestInvalidUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrders(userId, campaignId, string.Empty, null, null));

            Assert.Equal("Usuário não encontrado pelo ID informado. ID: " + userId, ex.Message);
        }

        [Fact]
        public async Task GetOrdersTestInvalidCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrders(userId, campaignId, string.Empty, null, null));

            Assert.Equal("Usuário não participa da campanha informada. Campanha: " + campaignId, ex.Message);
        }

        [Fact]
        public async Task GetOrderTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var user = await _userRepository.Get(userId);

            if (user == null)
                throw MotivaiException.ofValidation("Usuário não encontrado pelo ID informado. ID: " + userId);

            var participant = user.UsersParticipantCampaign.FirstOrDefault(p => p.CampaignId == campaignId);

            if (participant == null)
                throw MotivaiException.ofValidation("Usuário não participa da campanha informada. Campanha: " + campaignId);

            var orders = await _orderRepository.GetOrdersByParticipant(participant.Id);

            if(orders == null ||  !orders.Any())
               Assert.True(true);

            var orderId = orders.Select(o=> o.Id).FirstOrDefault();

            Assert.IsNotType<Exception>(await _myAccountApp.GetOrder(userId, campaignId, orderId));
        }

        [Fact]
        public async Task GetOrderNotInformUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var campaignId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrder(Guid.Empty, campaignId, campaignId));

            Assert.Equal("Deve ser informado o ID do Usuário.", ex.Message);
        }

        [Fact]
        public async Task GetOrderNotInformCampaignIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrder(userId, Guid.Empty, userId));

            Assert.Equal("Deve ser informado o ID da Campanha.", ex.Message);
        }

        [Fact]
        public async Task GetOrderNotInformOrderIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrder(userId, campaignId, Guid.Empty));

            Assert.Equal("Deve ser informado o ID do Pedido Master.", ex.Message);
        }

        [Fact]
        public async Task GetOrderInvalidUserIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrder(userId, campaignId, userId));

            Assert.Equal("Usuário não participa da campanha", ex.Message);
        }

        [Fact]
        public async Task GetOrderInvalidCampaingIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

            var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrder(userId, campaignId, userId));

            Assert.Equal("Usuário não participa da campanha", ex.Message);
        }

        [Fact]
        public async Task GetOrderInvalidorderIdTest()
        {
            ConfigEnvironment(EEnvironment.Development);

             var userId = new Guid("fb5961c4-2ca3-42da-8090-9a8d90347b24");
            var campaignId = new Guid("124c66e8-0991-4388-aebe-23376b11caf4");

            var ex = await Assert.ThrowsAnyAsync<MotivaiException>(() => _myAccountApp.GetOrder(userId, campaignId, campaignId));

            Assert.Equal("Pedido não encontrado pelo ID informado.", ex.Message);
        }

        [Fact]
        public async Task RegisterFirstAccess()
        {
            var userId = new Guid("");
            var campaignId = new Guid("");
        }

    }
}