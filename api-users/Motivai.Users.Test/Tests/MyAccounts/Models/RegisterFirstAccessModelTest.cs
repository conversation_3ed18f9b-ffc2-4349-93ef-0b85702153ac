using System;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.Usuarios.Domain.Models.MyAccount;

namespace Motivai.Users.Test.Tests.MyAccounts.Models
{
    public class RegisterFirstAccessModelTest
    {
        public static RegisterFirstAccessModel Get(Guid userId, Guid campaignId)
        {
            return new RegisterFirstAccessModel()
            {

            };
        }

        public static Contact Get()
        {
            return new Contact()
            {

            };
        }
    }
}