using System;
using Motivai.Usuarios.Domain.Models.MyAccount;

namespace Motivai.Users.Test.Tests.MyAccounts.Models
{
    public class AddressViewModelTestHelper
    {
        public static EnderecoViewModel GetAddressViewModel(Guid userId)
        {
            var addressViewModel = new EnderecoViewModel()
            {
                Nome = "Casa Lucas",
                Cep = "********",
                Logradouro = "Avenida Brigadeiro Manoel Rodrigues Jordão 1501",
                Numero = "1501",
                Complemento = "Casa",
                Bairro = "<PERSON><PERSON><PERSON>",
                Cidade = "Barueri",
                Estado = "São Paulo",
                SiglaEstado = "SP",
                PontoReferencia = "Próximo ao Mercado Carajás",
                UsuarioId = userId,
                UsuarioParticipacaoCampanhaId = new Guid("eddf6c56-2357-4e0c-a53e-6696fe8a31cd")
            };

            addressViewModel.Destinatario = GetDestinatarioAddressViewModel();

            return addressViewModel;
        }

        public static DestinatarioViewModel GetDestinatarioAddressViewModel()
        {
            return new DestinatarioViewModel()
            {
                Nome = "Lucas",
                Cpf = "39703895859",
                Telefone = "1141941279",
                Celular = "11968395620",
                Email = "<EMAIL>"
            };
        }
    }
}