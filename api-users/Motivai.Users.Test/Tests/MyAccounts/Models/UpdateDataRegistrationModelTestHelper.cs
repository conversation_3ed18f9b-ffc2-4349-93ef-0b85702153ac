using System;
using Motivai.SharedKernel.Domain.Entities.References.Usuario;
using Motivai.Usuarios.Domain.Entities;

namespace Motivai.Users.Test.Tests.MyAccounts.Models
{
    public class UpdateDataRegistrationModelTestHelper
    {
        public static UsuarioDadosCadastroReference GetDataUpdate(User user, string cpf)
        {
            return new UsuarioDadosCadastroReference()
            {
                Id = user.Id,
                Name = user.Name,
                Rg = user.Rg,
                Gender = user.Gender,
                MaritalStatus = user.MaritalStatus,
                BirthDate = user.BirthDate.ToString(),
                GpInf = user.GpInf,
                PartnerGpInf = user.PartnerGpInf,
                Cpf = cpf,
                RazaoSocial = user.RazaoSocial,
                PhotoUrl = user.PhotoUrl
            };
        }
    }
}